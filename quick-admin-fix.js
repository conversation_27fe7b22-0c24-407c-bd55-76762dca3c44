// Quick admin user creation with timeout handling
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function createAdminQuickly() {
  try {
    console.log('🚀 Quick Admin User Creation...');
    
    // Try to register admin user through API (this bypasses direct DB access)
    const adminData = {
      firstName: 'Admin',
      lastName: 'User',
      username: 'admin',
      email: '<EMAIL>',
      password: 'admin123',
      school: 'Brainwave Admin',
      level: 'primary',
      class: '1',
      phoneNumber: '0700000000'
    };
    
    console.log('📝 Registering admin user through API...');
    
    try {
      const registerResponse = await axios.post(`${BASE_URL}/users/register`, adminData, {
        timeout: 15000 // 15 second timeout
      });
      
      if (registerResponse.data.success) {
        console.log('✅ Admin user registered successfully!');
        
        // Now try to login
        console.log('🔐 Testing admin login...');
        const loginResponse = await axios.post(`${BASE_URL}/users/login`, {
          email: '<EMAIL>',
          password: 'admin123'
        }, { timeout: 10000 });
        
        if (loginResponse.data.success) {
          console.log('✅ Admin login successful!');
          console.log('🎉 Admin user is ready to use!');
          console.log('📧 Email: <EMAIL>');
          console.log('🔑 Password: admin123');
          
          // Test a simple material upload
          console.log('\n🧪 Testing simple video upload...');
          const token = loginResponse.data.data;
          
          const testVideo = {
            className: '1',
            subject: 'Mathematics',
            title: 'Test Video - Quick Fix',
            level: 'primary',
            videoID: 'dQw4w9WgXcQ'
          };
          
          const uploadResponse = await axios.post(`${BASE_URL}/study/add-video`, testVideo, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            timeout: 20000 // 20 second timeout
          });
          
          if (uploadResponse.data.success) {
            console.log('🎉 VIDEO UPLOAD SUCCESSFUL!');
            console.log('✅ The admin panel should now work properly!');
          } else {
            console.log('⚠️ Video upload failed:', uploadResponse.data.message);
          }
          
        } else {
          console.log('❌ Admin login failed:', loginResponse.data.message);
        }
        
      } else {
        console.log('⚠️ Registration response:', registerResponse.data.message);
      }
      
    } catch (regError) {
      if (regError.response?.status === 409) {
        console.log('✅ Admin user already exists! Trying to login...');
        
        // Try to login with existing user
        const loginResponse = await axios.post(`${BASE_URL}/users/login`, {
          email: '<EMAIL>',
          password: 'admin123'
        }, { timeout: 10000 });
        
        if (loginResponse.data.success) {
          console.log('✅ Login successful with existing admin!');
          console.log('🎉 Admin user is ready to use!');
        } else {
          console.log('❌ Login failed with existing user');
        }
        
      } else {
        console.log('❌ Registration error:', regError.response?.data?.message || regError.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Quick admin creation failed:', error.message);
    
    if (error.code === 'ECONNABORTED') {
      console.log('⏰ Request timed out - server might be slow');
      console.log('💡 Try refreshing the admin panel and logging in with:');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: admin123');
    }
  }
}

createAdminQuickly();
