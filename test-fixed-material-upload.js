// Test the fixed material upload with timeout handling
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testFixedMaterialUpload() {
  try {
    console.log('🧪 Testing Fixed Material Upload System...');
    
    // Step 1: Login
    console.log('\n1️⃣ Logging in as admin...');
    const loginResponse = await axios.post(`${BASE_URL}/users/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    }, { timeout: 10000 });
    
    if (!loginResponse.data.success) {
      console.log('❌ Login failed:', loginResponse.data.message);
      return;
    }
    
    const token = loginResponse.data.data;
    console.log('✅ Login successful');
    
    // Step 2: Test video upload with timeout handling
    console.log('\n2️⃣ Testing video upload with improved timeout handling...');
    
    const testVideo = {
      className: '1',
      subject: 'Mathematics',
      title: `Fixed Test Video ${Date.now()}`,
      level: 'primary',
      videoID: 'dQw4w9WgXcQ'
    };
    
    console.log('📤 Uploading video...');
    const startTime = Date.now();
    
    try {
      const uploadResponse = await axios.post(`${BASE_URL}/study/add-video`, testVideo, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 20000 // 20 second timeout
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (uploadResponse.data.success) {
        console.log(`🎉 VIDEO UPLOAD SUCCESSFUL in ${duration}ms!`);
        console.log('📊 Response:', uploadResponse.data.message);
        console.log('🆔 Video ID:', uploadResponse.data.data._id);
        
        // Step 3: Verify the video was actually saved
        console.log('\n3️⃣ Verifying video was saved to database...');
        
        const verifyResponse = await axios.post(`${BASE_URL}/study/get-study-content`, {
          content: 'videos',
          className: 'all',
          subject: 'all'
        }, {
          headers: {
            'Authorization': `Bearer ${token}`
          },
          timeout: 10000
        });
        
        if (verifyResponse.data.success) {
          const videos = verifyResponse.data.data;
          const ourVideo = videos.find(v => v.title === testVideo.title);
          
          if (ourVideo) {
            console.log('✅ Video verified in database!');
            console.log('📋 Video details:', {
              id: ourVideo._id,
              title: ourVideo.title,
              subject: ourVideo.subject,
              level: ourVideo.level
            });
          } else {
            console.log('❌ Video not found in database');
          }
        } else {
          console.log('⚠️ Could not verify video in database');
        }
        
      } else {
        console.log('❌ Upload failed:', uploadResponse.data.message);
      }
      
    } catch (uploadError) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`❌ Upload failed after ${duration}ms`);
      
      if (uploadError.code === 'ECONNABORTED') {
        console.log('⏰ Request timed out on client side');
      } else if (uploadError.response) {
        console.log('📋 Server error:', uploadError.response.status);
        console.log('📋 Error message:', uploadError.response.data?.message);
      } else {
        console.log('📋 Network error:', uploadError.message);
      }
    }
    
    // Step 4: Test materials list endpoint
    console.log('\n4️⃣ Testing materials list endpoint...');
    
    try {
      const materialsResponse = await axios.get(`${BASE_URL}/study/admin/all-materials`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });
      
      if (materialsResponse.data.success) {
        console.log('✅ Materials list endpoint working');
        console.log('📊 Total materials:', materialsResponse.data.data.length);
      } else {
        console.log('⚠️ Materials list endpoint issue:', materialsResponse.data.message);
      }
      
    } catch (listError) {
      console.log('❌ Materials list failed:', listError.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testFixedMaterialUpload();
