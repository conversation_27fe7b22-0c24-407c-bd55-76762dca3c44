// Quick server verification after restart
const axios = require('axios');

async function quickCheck() {
  try {
    console.log('🔄 Quick Server Check After Restart...');
    
    // Check server health
    const health = await axios.get('http://localhost:5000/api/health', { timeout: 5000 });
    console.log('✅ Server Status:', health.data.status);
    console.log('📊 Port:', health.data.port);
    console.log('🌍 Environment:', health.data.environment);
    
    // Quick login test
    const login = await axios.post('http://localhost:5000/api/users/login', {
      email: '<EMAIL>',
      password: 'admin123'
    }, { timeout: 5000 });
    
    if (login.data.success) {
      console.log('✅ Admin login working');
      console.log('🎉 SERVER RESTART SUCCESSFUL!');
      console.log('');
      console.log('🚀 READY TO USE:');
      console.log('   - Server: http://localhost:5000');
      console.log('   - Client: http://localhost:3000');
      console.log('   - Admin Panel: http://localhost:3000/admin/login');
      console.log('   - All fixes applied and active');
    } else {
      console.log('❌ Login issue:', login.data.message);
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error.message);
  }
}

quickCheck();
