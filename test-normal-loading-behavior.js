// Test that normal loading behavior is restored
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testNormalLoadingBehavior() {
  try {
    console.log('🧪 Testing Normal Loading Behavior After Fix...');
    
    // Step 1: Login
    console.log('\n1️⃣ Logging in as admin...');
    const loginResponse = await axios.post(`${BASE_URL}/users/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    }, { timeout: 10000 });
    
    if (!loginResponse.data.success) {
      console.log('❌ Login failed:', loginResponse.data.message);
      return;
    }
    
    const token = loginResponse.data.data;
    console.log('✅ Login successful');
    
    // Step 2: Test video upload with timing
    console.log('\n2️⃣ Testing video upload with normal loading behavior...');
    
    const testVideo = {
      className: '1',
      subject: 'Mathematics',
      title: `Normal Loading Test ${Date.now()}`,
      level: 'primary',
      videoID: 'dQw4w9WgXcQ'
    };
    
    console.log('📤 Starting upload...');
    console.log('⏰ No artificial 3-second timeout should interfere');
    
    const startTime = Date.now();
    
    try {
      const uploadResponse = await axios.post(`${BASE_URL}/study/add-video`, testVideo, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 180000 // 3 minutes - normal timeout
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (uploadResponse.data.success) {
        console.log(`🎉 UPLOAD SUCCESSFUL in ${duration}ms!`);
        console.log('✅ Normal loading behavior working correctly');
        console.log('📊 Response:', uploadResponse.data.message);
        
        // Verify the material was actually saved
        console.log('\n3️⃣ Verifying material was actually saved...');
        
        const materialsResponse = await axios.get(`${BASE_URL}/study/admin/all-materials`, {
          headers: {
            'Authorization': `Bearer ${token}`
          },
          timeout: 10000
        });
        
        if (materialsResponse.data.success) {
          const materials = materialsResponse.data.data;
          const ourMaterial = materials.find(m => m.title === testVideo.title);
          
          if (ourMaterial) {
            console.log('✅ Material verified in database!');
            console.log('📋 Material ID:', ourMaterial._id);
            console.log('🎯 CONCLUSION: Normal loading behavior restored successfully!');
            console.log('');
            console.log('🎉 ADMIN PANEL READY FOR USE:');
            console.log('   - No more artificial timeouts');
            console.log('   - Loading states work correctly');
            console.log('   - Materials are actually saved');
            console.log('   - Proper success/error feedback');
          } else {
            console.log('❌ Material not found in database - there may still be an issue');
          }
        } else {
          console.log('⚠️ Could not verify materials list');
        }
        
      } else {
        console.log('❌ Upload failed:', uploadResponse.data.message);
      }
      
    } catch (uploadError) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`❌ Upload failed after ${duration}ms`);
      console.log('📋 Error:', uploadError.message);
      
      if (uploadError.code === 'ECONNABORTED') {
        console.log('⏰ This is a real timeout, not an artificial one');
        console.log('💡 The server may still have database connectivity issues');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testNormalLoadingBehavior();
