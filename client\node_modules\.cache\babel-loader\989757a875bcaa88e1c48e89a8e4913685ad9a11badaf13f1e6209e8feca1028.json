{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Forum\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport './index.css';\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message, Button, Input, Form, Avatar, Badge, Tag } from \"antd\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\nimport image from '../../../assets/person.png';\nimport { PlusOutlined, MessageOutlined, EyeOutlined, UserOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Forum = () => {\n  _s();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [userData, setUserData] = useState('');\n  const [questions, setQuestions] = useState([]);\n  const [expandedReplies, setExpandedReplies] = useState({});\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const [replyRefs, setReplyRefs] = useState({});\n\n  // Pagination states\n  const [currentPage, setCurrentPage] = useState(1);\n  const [questionsPerPage] = useState(5);\n  const [totalQuestions, setTotalQuestions] = useState(0);\n  const fetchQuestions = async (page = currentPage) => {\n    try {\n      const response = await getAllQuestions({\n        page,\n        limit: questionsPerPage\n      });\n      if (response.success) {\n        setQuestions(response.data.reverse());\n        setTotalQuestions(response.total || response.data.length);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchQuestions();\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n    dispatch(HideLoading());\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      dispatch(ShowLoading());\n      getUserData();\n    }\n  }, []);\n  const toggleReplies = questionId => {\n    setExpandedReplies(prevExpandedReplies => ({\n      ...prevExpandedReplies,\n      [questionId]: !prevExpandedReplies[questionId]\n    }));\n  };\n  const handleAskQuestion = async values => {\n    try {\n      const response = await addQuestion(values);\n      if (response.success) {\n        message.success(response.message);\n        setAskQuestionVisible(false);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleReply = questionId => {\n    setReplyQuestionId(questionId);\n  };\n  const handleReplySubmit = async values => {\n    try {\n      const payload = {\n        questionId: replyQuestionId,\n        text: values.text\n      };\n      const response = await addReply(payload);\n      if (response.success) {\n        message.success(response.message);\n        setReplyQuestionId(null);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\n      setReplyRefs(prevRefs => ({\n        ...prevRefs,\n        [replyQuestionId]: /*#__PURE__*/React.createRef()\n      }));\n    }\n  }, [replyQuestionId, replyRefs]);\n  useEffect(() => {\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\n      replyRefs[replyQuestionId].current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [replyQuestionId, replyRefs]);\n\n  // Pagination calculations\n  const indexOfLastQuestion = currentPage * questionsPerPage;\n  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;\n  const currentQuestions = questions.slice(indexOfFirstQuestion, indexOfLastQuestion);\n  const totalPages = Math.ceil(questions.length / questionsPerPage);\n  const handlePageChange = page => {\n    setCurrentPage(page);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modern-forum\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"forum-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"forum-header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"forum-title\",\n            children: \"Community Forum\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"forum-description\",\n            children: \"Connect with fellow learners, ask questions, and share knowledge. Join our vibrant community discussion!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-ask-btn\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 35\n          }, this),\n          onClick: () => setAskQuestionVisible(true),\n          className: \"ask-question-floating\",\n          children: \"Ask Question\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 21\n      }, this), askQuestionVisible && /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        onFinish: handleAskQuestion,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"title\",\n          label: \"Title\",\n          rules: [{\n            required: true,\n            message: 'Please enter the title'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            style: {\n              padding: '18px 12px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"body\",\n          label: \"Body\",\n          rules: [{\n            required: true,\n            message: 'Please enter the body'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            children: \"Ask Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setAskQuestionVisible(false),\n            style: {\n              marginLeft: 10\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 25\n      }, this), questions.map(question => {\n        var _question$user, _question$user2, _question$replies;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"forum-question-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"question\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-row\",\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                src: (_question$user = question.user) !== null && _question$user !== void 0 && _question$user.profileImage ? question.user.profileImage : image,\n                alt: \"profile\",\n                size: 50\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: ((_question$user2 = question.user) === null || _question$user2 === void 0 ? void 0 : _question$user2.name) || 'Anonymous'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"title\",\n              children: question.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"body\",\n              children: question.body\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => toggleReplies(question._id),\n              children: expandedReplies[question._id] ? \"Collapse Replies\" : \"Expand Replies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => handleReply(question._id),\n              children: \"Reply\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 29\n          }, this), expandedReplies[question._id] && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"replies\",\n            children: ((_question$replies = question.replies) === null || _question$replies === void 0 ? void 0 : _question$replies.map(reply => {\n              var _reply$user, _reply$user2;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reply\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"profile-row\",\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    src: (_reply$user = reply.user) !== null && _reply$user !== void 0 && _reply$user.profileImage ? reply.user.profileImage : image,\n                    alt: \"profile\",\n                    size: 50\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 206,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: ((_reply$user2 = reply.user) === null || _reply$user2 === void 0 ? void 0 : _reply$user2.name) || 'Anonymous'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text\",\n                  children: reply.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 45\n                }, this)]\n              }, reply._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 41\n              }, this);\n            })) || /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No replies yet.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: replyRefs[question._id],\n            children: replyQuestionId === question._id && /*#__PURE__*/_jsxDEV(Form, {\n              form: form,\n              onFinish: handleReplySubmit,\n              layout: \"vertical\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"text\",\n                label: \"Your Reply\",\n                rules: [{\n                  required: true,\n                  message: 'Please enter your reply'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                  rows: 4\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  children: \"Submit Reply\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: () => setReplyQuestionId(null),\n                  style: {\n                    marginLeft: 10\n                  },\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 29\n          }, this)]\n        }, question._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 25\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 9\n  }, this);\n};\n_s(Forum, \"Q3mjrzM5QCjbgkH5fGALxIqRNSI=\", false, function () {\n  return [Form.useForm, useDispatch];\n});\n_c = Forum;\nexport default Forum;\nvar _c;\n$RefreshReg$(_c, \"Forum\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getUserInfo", "message", "<PERSON><PERSON>", "Input", "Form", "Avatar", "Badge", "Tag", "Page<PERSON><PERSON>le", "useDispatch", "HideLoading", "ShowLoading", "addQuestion", "addReply", "getAllQuestions", "image", "PlusOutlined", "MessageOutlined", "EyeOutlined", "UserOutlined", "jsxDEV", "_jsxDEV", "Forum", "_s", "isAdmin", "setIsAdmin", "userData", "setUserData", "questions", "setQuestions", "expandedReplies", "setExpandedReplies", "askQuestionVisible", "setAskQuestionVisible", "replyQuestionId", "setReplyQuestionId", "form", "useForm", "dispatch", "replyRefs", "setReplyRefs", "currentPage", "setCurrentPage", "questionsPerPage", "totalQuestions", "setTotalQuestions", "fetchQuestions", "page", "response", "limit", "success", "data", "reverse", "total", "length", "error", "getUserData", "localStorage", "getItem", "toggleReplies", "questionId", "prevExpandedReplies", "handleAskQuestion", "values", "resetFields", "handleReply", "handleReplySubmit", "payload", "text", "prevRefs", "createRef", "current", "scrollIntoView", "behavior", "indexOfLastQuestion", "indexOfFirstQuestion", "currentQuestions", "slice", "totalPages", "Math", "ceil", "handlePageChange", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "icon", "onClick", "onFinish", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "style", "padding", "TextArea", "htmlType", "marginLeft", "map", "question", "_question$user", "_question$user2", "_question$replies", "src", "user", "profileImage", "alt", "size", "title", "body", "_id", "replies", "reply", "_reply$user", "_reply$user2", "ref", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport './index.css';\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Badge, Tag } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\r\nimport image from '../../../assets/person.png';\r\nimport { PlusOutlined, MessageOutlined, EyeOutlined, UserOutlined } from '@ant-design/icons';\r\n\r\nconst Forum = () => {\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [userData, setUserData] = useState('');\r\n    const [questions, setQuestions] = useState([]);\r\n    const [expandedReplies, setExpandedReplies] = useState({});\r\n    const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n    const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n    const [form] = Form.useForm();\r\n    const dispatch = useDispatch();\r\n    const [replyRefs, setReplyRefs] = useState({});\r\n\r\n    // Pagination states\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [questionsPerPage] = useState(5);\r\n    const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n    const fetchQuestions = async (page = currentPage) => {\r\n        try {\r\n            const response = await getAllQuestions({ page, limit: questionsPerPage });\r\n            if (response.success) {\r\n                setQuestions(response.data.reverse());\r\n                setTotalQuestions(response.total || response.data.length);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchQuestions();\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n        dispatch(HideLoading());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const toggleReplies = (questionId) => {\r\n        setExpandedReplies((prevExpandedReplies) => ({\r\n            ...prevExpandedReplies,\r\n            [questionId]: !prevExpandedReplies[questionId],\r\n        }));\r\n    };\r\n\r\n    const handleAskQuestion = async (values) => {\r\n        try {\r\n            const response = await addQuestion(values);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setAskQuestionVisible(false);\r\n                form.resetFields();\r\n                await fetchQuestions();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    const handleReply = (questionId) => {\r\n        setReplyQuestionId(questionId);\r\n    };\r\n\r\n    const handleReplySubmit = async (values) => {\r\n        try {\r\n            const payload = {\r\n                questionId: replyQuestionId,\r\n                text: values.text\r\n            };\r\n            const response = await addReply(payload);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setReplyQuestionId(null);\r\n                form.resetFields();\r\n                await fetchQuestions();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n            setReplyRefs((prevRefs) => ({\r\n                ...prevRefs,\r\n                [replyQuestionId]: React.createRef(),\r\n            }));\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n            replyRefs[replyQuestionId].current.scrollIntoView({ behavior: 'smooth' });\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    // Pagination calculations\r\n    const indexOfLastQuestion = currentPage * questionsPerPage;\r\n    const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;\r\n    const currentQuestions = questions.slice(indexOfFirstQuestion, indexOfLastQuestion);\r\n    const totalPages = Math.ceil(questions.length / questionsPerPage);\r\n\r\n    const handlePageChange = (page) => {\r\n        setCurrentPage(page);\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            {!isAdmin && (\r\n                <div className=\"modern-forum\">\r\n                    {/* Header Section */}\r\n                    <div className=\"forum-header\">\r\n                        <div className=\"forum-header-content\">\r\n                            <h1 className=\"forum-title\">Community Forum</h1>\r\n                            <p className=\"forum-description\">\r\n                                Connect with fellow learners, ask questions, and share knowledge.\r\n                                Join our vibrant community discussion!\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Floating Ask Question Button */}\r\n                    <div className=\"floating-ask-btn\">\r\n                        <Button\r\n                            type=\"primary\"\r\n                            icon={<PlusOutlined />}\r\n                            onClick={() => setAskQuestionVisible(true)}\r\n                            className=\"ask-question-floating\"\r\n                        >\r\n                            Ask Question\r\n                        </Button>\r\n                    </div>\r\n\r\n                    {askQuestionVisible && (\r\n                        <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\">\r\n                            <Form.Item name=\"title\" label=\"Title\" rules={[{ required: true, message: 'Please enter the title' }]}>\r\n                                <Input style={{ padding: '18px 12px' }} />\r\n                            </Form.Item>\r\n                            <Form.Item name=\"body\" label=\"Body\" rules={[{ required: true, message: 'Please enter the body' }]}>\r\n                                <Input.TextArea />\r\n                            </Form.Item>\r\n                            <Form.Item>\r\n                                <Button type=\"primary\" htmlType=\"submit\">\r\n                                    Ask Question\r\n                                </Button>\r\n                                <Button onClick={() => setAskQuestionVisible(false)} style={{ marginLeft: 10 }}>\r\n                                    Cancel\r\n                                </Button>\r\n                            </Form.Item>\r\n                        </Form>\r\n                    )}\r\n\r\n                    {questions.map((question) => (\r\n                        <div key={question._id} className=\"forum-question-container\">\r\n                            <div className=\"question\">\r\n                                <div className=\"profile-row\">\r\n                                    <Avatar src={question.user?.profileImage ? question.user.profileImage : image} alt=\"profile\" size={50} />\r\n                                    <p>{question.user?.name || 'Anonymous'}</p>\r\n                                </div>\r\n                                <div className=\"title\">{question.title}</div>\r\n                                <div className=\"body\">{question.body}</div>\r\n                                <Button onClick={() => toggleReplies(question._id)}>\r\n                                    {expandedReplies[question._id] ? \"Collapse Replies\" : \"Expand Replies\"}\r\n                                </Button>\r\n                                <Button onClick={() => handleReply(question._id)}>Reply</Button>\r\n                            </div>\r\n                            {expandedReplies[question._id] && (\r\n                                <div className=\"replies\">\r\n                                    {question.replies?.map((reply) => (\r\n                                        <div key={reply._id} className=\"reply\">\r\n                                            <div className=\"profile-row\">\r\n                                                <Avatar src={reply.user?.profileImage ? reply.user.profileImage : image} alt=\"profile\" size={50} />\r\n                                                <p>{reply.user?.name || 'Anonymous'}</p>\r\n                                            </div>\r\n                                            <div className=\"text\">{reply.text}</div>\r\n                                        </div>\r\n                                    )) || <p>No replies yet.</p>}\r\n                                </div>\r\n                            )}\r\n                            <div ref={replyRefs[question._id]}>\r\n                                {replyQuestionId === question._id && (\r\n                                    <Form form={form} onFinish={handleReplySubmit} layout=\"vertical\">\r\n                                        <Form.Item name=\"text\" label=\"Your Reply\" rules={[{ required: true, message: 'Please enter your reply' }]}>\r\n                                            <Input.TextArea rows={4} />\r\n                                        </Form.Item>\r\n                                        <Form.Item>\r\n                                            <Button type=\"primary\" htmlType=\"submit\">\r\n                                                Submit Reply\r\n                                            </Button>\r\n                                            <Button onClick={() => setReplyQuestionId(null)} style={{ marginLeft: 10 }}>\r\n                                                Cancel\r\n                                            </Button>\r\n                                        </Form.Item>\r\n                                    </Form>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n                    ))}\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Forum;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,QAAQ,MAAM;AACvE,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,WAAW,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,yBAAyB;AAChF,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,SAASC,YAAY,EAAEC,eAAe,EAAEC,WAAW,EAAEC,YAAY,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7F,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACmC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuC,IAAI,CAAC,GAAGhC,IAAI,CAACiC,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC8C,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EACtC,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAMiD,cAAc,GAAG,MAAAA,CAAOC,IAAI,GAAGN,WAAW,KAAK;IACjD,IAAI;MACA,MAAMO,QAAQ,GAAG,MAAMlC,eAAe,CAAC;QAAEiC,IAAI;QAAEE,KAAK,EAAEN;MAAiB,CAAC,CAAC;MACzE,IAAIK,QAAQ,CAACE,OAAO,EAAE;QAClBrB,YAAY,CAACmB,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;QACrCP,iBAAiB,CAACG,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACG,IAAI,CAACG,MAAM,CAAC;MAC7D,CAAC,MAAM;QACHrD,OAAO,CAACsD,KAAK,CAACP,QAAQ,CAAC/C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACZtD,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;IAChC;EACJ,CAAC;EAED,MAAMuD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMR,QAAQ,GAAG,MAAMhD,WAAW,CAAC,CAAC;MACpC,IAAIgD,QAAQ,CAACE,OAAO,EAAE;QAClB,IAAIF,QAAQ,CAACG,IAAI,CAAC3B,OAAO,EAAE;UACvBC,UAAU,CAAC,IAAI,CAAC;QACpB,CAAC,MAAM;UACHA,UAAU,CAAC,KAAK,CAAC;UACjBE,WAAW,CAACqB,QAAQ,CAACG,IAAI,CAAC;UAC1B,MAAML,cAAc,CAAC,CAAC;QAC1B;MACJ,CAAC,MAAM;QACH7C,OAAO,CAACsD,KAAK,CAACP,QAAQ,CAAC/C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACZtD,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;IAChC;IACAqC,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;EAC3B,CAAC;EAEDZ,SAAS,CAAC,MAAM;IACZ,IAAI2D,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MAC/BpB,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;MACvB6C,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAIC,UAAU,IAAK;IAClC7B,kBAAkB,CAAE8B,mBAAmB,KAAM;MACzC,GAAGA,mBAAmB;MACtB,CAACD,UAAU,GAAG,CAACC,mBAAmB,CAACD,UAAU;IACjD,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IACxC,IAAI;MACA,MAAMf,QAAQ,GAAG,MAAMpC,WAAW,CAACmD,MAAM,CAAC;MAC1C,IAAIf,QAAQ,CAACE,OAAO,EAAE;QAClBjD,OAAO,CAACiD,OAAO,CAACF,QAAQ,CAAC/C,OAAO,CAAC;QACjCgC,qBAAqB,CAAC,KAAK,CAAC;QAC5BG,IAAI,CAAC4B,WAAW,CAAC,CAAC;QAClB,MAAMlB,cAAc,CAAC,CAAC;MAC1B,CAAC,MAAM;QACH7C,OAAO,CAACsD,KAAK,CAACP,QAAQ,CAAC/C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACZtD,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;IAChC;EACJ,CAAC;EAED,MAAMgE,WAAW,GAAIL,UAAU,IAAK;IAChCzB,kBAAkB,CAACyB,UAAU,CAAC;EAClC,CAAC;EAED,MAAMM,iBAAiB,GAAG,MAAOH,MAAM,IAAK;IACxC,IAAI;MACA,MAAMI,OAAO,GAAG;QACZP,UAAU,EAAE1B,eAAe;QAC3BkC,IAAI,EAAEL,MAAM,CAACK;MACjB,CAAC;MACD,MAAMpB,QAAQ,GAAG,MAAMnC,QAAQ,CAACsD,OAAO,CAAC;MACxC,IAAInB,QAAQ,CAACE,OAAO,EAAE;QAClBjD,OAAO,CAACiD,OAAO,CAACF,QAAQ,CAAC/C,OAAO,CAAC;QACjCkC,kBAAkB,CAAC,IAAI,CAAC;QACxBC,IAAI,CAAC4B,WAAW,CAAC,CAAC;QAClB,MAAMlB,cAAc,CAAC,CAAC;MAC1B,CAAC,MAAM;QACH7C,OAAO,CAACsD,KAAK,CAACP,QAAQ,CAAC/C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACZtD,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDH,SAAS,CAAC,MAAM;IACZ,IAAIoC,eAAe,IAAI,CAACK,SAAS,CAACL,eAAe,CAAC,EAAE;MAChDM,YAAY,CAAE6B,QAAQ,KAAM;QACxB,GAAGA,QAAQ;QACX,CAACnC,eAAe,gBAAGtC,KAAK,CAAC0E,SAAS,CAAC;MACvC,CAAC,CAAC,CAAC;IACP;EACJ,CAAC,EAAE,CAACpC,eAAe,EAAEK,SAAS,CAAC,CAAC;EAEhCzC,SAAS,CAAC,MAAM;IACZ,IAAIoC,eAAe,IAAIK,SAAS,CAACL,eAAe,CAAC,EAAE;MAC/CK,SAAS,CAACL,eAAe,CAAC,CAACqC,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC7E;EACJ,CAAC,EAAE,CAACvC,eAAe,EAAEK,SAAS,CAAC,CAAC;;EAEhC;EACA,MAAMmC,mBAAmB,GAAGjC,WAAW,GAAGE,gBAAgB;EAC1D,MAAMgC,oBAAoB,GAAGD,mBAAmB,GAAG/B,gBAAgB;EACnE,MAAMiC,gBAAgB,GAAGhD,SAAS,CAACiD,KAAK,CAACF,oBAAoB,EAAED,mBAAmB,CAAC;EACnF,MAAMI,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACpD,SAAS,CAAC0B,MAAM,GAAGX,gBAAgB,CAAC;EAEjE,MAAMsC,gBAAgB,GAAIlC,IAAI,IAAK;IAC/BL,cAAc,CAACK,IAAI,CAAC;EACxB,CAAC;EAED,oBACI1B,OAAA;IAAA6D,QAAA,EACK,CAAC1D,OAAO,iBACLH,OAAA;MAAK8D,SAAS,EAAC,cAAc;MAAAD,QAAA,gBAEzB7D,OAAA;QAAK8D,SAAS,EAAC,cAAc;QAAAD,QAAA,eACzB7D,OAAA;UAAK8D,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjC7D,OAAA;YAAI8D,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDlE,OAAA;YAAG8D,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAGjC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNlE,OAAA;QAAK8D,SAAS,EAAC,kBAAkB;QAAAD,QAAA,eAC7B7D,OAAA,CAACnB,MAAM;UACHsF,IAAI,EAAC,SAAS;UACdC,IAAI,eAAEpE,OAAA,CAACL,YAAY;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBG,OAAO,EAAEA,CAAA,KAAMzD,qBAAqB,CAAC,IAAI,CAAE;UAC3CkD,SAAS,EAAC,uBAAuB;UAAAD,QAAA,EACpC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,EAELvD,kBAAkB,iBACfX,OAAA,CAACjB,IAAI;QAACgC,IAAI,EAAEA,IAAK;QAACuD,QAAQ,EAAE7B,iBAAkB;QAAC8B,MAAM,EAAC,UAAU;QAAAV,QAAA,gBAC5D7D,OAAA,CAACjB,IAAI,CAACyF,IAAI;UAACC,IAAI,EAAC,OAAO;UAACC,KAAK,EAAC,OAAO;UAACC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEhG,OAAO,EAAE;UAAyB,CAAC,CAAE;UAAAiF,QAAA,eACjG7D,OAAA,CAAClB,KAAK;YAAC+F,KAAK,EAAE;cAAEC,OAAO,EAAE;YAAY;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACZlE,OAAA,CAACjB,IAAI,CAACyF,IAAI;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC,MAAM;UAACC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEhG,OAAO,EAAE;UAAwB,CAAC,CAAE;UAAAiF,QAAA,eAC9F7D,OAAA,CAAClB,KAAK,CAACiG,QAAQ;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACZlE,OAAA,CAACjB,IAAI,CAACyF,IAAI;UAAAX,QAAA,gBACN7D,OAAA,CAACnB,MAAM;YAACsF,IAAI,EAAC,SAAS;YAACa,QAAQ,EAAC,QAAQ;YAAAnB,QAAA,EAAC;UAEzC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlE,OAAA,CAACnB,MAAM;YAACwF,OAAO,EAAEA,CAAA,KAAMzD,qBAAqB,CAAC,KAAK,CAAE;YAACiE,KAAK,EAAE;cAAEI,UAAU,EAAE;YAAG,CAAE;YAAApB,QAAA,EAAC;UAEhF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACT,EAEA3D,SAAS,CAAC2E,GAAG,CAAEC,QAAQ;QAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,iBAAA;QAAA,oBACpBtF,OAAA;UAAwB8D,SAAS,EAAC,0BAA0B;UAAAD,QAAA,gBACxD7D,OAAA;YAAK8D,SAAS,EAAC,UAAU;YAAAD,QAAA,gBACrB7D,OAAA;cAAK8D,SAAS,EAAC,aAAa;cAAAD,QAAA,gBACxB7D,OAAA,CAAChB,MAAM;gBAACuG,GAAG,EAAE,CAAAH,cAAA,GAAAD,QAAQ,CAACK,IAAI,cAAAJ,cAAA,eAAbA,cAAA,CAAeK,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACC,YAAY,GAAG/F,KAAM;gBAACgG,GAAG,EAAC,SAAS;gBAACC,IAAI,EAAE;cAAG;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzGlE,OAAA;gBAAA6D,QAAA,EAAI,EAAAwB,eAAA,GAAAF,QAAQ,CAACK,IAAI,cAAAH,eAAA,uBAAbA,eAAA,CAAeZ,IAAI,KAAI;cAAW;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNlE,OAAA;cAAK8D,SAAS,EAAC,OAAO;cAAAD,QAAA,EAAEsB,QAAQ,CAACS;YAAK;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7ClE,OAAA;cAAK8D,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAEsB,QAAQ,CAACU;YAAI;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3ClE,OAAA,CAACnB,MAAM;cAACwF,OAAO,EAAEA,CAAA,KAAM/B,aAAa,CAAC6C,QAAQ,CAACW,GAAG,CAAE;cAAAjC,QAAA,EAC9CpD,eAAe,CAAC0E,QAAQ,CAACW,GAAG,CAAC,GAAG,kBAAkB,GAAG;YAAgB;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACTlE,OAAA,CAACnB,MAAM;cAACwF,OAAO,EAAEA,CAAA,KAAMzB,WAAW,CAACuC,QAAQ,CAACW,GAAG,CAAE;cAAAjC,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,EACLzD,eAAe,CAAC0E,QAAQ,CAACW,GAAG,CAAC,iBAC1B9F,OAAA;YAAK8D,SAAS,EAAC,SAAS;YAAAD,QAAA,EACnB,EAAAyB,iBAAA,GAAAH,QAAQ,CAACY,OAAO,cAAAT,iBAAA,uBAAhBA,iBAAA,CAAkBJ,GAAG,CAAEc,KAAK;cAAA,IAAAC,WAAA,EAAAC,YAAA;cAAA,oBACzBlG,OAAA;gBAAqB8D,SAAS,EAAC,OAAO;gBAAAD,QAAA,gBAClC7D,OAAA;kBAAK8D,SAAS,EAAC,aAAa;kBAAAD,QAAA,gBACxB7D,OAAA,CAAChB,MAAM;oBAACuG,GAAG,EAAE,CAAAU,WAAA,GAAAD,KAAK,CAACR,IAAI,cAAAS,WAAA,eAAVA,WAAA,CAAYR,YAAY,GAAGO,KAAK,CAACR,IAAI,CAACC,YAAY,GAAG/F,KAAM;oBAACgG,GAAG,EAAC,SAAS;oBAACC,IAAI,EAAE;kBAAG;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnGlE,OAAA;oBAAA6D,QAAA,EAAI,EAAAqC,YAAA,GAAAF,KAAK,CAACR,IAAI,cAAAU,YAAA,uBAAVA,YAAA,CAAYzB,IAAI,KAAI;kBAAW;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACNlE,OAAA;kBAAK8D,SAAS,EAAC,MAAM;kBAAAD,QAAA,EAAEmC,KAAK,CAACjD;gBAAI;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GALlC8B,KAAK,CAACF,GAAG;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMd,CAAC;YAAA,CACT,CAAC,kBAAIlE,OAAA;cAAA6D,QAAA,EAAG;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CACR,eACDlE,OAAA;YAAKmG,GAAG,EAAEjF,SAAS,CAACiE,QAAQ,CAACW,GAAG,CAAE;YAAAjC,QAAA,EAC7BhD,eAAe,KAAKsE,QAAQ,CAACW,GAAG,iBAC7B9F,OAAA,CAACjB,IAAI;cAACgC,IAAI,EAAEA,IAAK;cAACuD,QAAQ,EAAEzB,iBAAkB;cAAC0B,MAAM,EAAC,UAAU;cAAAV,QAAA,gBAC5D7D,OAAA,CAACjB,IAAI,CAACyF,IAAI;gBAACC,IAAI,EAAC,MAAM;gBAACC,KAAK,EAAC,YAAY;gBAACC,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAEhG,OAAO,EAAE;gBAA0B,CAAC,CAAE;gBAAAiF,QAAA,eACtG7D,OAAA,CAAClB,KAAK,CAACiG,QAAQ;kBAACqB,IAAI,EAAE;gBAAE;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACZlE,OAAA,CAACjB,IAAI,CAACyF,IAAI;gBAAAX,QAAA,gBACN7D,OAAA,CAACnB,MAAM;kBAACsF,IAAI,EAAC,SAAS;kBAACa,QAAQ,EAAC,QAAQ;kBAAAnB,QAAA,EAAC;gBAEzC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlE,OAAA,CAACnB,MAAM;kBAACwF,OAAO,EAAEA,CAAA,KAAMvD,kBAAkB,CAAC,IAAI,CAAE;kBAAC+D,KAAK,EAAE;oBAAEI,UAAU,EAAE;kBAAG,CAAE;kBAAApB,QAAA,EAAC;gBAE5E;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA,GA1CAiB,QAAQ,CAACW,GAAG;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2CjB,CAAC;MAAA,CACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAhE,EAAA,CAjOKD,KAAK;EAAA,QAOQlB,IAAI,CAACiC,OAAO,EACV5B,WAAW;AAAA;AAAAiH,EAAA,GAR1BpG,KAAK;AAmOX,eAAeA,KAAK;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}