{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Forum\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport './index.css';\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message, Button, Input, Form, Avatar, Badge, Tag } from \"antd\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\nimport image from '../../../assets/person.png';\nimport { PlusOutlined, MessageOutlined, EyeOutlined, UserOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Forum = () => {\n  _s();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [userData, setUserData] = useState('');\n  const [questions, setQuestions] = useState([]);\n  const [expandedReplies, setExpandedReplies] = useState({});\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const [replyRefs, setReplyRefs] = useState({});\n\n  // Pagination states\n  const [currentPage, setCurrentPage] = useState(1);\n  const [questionsPerPage] = useState(5);\n  const [totalQuestions, setTotalQuestions] = useState(0);\n  const fetchQuestions = async (page = currentPage) => {\n    try {\n      const response = await getAllQuestions({\n        page,\n        limit: questionsPerPage\n      });\n      if (response.success) {\n        // Sort by creation date (newest first) instead of reversing\n        const sortedQuestions = response.data.sort((a, b) => new Date(b.createdAt || b._id) - new Date(a.createdAt || a._id));\n        setQuestions(sortedQuestions);\n        setTotalQuestions(response.total || response.data.length);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchQuestions();\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n    dispatch(HideLoading());\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      dispatch(ShowLoading());\n      getUserData();\n    }\n  }, []);\n  const toggleReplies = questionId => {\n    setExpandedReplies(prevExpandedReplies => ({\n      ...prevExpandedReplies,\n      [questionId]: !prevExpandedReplies[questionId]\n    }));\n  };\n  const handleAskQuestion = async values => {\n    try {\n      const response = await addQuestion(values);\n      if (response.success) {\n        message.success(response.message);\n        setAskQuestionVisible(false);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleReply = questionId => {\n    setReplyQuestionId(questionId);\n  };\n  const handleReplySubmit = async values => {\n    try {\n      const payload = {\n        questionId: replyQuestionId,\n        text: values.text\n      };\n      const response = await addReply(payload);\n      if (response.success) {\n        message.success(response.message);\n        setReplyQuestionId(null);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\n      setReplyRefs(prevRefs => ({\n        ...prevRefs,\n        [replyQuestionId]: /*#__PURE__*/React.createRef()\n      }));\n    }\n  }, [replyQuestionId, replyRefs]);\n  useEffect(() => {\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\n      replyRefs[replyQuestionId].current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [replyQuestionId, replyRefs]);\n\n  // Pagination calculations\n  const indexOfLastQuestion = currentPage * questionsPerPage;\n  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;\n  const currentQuestions = questions.slice(indexOfFirstQuestion, indexOfLastQuestion);\n  const totalPages = Math.ceil(questions.length / questionsPerPage);\n  const handlePageChange = page => {\n    setCurrentPage(page);\n    fetchQuestions(page);\n  };\n\n  // Format date and time\n  const formatDateTime = dateString => {\n    if (!dateString) return 'Just now';\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInHours = (now - date) / (1000 * 60 * 60);\n    if (diffInHours < 24) {\n      // Show time if less than 24 hours\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    } else {\n      // Show date if more than 24 hours\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modern-forum\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"forum-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"forum-header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"forum-title\",\n            children: \"Community Forum\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"forum-description\",\n            children: \"Connect with fellow learners, ask questions, and share knowledge. Join our vibrant community discussion!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-ask-btn\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 43\n              }, this),\n              onClick: () => setAskQuestionVisible(true),\n              className: \"ask-question-header\",\n              size: \"large\",\n              children: \"Ask Question\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 21\n      }, this), askQuestionVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ask-question-modal\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ask-question-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"form-title\",\n            children: \"Ask a Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            onFinish: handleAskQuestion,\n            layout: \"vertical\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"Question Title\",\n              rules: [{\n                required: true,\n                message: 'Please enter the title'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"What's your question about?\",\n                className: \"modern-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"body\",\n              label: \"Question Details\",\n              rules: [{\n                required: true,\n                message: 'Please enter the question details'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                rows: 4,\n                placeholder: \"Provide more details about your question...\",\n                className: \"modern-textarea\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              className: \"form-actions\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                className: \"submit-btn\",\n                children: \"Post Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => setAskQuestionVisible(false),\n                className: \"cancel-btn\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 25\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"questions-container\",\n        children: currentQuestions.map(question => {\n          var _question$user, _question$user2, _question$replies, _question$replies2;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modern-question-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"question-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-info\",\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  src: (_question$user = question.user) !== null && _question$user !== void 0 && _question$user.profileImage ? question.user.profileImage : image,\n                  alt: \"profile\",\n                  size: 48,\n                  icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 51\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"username\",\n                    children: ((_question$user2 = question.user) === null || _question$user2 === void 0 ? void 0 : _question$user2.name) || 'Anonymous'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"blue\",\n                    className: \"subject-tag\",\n                    children: question.subject || 'General'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                count: ((_question$replies = question.replies) === null || _question$replies === void 0 ? void 0 : _question$replies.length) || 0,\n                className: \"reply-badge\",\n                showZero: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"question-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"question-title\",\n                children: question.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"question-body\",\n                children: question.body\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"question-actions\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                icon: /*#__PURE__*/_jsxDEV(MessageOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 47\n                }, this),\n                onClick: () => handleReply(question._id),\n                className: \"action-btn reply-btn\",\n                children: \"Reply\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 47\n                }, this),\n                onClick: () => toggleReplies(question._id),\n                className: \"action-btn view-btn\",\n                children: expandedReplies[question._id] ? \"Hide Replies\" : \"View Replies\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 33\n            }, this), expandedReplies[question._id] && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"replies-section\",\n              children: ((_question$replies2 = question.replies) === null || _question$replies2 === void 0 ? void 0 : _question$replies2.length) > 0 ? question.replies.map(reply => {\n                var _reply$user, _reply$user2;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"modern-reply\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"reply-header\",\n                    children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                      src: (_reply$user = reply.user) !== null && _reply$user !== void 0 && _reply$user.profileImage ? reply.user.profileImage : image,\n                      alt: \"profile\",\n                      size: 32,\n                      icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 300,\n                        columnNumber: 67\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 296,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"reply-username\",\n                      children: ((_reply$user2 = reply.user) === null || _reply$user2 === void 0 ? void 0 : _reply$user2.name) || 'Anonymous'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 302,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 53\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"reply-content\",\n                    children: reply.text\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 53\n                  }, this)]\n                }, reply._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 49\n                }, this);\n              }) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"no-replies\",\n                children: \"No replies yet. Be the first to reply!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: replyRefs[question._id],\n              children: replyQuestionId === question._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reply-form-section\",\n                children: /*#__PURE__*/_jsxDEV(Form, {\n                  form: form,\n                  onFinish: handleReplySubmit,\n                  layout: \"vertical\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                    name: \"text\",\n                    label: \"Your Reply\",\n                    rules: [{\n                      required: true,\n                      message: 'Please enter your reply'\n                    }],\n                    children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                      rows: 3,\n                      placeholder: \"Write your reply...\",\n                      className: \"modern-textarea\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                    className: \"reply-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      className: \"submit-reply-btn\",\n                      children: \"Submit Reply\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      onClick: () => setReplyQuestionId(null),\n                      className: \"cancel-reply-btn\",\n                      children: \"Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 33\n            }, this)]\n          }, question._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 29\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 21\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"forum-pagination\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [\"Showing \", indexOfFirstQuestion + 1, \"-\", Math.min(indexOfLastQuestion, questions.length), \" of \", questions.length, \" questions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-controls\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handlePageChange(currentPage - 1),\n            disabled: currentPage === 1,\n            className: \"pagination-btn\",\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 33\n          }, this), Array.from({\n            length: totalPages\n          }, (_, i) => i + 1).map(page => /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handlePageChange(page),\n            className: `pagination-btn ${currentPage === page ? 'active' : ''}`,\n            children: page\n          }, page, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 37\n          }, this)), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handlePageChange(currentPage + 1),\n            disabled: currentPage === totalPages,\n            className: \"pagination-btn\",\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 9\n  }, this);\n};\n_s(Forum, \"Q3mjrzM5QCjbgkH5fGALxIqRNSI=\", false, function () {\n  return [Form.useForm, useDispatch];\n});\n_c = Forum;\nexport default Forum;\nvar _c;\n$RefreshReg$(_c, \"Forum\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getUserInfo", "message", "<PERSON><PERSON>", "Input", "Form", "Avatar", "Badge", "Tag", "Page<PERSON><PERSON>le", "useDispatch", "HideLoading", "ShowLoading", "addQuestion", "addReply", "getAllQuestions", "image", "PlusOutlined", "MessageOutlined", "EyeOutlined", "UserOutlined", "jsxDEV", "_jsxDEV", "Forum", "_s", "isAdmin", "setIsAdmin", "userData", "setUserData", "questions", "setQuestions", "expandedReplies", "setExpandedReplies", "askQuestionVisible", "setAskQuestionVisible", "replyQuestionId", "setReplyQuestionId", "form", "useForm", "dispatch", "replyRefs", "setReplyRefs", "currentPage", "setCurrentPage", "questionsPerPage", "totalQuestions", "setTotalQuestions", "fetchQuestions", "page", "response", "limit", "success", "sortedQuestions", "data", "sort", "a", "b", "Date", "createdAt", "_id", "total", "length", "error", "getUserData", "localStorage", "getItem", "toggleReplies", "questionId", "prevExpandedReplies", "handleAskQuestion", "values", "resetFields", "handleReply", "handleReplySubmit", "payload", "text", "prevRefs", "createRef", "current", "scrollIntoView", "behavior", "indexOfLastQuestion", "indexOfFirstQuestion", "currentQuestions", "slice", "totalPages", "Math", "ceil", "handlePageChange", "formatDateTime", "dateString", "date", "now", "diffInHours", "toLocaleTimeString", "hour", "minute", "hour12", "toLocaleDateString", "month", "day", "year", "getFullYear", "undefined", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "icon", "onClick", "size", "onFinish", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "TextArea", "rows", "htmlType", "map", "question", "_question$user", "_question$user2", "_question$replies", "_question$replies2", "src", "user", "profileImage", "alt", "color", "subject", "count", "replies", "showZero", "title", "body", "reply", "_reply$user", "_reply$user2", "ref", "min", "disabled", "Array", "from", "_", "i", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport './index.css';\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Badge, Tag } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\r\nimport image from '../../../assets/person.png';\r\nimport { PlusOutlined, MessageOutlined, EyeOutlined, UserOutlined } from '@ant-design/icons';\r\n\r\nconst Forum = () => {\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [userData, setUserData] = useState('');\r\n    const [questions, setQuestions] = useState([]);\r\n    const [expandedReplies, setExpandedReplies] = useState({});\r\n    const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n    const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n    const [form] = Form.useForm();\r\n    const dispatch = useDispatch();\r\n    const [replyRefs, setReplyRefs] = useState({});\r\n\r\n    // Pagination states\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [questionsPerPage] = useState(5);\r\n    const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n    const fetchQuestions = async (page = currentPage) => {\r\n        try {\r\n            const response = await getAllQuestions({ page, limit: questionsPerPage });\r\n            if (response.success) {\r\n                // Sort by creation date (newest first) instead of reversing\r\n                const sortedQuestions = response.data.sort((a, b) =>\r\n                    new Date(b.createdAt || b._id) - new Date(a.createdAt || a._id)\r\n                );\r\n                setQuestions(sortedQuestions);\r\n                setTotalQuestions(response.total || response.data.length);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchQuestions();\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n        dispatch(HideLoading());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const toggleReplies = (questionId) => {\r\n        setExpandedReplies((prevExpandedReplies) => ({\r\n            ...prevExpandedReplies,\r\n            [questionId]: !prevExpandedReplies[questionId],\r\n        }));\r\n    };\r\n\r\n    const handleAskQuestion = async (values) => {\r\n        try {\r\n            const response = await addQuestion(values);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setAskQuestionVisible(false);\r\n                form.resetFields();\r\n                await fetchQuestions();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    const handleReply = (questionId) => {\r\n        setReplyQuestionId(questionId);\r\n    };\r\n\r\n    const handleReplySubmit = async (values) => {\r\n        try {\r\n            const payload = {\r\n                questionId: replyQuestionId,\r\n                text: values.text\r\n            };\r\n            const response = await addReply(payload);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setReplyQuestionId(null);\r\n                form.resetFields();\r\n                await fetchQuestions();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n            setReplyRefs((prevRefs) => ({\r\n                ...prevRefs,\r\n                [replyQuestionId]: React.createRef(),\r\n            }));\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n            replyRefs[replyQuestionId].current.scrollIntoView({ behavior: 'smooth' });\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    // Pagination calculations\r\n    const indexOfLastQuestion = currentPage * questionsPerPage;\r\n    const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;\r\n    const currentQuestions = questions.slice(indexOfFirstQuestion, indexOfLastQuestion);\r\n    const totalPages = Math.ceil(questions.length / questionsPerPage);\r\n\r\n    const handlePageChange = (page) => {\r\n        setCurrentPage(page);\r\n        fetchQuestions(page);\r\n    };\r\n\r\n    // Format date and time\r\n    const formatDateTime = (dateString) => {\r\n        if (!dateString) return 'Just now';\r\n\r\n        const date = new Date(dateString);\r\n        const now = new Date();\r\n        const diffInHours = (now - date) / (1000 * 60 * 60);\r\n\r\n        if (diffInHours < 24) {\r\n            // Show time if less than 24 hours\r\n            return date.toLocaleTimeString('en-US', {\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n                hour12: true\r\n            });\r\n        } else {\r\n            // Show date if more than 24 hours\r\n            return date.toLocaleDateString('en-US', {\r\n                month: 'short',\r\n                day: 'numeric',\r\n                year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined\r\n            });\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            {!isAdmin && (\r\n                <div className=\"modern-forum\">\r\n                    {/* Header Section */}\r\n                    <div className=\"forum-header\">\r\n                        <div className=\"forum-header-content\">\r\n                            <h1 className=\"forum-title\">Community Forum</h1>\r\n                            <p className=\"forum-description\">\r\n                                Connect with fellow learners, ask questions, and share knowledge.\r\n                                Join our vibrant community discussion!\r\n                            </p>\r\n                            {/* Ask Question Button in Header */}\r\n                            <div className=\"header-ask-btn\">\r\n                                <Button\r\n                                    type=\"primary\"\r\n                                    icon={<PlusOutlined />}\r\n                                    onClick={() => setAskQuestionVisible(true)}\r\n                                    className=\"ask-question-header\"\r\n                                    size=\"large\"\r\n                                >\r\n                                    Ask Question\r\n                                </Button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Ask Question Form */}\r\n                    {askQuestionVisible && (\r\n                        <div className=\"ask-question-modal\">\r\n                            <div className=\"ask-question-form\">\r\n                                <h3 className=\"form-title\">Ask a Question</h3>\r\n                                <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\">\r\n                                    <Form.Item\r\n                                        name=\"title\"\r\n                                        label=\"Question Title\"\r\n                                        rules={[{ required: true, message: 'Please enter the title' }]}\r\n                                    >\r\n                                        <Input\r\n                                            placeholder=\"What's your question about?\"\r\n                                            className=\"modern-input\"\r\n                                        />\r\n                                    </Form.Item>\r\n                                    <Form.Item\r\n                                        name=\"body\"\r\n                                        label=\"Question Details\"\r\n                                        rules={[{ required: true, message: 'Please enter the question details' }]}\r\n                                    >\r\n                                        <Input.TextArea\r\n                                            rows={4}\r\n                                            placeholder=\"Provide more details about your question...\"\r\n                                            className=\"modern-textarea\"\r\n                                        />\r\n                                    </Form.Item>\r\n                                    <Form.Item className=\"form-actions\">\r\n                                        <Button type=\"primary\" htmlType=\"submit\" className=\"submit-btn\">\r\n                                            Post Question\r\n                                        </Button>\r\n                                        <Button onClick={() => setAskQuestionVisible(false)} className=\"cancel-btn\">\r\n                                            Cancel\r\n                                        </Button>\r\n                                    </Form.Item>\r\n                                </Form>\r\n                            </div>\r\n                        </div>\r\n                    )}\r\n\r\n                    {/* Questions List */}\r\n                    <div className=\"questions-container\">\r\n                        {currentQuestions.map((question) => (\r\n                            <div key={question._id} className=\"modern-question-card\">\r\n                                {/* Question Header */}\r\n                                <div className=\"question-header\">\r\n                                    <div className=\"user-info\">\r\n                                        <Avatar\r\n                                            src={question.user?.profileImage ? question.user.profileImage : image}\r\n                                            alt=\"profile\"\r\n                                            size={48}\r\n                                            icon={<UserOutlined />}\r\n                                        />\r\n                                        <div className=\"user-details\">\r\n                                            <span className=\"username\">{question.user?.name || 'Anonymous'}</span>\r\n                                            <Tag color=\"blue\" className=\"subject-tag\">\r\n                                                {question.subject || 'General'}\r\n                                            </Tag>\r\n                                        </div>\r\n                                    </div>\r\n                                    <Badge\r\n                                        count={question.replies?.length || 0}\r\n                                        className=\"reply-badge\"\r\n                                        showZero\r\n                                    />\r\n                                </div>\r\n\r\n                                {/* Question Content */}\r\n                                <div className=\"question-content\">\r\n                                    <h3 className=\"question-title\">{question.title}</h3>\r\n                                    <p className=\"question-body\">{question.body}</p>\r\n                                </div>\r\n\r\n                                {/* Action Buttons */}\r\n                                <div className=\"question-actions\">\r\n                                    <Button\r\n                                        icon={<MessageOutlined />}\r\n                                        onClick={() => handleReply(question._id)}\r\n                                        className=\"action-btn reply-btn\"\r\n                                    >\r\n                                        Reply\r\n                                    </Button>\r\n                                    <Button\r\n                                        icon={<EyeOutlined />}\r\n                                        onClick={() => toggleReplies(question._id)}\r\n                                        className=\"action-btn view-btn\"\r\n                                    >\r\n                                        {expandedReplies[question._id] ? \"Hide Replies\" : \"View Replies\"}\r\n                                    </Button>\r\n                                </div>\r\n\r\n                                {/* Replies Section */}\r\n                                {expandedReplies[question._id] && (\r\n                                    <div className=\"replies-section\">\r\n                                        {question.replies?.length > 0 ? (\r\n                                            question.replies.map((reply) => (\r\n                                                <div key={reply._id} className=\"modern-reply\">\r\n                                                    <div className=\"reply-header\">\r\n                                                        <Avatar\r\n                                                            src={reply.user?.profileImage ? reply.user.profileImage : image}\r\n                                                            alt=\"profile\"\r\n                                                            size={32}\r\n                                                            icon={<UserOutlined />}\r\n                                                        />\r\n                                                        <span className=\"reply-username\">{reply.user?.name || 'Anonymous'}</span>\r\n                                                    </div>\r\n                                                    <div className=\"reply-content\">{reply.text}</div>\r\n                                                </div>\r\n                                            ))\r\n                                        ) : (\r\n                                            <p className=\"no-replies\">No replies yet. Be the first to reply!</p>\r\n                                        )}\r\n                                    </div>\r\n                                )}\r\n\r\n                                {/* Reply Form */}\r\n                                <div ref={replyRefs[question._id]}>\r\n                                    {replyQuestionId === question._id && (\r\n                                        <div className=\"reply-form-section\">\r\n                                            <Form form={form} onFinish={handleReplySubmit} layout=\"vertical\">\r\n                                                <Form.Item\r\n                                                    name=\"text\"\r\n                                                    label=\"Your Reply\"\r\n                                                    rules={[{ required: true, message: 'Please enter your reply' }]}\r\n                                                >\r\n                                                    <Input.TextArea\r\n                                                        rows={3}\r\n                                                        placeholder=\"Write your reply...\"\r\n                                                        className=\"modern-textarea\"\r\n                                                    />\r\n                                                </Form.Item>\r\n                                                <Form.Item className=\"reply-actions\">\r\n                                                    <Button type=\"primary\" htmlType=\"submit\" className=\"submit-reply-btn\">\r\n                                                        Submit Reply\r\n                                                    </Button>\r\n                                                    <Button onClick={() => setReplyQuestionId(null)} className=\"cancel-reply-btn\">\r\n                                                        Cancel\r\n                                                    </Button>\r\n                                                </Form.Item>\r\n                                            </Form>\r\n                                        </div>\r\n                                    )}\r\n                                </div>\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n\r\n                    {/* Pagination */}\r\n                    {totalPages > 1 && (\r\n                        <div className=\"forum-pagination\">\r\n                            <div className=\"pagination-info\">\r\n                                Showing {indexOfFirstQuestion + 1}-{Math.min(indexOfLastQuestion, questions.length)} of {questions.length} questions\r\n                            </div>\r\n                            <div className=\"pagination-controls\">\r\n                                <Button\r\n                                    onClick={() => handlePageChange(currentPage - 1)}\r\n                                    disabled={currentPage === 1}\r\n                                    className=\"pagination-btn\"\r\n                                >\r\n                                    Previous\r\n                                </Button>\r\n\r\n                                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (\r\n                                    <Button\r\n                                        key={page}\r\n                                        onClick={() => handlePageChange(page)}\r\n                                        className={`pagination-btn ${currentPage === page ? 'active' : ''}`}\r\n                                    >\r\n                                        {page}\r\n                                    </Button>\r\n                                ))}\r\n\r\n                                <Button\r\n                                    onClick={() => handlePageChange(currentPage + 1)}\r\n                                    disabled={currentPage === totalPages}\r\n                                    className=\"pagination-btn\"\r\n                                >\r\n                                    Next\r\n                                </Button>\r\n                            </div>\r\n                        </div>\r\n                    )}\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Forum;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,QAAQ,MAAM;AACvE,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,WAAW,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,yBAAyB;AAChF,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,SAASC,YAAY,EAAEC,eAAe,EAAEC,WAAW,EAAEC,YAAY,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7F,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACmC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuC,IAAI,CAAC,GAAGhC,IAAI,CAACiC,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC8C,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EACtC,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAMiD,cAAc,GAAG,MAAAA,CAAOC,IAAI,GAAGN,WAAW,KAAK;IACjD,IAAI;MACA,MAAMO,QAAQ,GAAG,MAAMlC,eAAe,CAAC;QAAEiC,IAAI;QAAEE,KAAK,EAAEN;MAAiB,CAAC,CAAC;MACzE,IAAIK,QAAQ,CAACE,OAAO,EAAE;QAClB;QACA,MAAMC,eAAe,GAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAC5C,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,IAAIF,CAAC,CAACG,GAAG,CAAC,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,SAAS,IAAIH,CAAC,CAACI,GAAG,CAClE,CAAC;QACD7B,YAAY,CAACsB,eAAe,CAAC;QAC7BN,iBAAiB,CAACG,QAAQ,CAACW,KAAK,IAAIX,QAAQ,CAACI,IAAI,CAACQ,MAAM,CAAC;MAC7D,CAAC,MAAM;QACH3D,OAAO,CAAC4D,KAAK,CAACb,QAAQ,CAAC/C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAO4D,KAAK,EAAE;MACZ5D,OAAO,CAAC4D,KAAK,CAACA,KAAK,CAAC5D,OAAO,CAAC;IAChC;EACJ,CAAC;EAED,MAAM6D,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMd,QAAQ,GAAG,MAAMhD,WAAW,CAAC,CAAC;MACpC,IAAIgD,QAAQ,CAACE,OAAO,EAAE;QAClB,IAAIF,QAAQ,CAACI,IAAI,CAAC5B,OAAO,EAAE;UACvBC,UAAU,CAAC,IAAI,CAAC;QACpB,CAAC,MAAM;UACHA,UAAU,CAAC,KAAK,CAAC;UACjBE,WAAW,CAACqB,QAAQ,CAACI,IAAI,CAAC;UAC1B,MAAMN,cAAc,CAAC,CAAC;QAC1B;MACJ,CAAC,MAAM;QACH7C,OAAO,CAAC4D,KAAK,CAACb,QAAQ,CAAC/C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAO4D,KAAK,EAAE;MACZ5D,OAAO,CAAC4D,KAAK,CAACA,KAAK,CAAC5D,OAAO,CAAC;IAChC;IACAqC,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;EAC3B,CAAC;EAEDZ,SAAS,CAAC,MAAM;IACZ,IAAIiE,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MAC/B1B,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;MACvBmD,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAIC,UAAU,IAAK;IAClCnC,kBAAkB,CAAEoC,mBAAmB,KAAM;MACzC,GAAGA,mBAAmB;MACtB,CAACD,UAAU,GAAG,CAACC,mBAAmB,CAACD,UAAU;IACjD,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IACxC,IAAI;MACA,MAAMrB,QAAQ,GAAG,MAAMpC,WAAW,CAACyD,MAAM,CAAC;MAC1C,IAAIrB,QAAQ,CAACE,OAAO,EAAE;QAClBjD,OAAO,CAACiD,OAAO,CAACF,QAAQ,CAAC/C,OAAO,CAAC;QACjCgC,qBAAqB,CAAC,KAAK,CAAC;QAC5BG,IAAI,CAACkC,WAAW,CAAC,CAAC;QAClB,MAAMxB,cAAc,CAAC,CAAC;MAC1B,CAAC,MAAM;QACH7C,OAAO,CAAC4D,KAAK,CAACb,QAAQ,CAAC/C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAO4D,KAAK,EAAE;MACZ5D,OAAO,CAAC4D,KAAK,CAACA,KAAK,CAAC5D,OAAO,CAAC;IAChC;EACJ,CAAC;EAED,MAAMsE,WAAW,GAAIL,UAAU,IAAK;IAChC/B,kBAAkB,CAAC+B,UAAU,CAAC;EAClC,CAAC;EAED,MAAMM,iBAAiB,GAAG,MAAOH,MAAM,IAAK;IACxC,IAAI;MACA,MAAMI,OAAO,GAAG;QACZP,UAAU,EAAEhC,eAAe;QAC3BwC,IAAI,EAAEL,MAAM,CAACK;MACjB,CAAC;MACD,MAAM1B,QAAQ,GAAG,MAAMnC,QAAQ,CAAC4D,OAAO,CAAC;MACxC,IAAIzB,QAAQ,CAACE,OAAO,EAAE;QAClBjD,OAAO,CAACiD,OAAO,CAACF,QAAQ,CAAC/C,OAAO,CAAC;QACjCkC,kBAAkB,CAAC,IAAI,CAAC;QACxBC,IAAI,CAACkC,WAAW,CAAC,CAAC;QAClB,MAAMxB,cAAc,CAAC,CAAC;MAC1B,CAAC,MAAM;QACH7C,OAAO,CAAC4D,KAAK,CAACb,QAAQ,CAAC/C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAO4D,KAAK,EAAE;MACZ5D,OAAO,CAAC4D,KAAK,CAACA,KAAK,CAAC5D,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDH,SAAS,CAAC,MAAM;IACZ,IAAIoC,eAAe,IAAI,CAACK,SAAS,CAACL,eAAe,CAAC,EAAE;MAChDM,YAAY,CAAEmC,QAAQ,KAAM;QACxB,GAAGA,QAAQ;QACX,CAACzC,eAAe,gBAAGtC,KAAK,CAACgF,SAAS,CAAC;MACvC,CAAC,CAAC,CAAC;IACP;EACJ,CAAC,EAAE,CAAC1C,eAAe,EAAEK,SAAS,CAAC,CAAC;EAEhCzC,SAAS,CAAC,MAAM;IACZ,IAAIoC,eAAe,IAAIK,SAAS,CAACL,eAAe,CAAC,EAAE;MAC/CK,SAAS,CAACL,eAAe,CAAC,CAAC2C,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC7E;EACJ,CAAC,EAAE,CAAC7C,eAAe,EAAEK,SAAS,CAAC,CAAC;;EAEhC;EACA,MAAMyC,mBAAmB,GAAGvC,WAAW,GAAGE,gBAAgB;EAC1D,MAAMsC,oBAAoB,GAAGD,mBAAmB,GAAGrC,gBAAgB;EACnE,MAAMuC,gBAAgB,GAAGtD,SAAS,CAACuD,KAAK,CAACF,oBAAoB,EAAED,mBAAmB,CAAC;EACnF,MAAMI,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAC1D,SAAS,CAACgC,MAAM,GAAGjB,gBAAgB,CAAC;EAEjE,MAAM4C,gBAAgB,GAAIxC,IAAI,IAAK;IAC/BL,cAAc,CAACK,IAAI,CAAC;IACpBD,cAAc,CAACC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMyC,cAAc,GAAIC,UAAU,IAAK;IACnC,IAAI,CAACA,UAAU,EAAE,OAAO,UAAU;IAElC,MAAMC,IAAI,GAAG,IAAIlC,IAAI,CAACiC,UAAU,CAAC;IACjC,MAAME,GAAG,GAAG,IAAInC,IAAI,CAAC,CAAC;IACtB,MAAMoC,WAAW,GAAG,CAACD,GAAG,GAAGD,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEnD,IAAIE,WAAW,GAAG,EAAE,EAAE;MAClB;MACA,OAAOF,IAAI,CAACG,kBAAkB,CAAC,OAAO,EAAE;QACpCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACZ,CAAC,CAAC;IACN,CAAC,MAAM;MACH;MACA,OAAON,IAAI,CAACO,kBAAkB,CAAC,OAAO,EAAE;QACpCC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAEV,IAAI,CAACW,WAAW,CAAC,CAAC,KAAKV,GAAG,CAACU,WAAW,CAAC,CAAC,GAAG,SAAS,GAAGC;MACjE,CAAC,CAAC;IACN;EACJ,CAAC;EAED,oBACIjF,OAAA;IAAAkF,QAAA,EACK,CAAC/E,OAAO,iBACLH,OAAA;MAAKmF,SAAS,EAAC,cAAc;MAAAD,QAAA,gBAEzBlF,OAAA;QAAKmF,SAAS,EAAC,cAAc;QAAAD,QAAA,eACzBlF,OAAA;UAAKmF,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjClF,OAAA;YAAImF,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDvF,OAAA;YAAGmF,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAGjC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJvF,OAAA;YAAKmF,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC3BlF,OAAA,CAACnB,MAAM;cACH2G,IAAI,EAAC,SAAS;cACdC,IAAI,eAAEzF,OAAA,CAACL,YAAY;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBG,OAAO,EAAEA,CAAA,KAAM9E,qBAAqB,CAAC,IAAI,CAAE;cAC3CuE,SAAS,EAAC,qBAAqB;cAC/BQ,IAAI,EAAC,OAAO;cAAAT,QAAA,EACf;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGL5E,kBAAkB,iBACfX,OAAA;QAAKmF,SAAS,EAAC,oBAAoB;QAAAD,QAAA,eAC/BlF,OAAA;UAAKmF,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAC9BlF,OAAA;YAAImF,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CvF,OAAA,CAACjB,IAAI;YAACgC,IAAI,EAAEA,IAAK;YAAC6E,QAAQ,EAAE7C,iBAAkB;YAAC8C,MAAM,EAAC,UAAU;YAAAX,QAAA,gBAC5DlF,OAAA,CAACjB,IAAI,CAAC+G,IAAI;cACNC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,gBAAgB;cACtBC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtH,OAAO,EAAE;cAAyB,CAAC,CAAE;cAAAsG,QAAA,eAE/DlF,OAAA,CAAClB,KAAK;gBACFqH,WAAW,EAAC,6BAA6B;gBACzChB,SAAS,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACZvF,OAAA,CAACjB,IAAI,CAAC+G,IAAI;cACNC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,kBAAkB;cACxBC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEtH,OAAO,EAAE;cAAoC,CAAC,CAAE;cAAAsG,QAAA,eAE1ElF,OAAA,CAAClB,KAAK,CAACsH,QAAQ;gBACXC,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC,6CAA6C;gBACzDhB,SAAS,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACZvF,OAAA,CAACjB,IAAI,CAAC+G,IAAI;cAACX,SAAS,EAAC,cAAc;cAAAD,QAAA,gBAC/BlF,OAAA,CAACnB,MAAM;gBAAC2G,IAAI,EAAC,SAAS;gBAACc,QAAQ,EAAC,QAAQ;gBAACnB,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvF,OAAA,CAACnB,MAAM;gBAAC6G,OAAO,EAAEA,CAAA,KAAM9E,qBAAqB,CAAC,KAAK,CAAE;gBAACuE,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAE5E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,eAGDvF,OAAA;QAAKmF,SAAS,EAAC,qBAAqB;QAAAD,QAAA,EAC/BrB,gBAAgB,CAAC0C,GAAG,CAAEC,QAAQ;UAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,iBAAA,EAAAC,kBAAA;UAAA,oBAC3B5G,OAAA;YAAwBmF,SAAS,EAAC,sBAAsB;YAAAD,QAAA,gBAEpDlF,OAAA;cAAKmF,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC5BlF,OAAA;gBAAKmF,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACtBlF,OAAA,CAAChB,MAAM;kBACH6H,GAAG,EAAE,CAAAJ,cAAA,GAAAD,QAAQ,CAACM,IAAI,cAAAL,cAAA,eAAbA,cAAA,CAAeM,YAAY,GAAGP,QAAQ,CAACM,IAAI,CAACC,YAAY,GAAGrH,KAAM;kBACtEsH,GAAG,EAAC,SAAS;kBACbrB,IAAI,EAAE,EAAG;kBACTF,IAAI,eAAEzF,OAAA,CAACF,YAAY;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACFvF,OAAA;kBAAKmF,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBACzBlF,OAAA;oBAAMmF,SAAS,EAAC,UAAU;oBAAAD,QAAA,EAAE,EAAAwB,eAAA,GAAAF,QAAQ,CAACM,IAAI,cAAAJ,eAAA,uBAAbA,eAAA,CAAeX,IAAI,KAAI;kBAAW;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtEvF,OAAA,CAACd,GAAG;oBAAC+H,KAAK,EAAC,MAAM;oBAAC9B,SAAS,EAAC,aAAa;oBAAAD,QAAA,EACpCsB,QAAQ,CAACU,OAAO,IAAI;kBAAS;oBAAA9B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNvF,OAAA,CAACf,KAAK;gBACFkI,KAAK,EAAE,EAAAR,iBAAA,GAAAH,QAAQ,CAACY,OAAO,cAAAT,iBAAA,uBAAhBA,iBAAA,CAAkBpE,MAAM,KAAI,CAAE;gBACrC4C,SAAS,EAAC,aAAa;gBACvBkC,QAAQ;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNvF,OAAA;cAAKmF,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC7BlF,OAAA;gBAAImF,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,EAAEsB,QAAQ,CAACc;cAAK;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpDvF,OAAA;gBAAGmF,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAEsB,QAAQ,CAACe;cAAI;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eAGNvF,OAAA;cAAKmF,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC7BlF,OAAA,CAACnB,MAAM;gBACH4G,IAAI,eAAEzF,OAAA,CAACJ,eAAe;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BG,OAAO,EAAEA,CAAA,KAAMxC,WAAW,CAACsD,QAAQ,CAACnE,GAAG,CAAE;gBACzC8C,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EACnC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTvF,OAAA,CAACnB,MAAM;gBACH4G,IAAI,eAAEzF,OAAA,CAACH,WAAW;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtBG,OAAO,EAAEA,CAAA,KAAM9C,aAAa,CAAC4D,QAAQ,CAACnE,GAAG,CAAE;gBAC3C8C,SAAS,EAAC,qBAAqB;gBAAAD,QAAA,EAE9BzE,eAAe,CAAC+F,QAAQ,CAACnE,GAAG,CAAC,GAAG,cAAc,GAAG;cAAc;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,EAGL9E,eAAe,CAAC+F,QAAQ,CAACnE,GAAG,CAAC,iBAC1BrC,OAAA;cAAKmF,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAC3B,EAAA0B,kBAAA,GAAAJ,QAAQ,CAACY,OAAO,cAAAR,kBAAA,uBAAhBA,kBAAA,CAAkBrE,MAAM,IAAG,CAAC,GACzBiE,QAAQ,CAACY,OAAO,CAACb,GAAG,CAAEiB,KAAK;gBAAA,IAAAC,WAAA,EAAAC,YAAA;gBAAA,oBACvB1H,OAAA;kBAAqBmF,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBACzClF,OAAA;oBAAKmF,SAAS,EAAC,cAAc;oBAAAD,QAAA,gBACzBlF,OAAA,CAAChB,MAAM;sBACH6H,GAAG,EAAE,CAAAY,WAAA,GAAAD,KAAK,CAACV,IAAI,cAAAW,WAAA,eAAVA,WAAA,CAAYV,YAAY,GAAGS,KAAK,CAACV,IAAI,CAACC,YAAY,GAAGrH,KAAM;sBAChEsH,GAAG,EAAC,SAAS;sBACbrB,IAAI,EAAE,EAAG;sBACTF,IAAI,eAAEzF,OAAA,CAACF,YAAY;wBAAAsF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAE;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC,eACFvF,OAAA;sBAAMmF,SAAS,EAAC,gBAAgB;sBAAAD,QAAA,EAAE,EAAAwC,YAAA,GAAAF,KAAK,CAACV,IAAI,cAAAY,YAAA,uBAAVA,YAAA,CAAY3B,IAAI,KAAI;oBAAW;sBAAAX,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxE,CAAC,eACNvF,OAAA;oBAAKmF,SAAS,EAAC,eAAe;oBAAAD,QAAA,EAAEsC,KAAK,CAACnE;kBAAI;oBAAA+B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA,GAV3CiC,KAAK,CAACnF,GAAG;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAWd,CAAC;cAAA,CACT,CAAC,gBAEFvF,OAAA;gBAAGmF,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YACtE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACR,eAGDvF,OAAA;cAAK2H,GAAG,EAAEzG,SAAS,CAACsF,QAAQ,CAACnE,GAAG,CAAE;cAAA6C,QAAA,EAC7BrE,eAAe,KAAK2F,QAAQ,CAACnE,GAAG,iBAC7BrC,OAAA;gBAAKmF,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,eAC/BlF,OAAA,CAACjB,IAAI;kBAACgC,IAAI,EAAEA,IAAK;kBAAC6E,QAAQ,EAAEzC,iBAAkB;kBAAC0C,MAAM,EAAC,UAAU;kBAAAX,QAAA,gBAC5DlF,OAAA,CAACjB,IAAI,CAAC+G,IAAI;oBACNC,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAC,YAAY;oBAClBC,KAAK,EAAE,CAAC;sBAAEC,QAAQ,EAAE,IAAI;sBAAEtH,OAAO,EAAE;oBAA0B,CAAC,CAAE;oBAAAsG,QAAA,eAEhElF,OAAA,CAAClB,KAAK,CAACsH,QAAQ;sBACXC,IAAI,EAAE,CAAE;sBACRF,WAAW,EAAC,qBAAqB;sBACjChB,SAAS,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,eACZvF,OAAA,CAACjB,IAAI,CAAC+G,IAAI;oBAACX,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAChClF,OAAA,CAACnB,MAAM;sBAAC2G,IAAI,EAAC,SAAS;sBAACc,QAAQ,EAAC,QAAQ;sBAACnB,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAEtE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTvF,OAAA,CAACnB,MAAM;sBAAC6G,OAAO,EAAEA,CAAA,KAAM5E,kBAAkB,CAAC,IAAI,CAAE;sBAACqE,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAE9E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YACR;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,GAnGAiB,QAAQ,CAACnE,GAAG;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoGjB,CAAC;QAAA,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLxB,UAAU,GAAG,CAAC,iBACX/D,OAAA;QAAKmF,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC7BlF,OAAA;UAAKmF,SAAS,EAAC,iBAAiB;UAAAD,QAAA,GAAC,UACrB,EAACtB,oBAAoB,GAAG,CAAC,EAAC,GAAC,EAACI,IAAI,CAAC4D,GAAG,CAACjE,mBAAmB,EAAEpD,SAAS,CAACgC,MAAM,CAAC,EAAC,MAAI,EAAChC,SAAS,CAACgC,MAAM,EAAC,YAC9G;QAAA;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNvF,OAAA;UAAKmF,SAAS,EAAC,qBAAqB;UAAAD,QAAA,gBAChClF,OAAA,CAACnB,MAAM;YACH6G,OAAO,EAAEA,CAAA,KAAMxB,gBAAgB,CAAC9C,WAAW,GAAG,CAAC,CAAE;YACjDyG,QAAQ,EAAEzG,WAAW,KAAK,CAAE;YAC5B+D,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAC7B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERuC,KAAK,CAACC,IAAI,CAAC;YAAExF,MAAM,EAAEwB;UAAW,CAAC,EAAE,CAACiE,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAAC1B,GAAG,CAAC7E,IAAI,iBACzD1B,OAAA,CAACnB,MAAM;YAEH6G,OAAO,EAAEA,CAAA,KAAMxB,gBAAgB,CAACxC,IAAI,CAAE;YACtCyD,SAAS,EAAG,kBAAiB/D,WAAW,KAAKM,IAAI,GAAG,QAAQ,GAAG,EAAG,EAAE;YAAAwD,QAAA,EAEnExD;UAAI,GAJAA,IAAI;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKL,CACX,CAAC,eAEFvF,OAAA,CAACnB,MAAM;YACH6G,OAAO,EAAEA,CAAA,KAAMxB,gBAAgB,CAAC9C,WAAW,GAAG,CAAC,CAAE;YACjDyG,QAAQ,EAAEzG,WAAW,KAAK2C,UAAW;YACrCoB,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAC7B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAArF,EAAA,CApXKD,KAAK;EAAA,QAOQlB,IAAI,CAACiC,OAAO,EACV5B,WAAW;AAAA;AAAA8I,EAAA,GAR1BjI,KAAK;AAsXX,eAAeA,KAAK;AAAC,IAAAiI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}