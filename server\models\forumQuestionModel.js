const mongoose = require('mongoose');

const forumQuestionSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true
    },
    body: {
        type: String,
        required: true
    },
    level: {
        type: String,
        enum: ["primary", "secondary", "advance"],
        default: "primary",
        required: false,
        set: function(value) {
            // Automatically convert to lowercase
            return value ? value.toLowerCase() : "primary";
        }
    },
    user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "users",
        required: true
    },
    replies: [
        {
            text: {
                type: String,
                required: true
            },
            user: {
                type: mongoose.Schema.Types.ObjectId,
                ref: "users",
                required: false // Make optional for AI responses
            },
            isVerified: {
                type: Boolean,
                default: false
            },
            isAI: {
                type: Boolean,
                default: false
            },
            aiAuthor: {
                type: String,
                default: null // For AI responses, store "Brainwave AI"
            },
            createdAt: {
                type: Date,
                default: Date.now
            },
            updatedAt: {
                type: Date,
                default: Date.now
            }
        }
    ],
},
    {
        timestamps: true,
    }
);

const forumQuestion = mongoose.model('forum-questions', forumQuestionSchema);

module.exports = forumQuestion;
