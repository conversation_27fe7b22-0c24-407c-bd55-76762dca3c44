// Simple database connection test
const mongoose = require('mongoose');
require('dotenv').config({ path: './server/.env' });

console.log('🧪 Testing MongoDB Connection...');
console.log('📍 MongoDB URL:', process.env.MONGO_URL ? 'Set' : 'Not set');

async function testConnection() {
  try {
    console.log('🔗 Attempting to connect to MongoDB...');
    
    const options = {
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 10000,
      connectTimeoutMS: 10000,
      family: 4,
      maxPoolSize: 5,
      retryWrites: true,
    };

    await mongoose.connect(process.env.MONGO_URL, options);
    console.log('✅ MongoDB connection successful!');
    
    // Test a simple query
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('📊 Available collections:', collections.length);
    
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    process.exit(1);
  }
}

testConnection();
