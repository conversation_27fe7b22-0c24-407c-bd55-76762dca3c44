{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Forum\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport './index.css';\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message, Button, Input, Form, Avatar, Badge } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\nimport image from '../../../assets/person.png';\nimport { PlusOutlined, MessageOutlined, UserOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Forum = () => {\n  _s();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [userData, setUserData] = useState('');\n  const [questions, setQuestions] = useState([]);\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const [replyRefs, setReplyRefs] = useState({});\n\n  // Pagination states\n  const [currentPage, setCurrentPage] = useState(1);\n  const [questionsPerPage] = useState(5);\n  const [totalQuestions, setTotalQuestions] = useState(0);\n  const fetchQuestions = async (page = currentPage) => {\n    try {\n      const response = await getAllQuestions({\n        page,\n        limit: questionsPerPage\n      });\n      if (response.success) {\n        // Sort by creation date (newest first) instead of reversing\n        const sortedQuestions = response.data.sort((a, b) => new Date(b.createdAt || b._id) - new Date(a.createdAt || a._id));\n        setQuestions(sortedQuestions);\n        setTotalQuestions(response.total || response.data.length);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchQuestions();\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n    dispatch(HideLoading());\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      dispatch(ShowLoading());\n      getUserData();\n    }\n  }, []);\n  const toggleReplies = questionId => {\n    setExpandedReplies(prevExpandedReplies => ({\n      ...prevExpandedReplies,\n      [questionId]: !prevExpandedReplies[questionId]\n    }));\n  };\n  const handleAskQuestion = async values => {\n    try {\n      const response = await addQuestion(values);\n      if (response.success) {\n        message.success(response.message);\n        setAskQuestionVisible(false);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleReply = questionId => {\n    setReplyQuestionId(questionId);\n  };\n  const handleReplySubmit = async values => {\n    try {\n      const payload = {\n        questionId: replyQuestionId,\n        text: values.text\n      };\n      const response = await addReply(payload);\n      if (response.success) {\n        message.success(response.message);\n        setReplyQuestionId(null);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\n      setReplyRefs(prevRefs => ({\n        ...prevRefs,\n        [replyQuestionId]: /*#__PURE__*/React.createRef()\n      }));\n    }\n  }, [replyQuestionId, replyRefs]);\n  useEffect(() => {\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\n      replyRefs[replyQuestionId].current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [replyQuestionId, replyRefs]);\n\n  // Pagination calculations\n  const indexOfLastQuestion = currentPage * questionsPerPage;\n  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;\n  const currentQuestions = questions.slice(indexOfFirstQuestion, indexOfLastQuestion);\n  const totalPages = Math.ceil(questions.length / questionsPerPage);\n  const handlePageChange = page => {\n    setCurrentPage(page);\n    fetchQuestions(page);\n  };\n\n  // Format date and time\n  const formatDateTime = dateString => {\n    if (!dateString) return 'Just now';\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInHours = (now - date) / (1000 * 60 * 60);\n    if (diffInHours < 24) {\n      // Show time if less than 24 hours\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    } else {\n      // Show date if more than 24 hours\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modern-forum\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"forum-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"forum-header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"forum-title\",\n            children: \"Community Forum\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"forum-description\",\n            children: \"Connect with fellow learners, ask questions, and share knowledge. Join our vibrant community discussion!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-ask-btn\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 43\n              }, this),\n              onClick: () => setAskQuestionVisible(true),\n              className: \"ask-question-header\",\n              size: \"large\",\n              children: \"Ask Question\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 21\n      }, this), askQuestionVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ask-question-modal\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ask-question-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"form-title\",\n            children: \"Ask a Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            onFinish: handleAskQuestion,\n            layout: \"vertical\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"Question Title\",\n              rules: [{\n                required: true,\n                message: 'Please enter the title'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"What's your question about?\",\n                className: \"modern-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"body\",\n              label: \"Question Details\",\n              rules: [{\n                required: true,\n                message: 'Please enter the question details'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                rows: 4,\n                placeholder: \"Provide more details about your question...\",\n                className: \"modern-textarea\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              className: \"form-actions\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                className: \"submit-btn\",\n                children: \"Post Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => setAskQuestionVisible(false),\n                className: \"cancel-btn\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 25\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"questions-container\",\n        children: currentQuestions.map(question => {\n          var _question$user, _question$user2, _question$replies, _question$replies2;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modern-question-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"question-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-info\",\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  src: (_question$user = question.user) !== null && _question$user !== void 0 && _question$user.profileImage ? question.user.profileImage : image,\n                  alt: \"profile\",\n                  size: 48,\n                  icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 51\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"username\",\n                    children: ((_question$user2 = question.user) === null || _question$user2 === void 0 ? void 0 : _question$user2.name) || 'Anonymous'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"question-datetime\",\n                    children: formatDateTime(question.createdAt)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                count: ((_question$replies = question.replies) === null || _question$replies === void 0 ? void 0 : _question$replies.length) || 0,\n                className: \"reply-badge\",\n                showZero: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"question-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"question-title\",\n                children: question.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"question-body\",\n                children: question.body\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"replies-section\",\n              children: ((_question$replies2 = question.replies) === null || _question$replies2 === void 0 ? void 0 : _question$replies2.length) > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"replies-header\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"replies-count\",\n                    children: [question.replies.length, \" \", question.replies.length === 1 ? 'Reply' : 'Replies']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 45\n                }, this), question.replies.map(reply => {\n                  var _reply$user, _reply$user2;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"modern-reply\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"reply-header\",\n                      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                        src: (_reply$user = reply.user) !== null && _reply$user !== void 0 && _reply$user.profileImage ? reply.user.profileImage : image,\n                        alt: \"profile\",\n                        size: 32,\n                        icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 285,\n                          columnNumber: 67\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 281,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"reply-user-info\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"reply-username\",\n                          children: ((_reply$user2 = reply.user) === null || _reply$user2 === void 0 ? void 0 : _reply$user2.name) || 'Anonymous'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 288,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"reply-datetime\",\n                          children: formatDateTime(reply.createdAt)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 289,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 287,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 280,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"reply-content\",\n                      children: reply.text\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 294,\n                      columnNumber: 53\n                    }, this)]\n                  }, reply._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 49\n                  }, this);\n                })]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"no-replies\",\n                children: \"No replies yet. Be the first to reply!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"question-actions\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                icon: /*#__PURE__*/_jsxDEV(MessageOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 306,\n                  columnNumber: 47\n                }, this),\n                onClick: () => handleReply(question._id),\n                className: \"action-btn reply-btn\",\n                children: \"Add Reply\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: replyRefs[question._id],\n              children: replyQuestionId === question._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reply-form-section\",\n                children: /*#__PURE__*/_jsxDEV(Form, {\n                  form: form,\n                  onFinish: handleReplySubmit,\n                  layout: \"vertical\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                    name: \"text\",\n                    label: \"Your Reply\",\n                    rules: [{\n                      required: true,\n                      message: 'Please enter your reply'\n                    }],\n                    children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                      rows: 3,\n                      placeholder: \"Write your reply...\",\n                      className: \"modern-textarea\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                    className: \"reply-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      className: \"submit-reply-btn\",\n                      children: \"Submit Reply\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 331,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      onClick: () => setReplyQuestionId(null),\n                      className: \"cancel-reply-btn\",\n                      children: \"Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 33\n            }, this)]\n          }, question._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 29\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 21\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"forum-pagination\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [\"Showing \", indexOfFirstQuestion + 1, \"-\", Math.min(indexOfLastQuestion, questions.length), \" of \", questions.length, \" questions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-controls\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handlePageChange(currentPage - 1),\n            disabled: currentPage === 1,\n            className: \"pagination-btn\",\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 33\n          }, this), Array.from({\n            length: totalPages\n          }, (_, i) => i + 1).map(page => /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handlePageChange(page),\n            className: `pagination-btn ${currentPage === page ? 'active' : ''}`,\n            children: page\n          }, page, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 37\n          }, this)), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handlePageChange(currentPage + 1),\n            disabled: currentPage === totalPages,\n            className: \"pagination-btn\",\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 9\n  }, this);\n};\n_s(Forum, \"PLowEnnynYfT2Iq7gUlOJ/goKSg=\", false, function () {\n  return [Form.useForm, useDispatch];\n});\n_c = Forum;\nexport default Forum;\nvar _c;\n$RefreshReg$(_c, \"Forum\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getUserInfo", "message", "<PERSON><PERSON>", "Input", "Form", "Avatar", "Badge", "useDispatch", "HideLoading", "ShowLoading", "addQuestion", "addReply", "getAllQuestions", "image", "PlusOutlined", "MessageOutlined", "UserOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Forum", "_s", "isAdmin", "setIsAdmin", "userData", "setUserData", "questions", "setQuestions", "askQuestionVisible", "setAskQuestionVisible", "replyQuestionId", "setReplyQuestionId", "form", "useForm", "dispatch", "replyRefs", "setReplyRefs", "currentPage", "setCurrentPage", "questionsPerPage", "totalQuestions", "setTotalQuestions", "fetchQuestions", "page", "response", "limit", "success", "sortedQuestions", "data", "sort", "a", "b", "Date", "createdAt", "_id", "total", "length", "error", "getUserData", "localStorage", "getItem", "toggleReplies", "questionId", "setExpandedReplies", "prevExpandedReplies", "handleAskQuestion", "values", "resetFields", "handleReply", "handleReplySubmit", "payload", "text", "prevRefs", "createRef", "current", "scrollIntoView", "behavior", "indexOfLastQuestion", "indexOfFirstQuestion", "currentQuestions", "slice", "totalPages", "Math", "ceil", "handlePageChange", "formatDateTime", "dateString", "date", "now", "diffInHours", "toLocaleTimeString", "hour", "minute", "hour12", "toLocaleDateString", "month", "day", "year", "getFullYear", "undefined", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "icon", "onClick", "size", "onFinish", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "TextArea", "rows", "htmlType", "map", "question", "_question$user", "_question$user2", "_question$replies", "_question$replies2", "src", "user", "profileImage", "alt", "count", "replies", "showZero", "title", "body", "reply", "_reply$user", "_reply$user2", "ref", "min", "disabled", "Array", "from", "_", "i", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport './index.css';\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Badge } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\r\nimport image from '../../../assets/person.png';\r\nimport { PlusOutlined, MessageOutlined, UserOutlined } from '@ant-design/icons';\r\n\r\nconst Forum = () => {\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [userData, setUserData] = useState('');\r\n    const [questions, setQuestions] = useState([]);\r\n    const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n    const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n    const [form] = Form.useForm();\r\n    const dispatch = useDispatch();\r\n    const [replyRefs, setReplyRefs] = useState({});\r\n\r\n    // Pagination states\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [questionsPerPage] = useState(5);\r\n    const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n    const fetchQuestions = async (page = currentPage) => {\r\n        try {\r\n            const response = await getAllQuestions({ page, limit: questionsPerPage });\r\n            if (response.success) {\r\n                // Sort by creation date (newest first) instead of reversing\r\n                const sortedQuestions = response.data.sort((a, b) =>\r\n                    new Date(b.createdAt || b._id) - new Date(a.createdAt || a._id)\r\n                );\r\n                setQuestions(sortedQuestions);\r\n                setTotalQuestions(response.total || response.data.length);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchQuestions();\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n        dispatch(HideLoading());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const toggleReplies = (questionId) => {\r\n        setExpandedReplies((prevExpandedReplies) => ({\r\n            ...prevExpandedReplies,\r\n            [questionId]: !prevExpandedReplies[questionId],\r\n        }));\r\n    };\r\n\r\n    const handleAskQuestion = async (values) => {\r\n        try {\r\n            const response = await addQuestion(values);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setAskQuestionVisible(false);\r\n                form.resetFields();\r\n                await fetchQuestions();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    const handleReply = (questionId) => {\r\n        setReplyQuestionId(questionId);\r\n    };\r\n\r\n    const handleReplySubmit = async (values) => {\r\n        try {\r\n            const payload = {\r\n                questionId: replyQuestionId,\r\n                text: values.text\r\n            };\r\n            const response = await addReply(payload);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setReplyQuestionId(null);\r\n                form.resetFields();\r\n                await fetchQuestions();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n            setReplyRefs((prevRefs) => ({\r\n                ...prevRefs,\r\n                [replyQuestionId]: React.createRef(),\r\n            }));\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n            replyRefs[replyQuestionId].current.scrollIntoView({ behavior: 'smooth' });\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    // Pagination calculations\r\n    const indexOfLastQuestion = currentPage * questionsPerPage;\r\n    const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;\r\n    const currentQuestions = questions.slice(indexOfFirstQuestion, indexOfLastQuestion);\r\n    const totalPages = Math.ceil(questions.length / questionsPerPage);\r\n\r\n    const handlePageChange = (page) => {\r\n        setCurrentPage(page);\r\n        fetchQuestions(page);\r\n    };\r\n\r\n    // Format date and time\r\n    const formatDateTime = (dateString) => {\r\n        if (!dateString) return 'Just now';\r\n\r\n        const date = new Date(dateString);\r\n        const now = new Date();\r\n        const diffInHours = (now - date) / (1000 * 60 * 60);\r\n\r\n        if (diffInHours < 24) {\r\n            // Show time if less than 24 hours\r\n            return date.toLocaleTimeString('en-US', {\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n                hour12: true\r\n            });\r\n        } else {\r\n            // Show date if more than 24 hours\r\n            return date.toLocaleDateString('en-US', {\r\n                month: 'short',\r\n                day: 'numeric',\r\n                year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined\r\n            });\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            {!isAdmin && (\r\n                <div className=\"modern-forum\">\r\n                    {/* Header Section */}\r\n                    <div className=\"forum-header\">\r\n                        <div className=\"forum-header-content\">\r\n                            <h1 className=\"forum-title\">Community Forum</h1>\r\n                            <p className=\"forum-description\">\r\n                                Connect with fellow learners, ask questions, and share knowledge.\r\n                                Join our vibrant community discussion!\r\n                            </p>\r\n                            {/* Ask Question Button in Header */}\r\n                            <div className=\"header-ask-btn\">\r\n                                <Button\r\n                                    type=\"primary\"\r\n                                    icon={<PlusOutlined />}\r\n                                    onClick={() => setAskQuestionVisible(true)}\r\n                                    className=\"ask-question-header\"\r\n                                    size=\"large\"\r\n                                >\r\n                                    Ask Question\r\n                                </Button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Ask Question Form */}\r\n                    {askQuestionVisible && (\r\n                        <div className=\"ask-question-modal\">\r\n                            <div className=\"ask-question-form\">\r\n                                <h3 className=\"form-title\">Ask a Question</h3>\r\n                                <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\">\r\n                                    <Form.Item\r\n                                        name=\"title\"\r\n                                        label=\"Question Title\"\r\n                                        rules={[{ required: true, message: 'Please enter the title' }]}\r\n                                    >\r\n                                        <Input\r\n                                            placeholder=\"What's your question about?\"\r\n                                            className=\"modern-input\"\r\n                                        />\r\n                                    </Form.Item>\r\n                                    <Form.Item\r\n                                        name=\"body\"\r\n                                        label=\"Question Details\"\r\n                                        rules={[{ required: true, message: 'Please enter the question details' }]}\r\n                                    >\r\n                                        <Input.TextArea\r\n                                            rows={4}\r\n                                            placeholder=\"Provide more details about your question...\"\r\n                                            className=\"modern-textarea\"\r\n                                        />\r\n                                    </Form.Item>\r\n                                    <Form.Item className=\"form-actions\">\r\n                                        <Button type=\"primary\" htmlType=\"submit\" className=\"submit-btn\">\r\n                                            Post Question\r\n                                        </Button>\r\n                                        <Button onClick={() => setAskQuestionVisible(false)} className=\"cancel-btn\">\r\n                                            Cancel\r\n                                        </Button>\r\n                                    </Form.Item>\r\n                                </Form>\r\n                            </div>\r\n                        </div>\r\n                    )}\r\n\r\n                    {/* Questions List */}\r\n                    <div className=\"questions-container\">\r\n                        {currentQuestions.map((question) => (\r\n                            <div key={question._id} className=\"modern-question-card\">\r\n                                {/* Question Header */}\r\n                                <div className=\"question-header\">\r\n                                    <div className=\"user-info\">\r\n                                        <Avatar\r\n                                            src={question.user?.profileImage ? question.user.profileImage : image}\r\n                                            alt=\"profile\"\r\n                                            size={48}\r\n                                            icon={<UserOutlined />}\r\n                                        />\r\n                                        <div className=\"user-details\">\r\n                                            <span className=\"username\">{question.user?.name || 'Anonymous'}</span>\r\n                                            <span className=\"question-datetime\">\r\n                                                {formatDateTime(question.createdAt)}\r\n                                            </span>\r\n                                        </div>\r\n                                    </div>\r\n                                    <Badge\r\n                                        count={question.replies?.length || 0}\r\n                                        className=\"reply-badge\"\r\n                                        showZero\r\n                                    />\r\n                                </div>\r\n\r\n                                {/* Question Content */}\r\n                                <div className=\"question-content\">\r\n                                    <h3 className=\"question-title\">{question.title}</h3>\r\n                                    <p className=\"question-body\">{question.body}</p>\r\n                                </div>\r\n\r\n                                {/* Replies Section - Always Visible */}\r\n                                <div className=\"replies-section\">\r\n                                    {question.replies?.length > 0 ? (\r\n                                        <>\r\n                                            <div className=\"replies-header\">\r\n                                                <span className=\"replies-count\">\r\n                                                    {question.replies.length} {question.replies.length === 1 ? 'Reply' : 'Replies'}\r\n                                                </span>\r\n                                            </div>\r\n                                            {question.replies.map((reply) => (\r\n                                                <div key={reply._id} className=\"modern-reply\">\r\n                                                    <div className=\"reply-header\">\r\n                                                        <Avatar\r\n                                                            src={reply.user?.profileImage ? reply.user.profileImage : image}\r\n                                                            alt=\"profile\"\r\n                                                            size={32}\r\n                                                            icon={<UserOutlined />}\r\n                                                        />\r\n                                                        <div className=\"reply-user-info\">\r\n                                                            <span className=\"reply-username\">{reply.user?.name || 'Anonymous'}</span>\r\n                                                            <span className=\"reply-datetime\">\r\n                                                                {formatDateTime(reply.createdAt)}\r\n                                                            </span>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div className=\"reply-content\">{reply.text}</div>\r\n                                                </div>\r\n                                            ))}\r\n                                        </>\r\n                                    ) : (\r\n                                        <p className=\"no-replies\">No replies yet. Be the first to reply!</p>\r\n                                    )}\r\n                                </div>\r\n\r\n                                {/* Action Buttons */}\r\n                                <div className=\"question-actions\">\r\n                                    <Button\r\n                                        icon={<MessageOutlined />}\r\n                                        onClick={() => handleReply(question._id)}\r\n                                        className=\"action-btn reply-btn\"\r\n                                    >\r\n                                        Add Reply\r\n                                    </Button>\r\n                                </div>\r\n\r\n                                {/* Reply Form */}\r\n                                <div ref={replyRefs[question._id]}>\r\n                                    {replyQuestionId === question._id && (\r\n                                        <div className=\"reply-form-section\">\r\n                                            <Form form={form} onFinish={handleReplySubmit} layout=\"vertical\">\r\n                                                <Form.Item\r\n                                                    name=\"text\"\r\n                                                    label=\"Your Reply\"\r\n                                                    rules={[{ required: true, message: 'Please enter your reply' }]}\r\n                                                >\r\n                                                    <Input.TextArea\r\n                                                        rows={3}\r\n                                                        placeholder=\"Write your reply...\"\r\n                                                        className=\"modern-textarea\"\r\n                                                    />\r\n                                                </Form.Item>\r\n                                                <Form.Item className=\"reply-actions\">\r\n                                                    <Button type=\"primary\" htmlType=\"submit\" className=\"submit-reply-btn\">\r\n                                                        Submit Reply\r\n                                                    </Button>\r\n                                                    <Button onClick={() => setReplyQuestionId(null)} className=\"cancel-reply-btn\">\r\n                                                        Cancel\r\n                                                    </Button>\r\n                                                </Form.Item>\r\n                                            </Form>\r\n                                        </div>\r\n                                    )}\r\n                                </div>\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n\r\n                    {/* Pagination */}\r\n                    {totalPages > 1 && (\r\n                        <div className=\"forum-pagination\">\r\n                            <div className=\"pagination-info\">\r\n                                Showing {indexOfFirstQuestion + 1}-{Math.min(indexOfLastQuestion, questions.length)} of {questions.length} questions\r\n                            </div>\r\n                            <div className=\"pagination-controls\">\r\n                                <Button\r\n                                    onClick={() => handlePageChange(currentPage - 1)}\r\n                                    disabled={currentPage === 1}\r\n                                    className=\"pagination-btn\"\r\n                                >\r\n                                    Previous\r\n                                </Button>\r\n\r\n                                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (\r\n                                    <Button\r\n                                        key={page}\r\n                                        onClick={() => handlePageChange(page)}\r\n                                        className={`pagination-btn ${currentPage === page ? 'active' : ''}`}\r\n                                    >\r\n                                        {page}\r\n                                    </Button>\r\n                                ))}\r\n\r\n                                <Button\r\n                                    onClick={() => handlePageChange(currentPage + 1)}\r\n                                    disabled={currentPage === totalPages}\r\n                                    className=\"pagination-btn\"\r\n                                >\r\n                                    Next\r\n                                </Button>\r\n                            </div>\r\n                        </div>\r\n                    )}\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Forum;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAClE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,WAAW,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,yBAAyB;AAChF,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,SAASC,YAAY,EAAEC,eAAe,EAAEC,YAAY,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEhF,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoC,IAAI,CAAC,GAAG7B,IAAI,CAAC8B,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC2C,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EACtC,MAAM,CAAC4C,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAM8C,cAAc,GAAG,MAAAA,CAAOC,IAAI,GAAGN,WAAW,KAAK;IACjD,IAAI;MACA,MAAMO,QAAQ,GAAG,MAAMjC,eAAe,CAAC;QAAEgC,IAAI;QAAEE,KAAK,EAAEN;MAAiB,CAAC,CAAC;MACzE,IAAIK,QAAQ,CAACE,OAAO,EAAE;QAClB;QACA,MAAMC,eAAe,GAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAC5C,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,IAAIF,CAAC,CAACG,GAAG,CAAC,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,SAAS,IAAIH,CAAC,CAACI,GAAG,CAClE,CAAC;QACD3B,YAAY,CAACoB,eAAe,CAAC;QAC7BN,iBAAiB,CAACG,QAAQ,CAACW,KAAK,IAAIX,QAAQ,CAACI,IAAI,CAACQ,MAAM,CAAC;MAC7D,CAAC,MAAM;QACHxD,OAAO,CAACyD,KAAK,CAACb,QAAQ,CAAC5C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACZzD,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;IAChC;EACJ,CAAC;EAED,MAAM0D,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMd,QAAQ,GAAG,MAAM7C,WAAW,CAAC,CAAC;MACpC,IAAI6C,QAAQ,CAACE,OAAO,EAAE;QAClB,IAAIF,QAAQ,CAACI,IAAI,CAAC1B,OAAO,EAAE;UACvBC,UAAU,CAAC,IAAI,CAAC;QACpB,CAAC,MAAM;UACHA,UAAU,CAAC,KAAK,CAAC;UACjBE,WAAW,CAACmB,QAAQ,CAACI,IAAI,CAAC;UAC1B,MAAMN,cAAc,CAAC,CAAC;QAC1B;MACJ,CAAC,MAAM;QACH1C,OAAO,CAACyD,KAAK,CAACb,QAAQ,CAAC5C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACZzD,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;IAChC;IACAkC,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;EAC3B,CAAC;EAEDV,SAAS,CAAC,MAAM;IACZ,IAAI8D,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MAC/B1B,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACvBkD,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAIC,UAAU,IAAK;IAClCC,kBAAkB,CAAEC,mBAAmB,KAAM;MACzC,GAAGA,mBAAmB;MACtB,CAACF,UAAU,GAAG,CAACE,mBAAmB,CAACF,UAAU;IACjD,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAMG,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IACxC,IAAI;MACA,MAAMtB,QAAQ,GAAG,MAAMnC,WAAW,CAACyD,MAAM,CAAC;MAC1C,IAAItB,QAAQ,CAACE,OAAO,EAAE;QAClB9C,OAAO,CAAC8C,OAAO,CAACF,QAAQ,CAAC5C,OAAO,CAAC;QACjC6B,qBAAqB,CAAC,KAAK,CAAC;QAC5BG,IAAI,CAACmC,WAAW,CAAC,CAAC;QAClB,MAAMzB,cAAc,CAAC,CAAC;MAC1B,CAAC,MAAM;QACH1C,OAAO,CAACyD,KAAK,CAACb,QAAQ,CAAC5C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACZzD,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;IAChC;EACJ,CAAC;EAED,MAAMoE,WAAW,GAAIN,UAAU,IAAK;IAChC/B,kBAAkB,CAAC+B,UAAU,CAAC;EAClC,CAAC;EAED,MAAMO,iBAAiB,GAAG,MAAOH,MAAM,IAAK;IACxC,IAAI;MACA,MAAMI,OAAO,GAAG;QACZR,UAAU,EAAEhC,eAAe;QAC3ByC,IAAI,EAAEL,MAAM,CAACK;MACjB,CAAC;MACD,MAAM3B,QAAQ,GAAG,MAAMlC,QAAQ,CAAC4D,OAAO,CAAC;MACxC,IAAI1B,QAAQ,CAACE,OAAO,EAAE;QAClB9C,OAAO,CAAC8C,OAAO,CAACF,QAAQ,CAAC5C,OAAO,CAAC;QACjC+B,kBAAkB,CAAC,IAAI,CAAC;QACxBC,IAAI,CAACmC,WAAW,CAAC,CAAC;QAClB,MAAMzB,cAAc,CAAC,CAAC;MAC1B,CAAC,MAAM;QACH1C,OAAO,CAACyD,KAAK,CAACb,QAAQ,CAAC5C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACZzD,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDH,SAAS,CAAC,MAAM;IACZ,IAAIiC,eAAe,IAAI,CAACK,SAAS,CAACL,eAAe,CAAC,EAAE;MAChDM,YAAY,CAAEoC,QAAQ,KAAM;QACxB,GAAGA,QAAQ;QACX,CAAC1C,eAAe,gBAAGnC,KAAK,CAAC8E,SAAS,CAAC;MACvC,CAAC,CAAC,CAAC;IACP;EACJ,CAAC,EAAE,CAAC3C,eAAe,EAAEK,SAAS,CAAC,CAAC;EAEhCtC,SAAS,CAAC,MAAM;IACZ,IAAIiC,eAAe,IAAIK,SAAS,CAACL,eAAe,CAAC,EAAE;MAC/CK,SAAS,CAACL,eAAe,CAAC,CAAC4C,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC7E;EACJ,CAAC,EAAE,CAAC9C,eAAe,EAAEK,SAAS,CAAC,CAAC;;EAEhC;EACA,MAAM0C,mBAAmB,GAAGxC,WAAW,GAAGE,gBAAgB;EAC1D,MAAMuC,oBAAoB,GAAGD,mBAAmB,GAAGtC,gBAAgB;EACnE,MAAMwC,gBAAgB,GAAGrD,SAAS,CAACsD,KAAK,CAACF,oBAAoB,EAAED,mBAAmB,CAAC;EACnF,MAAMI,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACzD,SAAS,CAAC8B,MAAM,GAAGjB,gBAAgB,CAAC;EAEjE,MAAM6C,gBAAgB,GAAIzC,IAAI,IAAK;IAC/BL,cAAc,CAACK,IAAI,CAAC;IACpBD,cAAc,CAACC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAM0C,cAAc,GAAIC,UAAU,IAAK;IACnC,IAAI,CAACA,UAAU,EAAE,OAAO,UAAU;IAElC,MAAMC,IAAI,GAAG,IAAInC,IAAI,CAACkC,UAAU,CAAC;IACjC,MAAME,GAAG,GAAG,IAAIpC,IAAI,CAAC,CAAC;IACtB,MAAMqC,WAAW,GAAG,CAACD,GAAG,GAAGD,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEnD,IAAIE,WAAW,GAAG,EAAE,EAAE;MAClB;MACA,OAAOF,IAAI,CAACG,kBAAkB,CAAC,OAAO,EAAE;QACpCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACZ,CAAC,CAAC;IACN,CAAC,MAAM;MACH;MACA,OAAON,IAAI,CAACO,kBAAkB,CAAC,OAAO,EAAE;QACpCC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAEV,IAAI,CAACW,WAAW,CAAC,CAAC,KAAKV,GAAG,CAACU,WAAW,CAAC,CAAC,GAAG,SAAS,GAAGC;MACjE,CAAC,CAAC;IACN;EACJ,CAAC;EAED,oBACIlF,OAAA;IAAAmF,QAAA,EACK,CAAC9E,OAAO,iBACLL,OAAA;MAAKoF,SAAS,EAAC,cAAc;MAAAD,QAAA,gBAEzBnF,OAAA;QAAKoF,SAAS,EAAC,cAAc;QAAAD,QAAA,eACzBnF,OAAA;UAAKoF,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCnF,OAAA;YAAIoF,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDxF,OAAA;YAAGoF,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAGjC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJxF,OAAA;YAAKoF,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC3BnF,OAAA,CAAChB,MAAM;cACHyG,IAAI,EAAC,SAAS;cACdC,IAAI,eAAE1F,OAAA,CAACJ,YAAY;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBG,OAAO,EAAEA,CAAA,KAAM/E,qBAAqB,CAAC,IAAI,CAAE;cAC3CwE,SAAS,EAAC,qBAAqB;cAC/BQ,IAAI,EAAC,OAAO;cAAAT,QAAA,EACf;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGL7E,kBAAkB,iBACfX,OAAA;QAAKoF,SAAS,EAAC,oBAAoB;QAAAD,QAAA,eAC/BnF,OAAA;UAAKoF,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAC9BnF,OAAA;YAAIoF,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CxF,OAAA,CAACd,IAAI;YAAC6B,IAAI,EAAEA,IAAK;YAAC8E,QAAQ,EAAE7C,iBAAkB;YAAC8C,MAAM,EAAC,UAAU;YAAAX,QAAA,gBAC5DnF,OAAA,CAACd,IAAI,CAAC6G,IAAI;cACNC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,gBAAgB;cACtBC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpH,OAAO,EAAE;cAAyB,CAAC,CAAE;cAAAoG,QAAA,eAE/DnF,OAAA,CAACf,KAAK;gBACFmH,WAAW,EAAC,6BAA6B;gBACzChB,SAAS,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACZxF,OAAA,CAACd,IAAI,CAAC6G,IAAI;cACNC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,kBAAkB;cACxBC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEpH,OAAO,EAAE;cAAoC,CAAC,CAAE;cAAAoG,QAAA,eAE1EnF,OAAA,CAACf,KAAK,CAACoH,QAAQ;gBACXC,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC,6CAA6C;gBACzDhB,SAAS,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACZxF,OAAA,CAACd,IAAI,CAAC6G,IAAI;cAACX,SAAS,EAAC,cAAc;cAAAD,QAAA,gBAC/BnF,OAAA,CAAChB,MAAM;gBAACyG,IAAI,EAAC,SAAS;gBAACc,QAAQ,EAAC,QAAQ;gBAACnB,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTxF,OAAA,CAAChB,MAAM;gBAAC2G,OAAO,EAAEA,CAAA,KAAM/E,qBAAqB,CAAC,KAAK,CAAE;gBAACwE,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAE5E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,eAGDxF,OAAA;QAAKoF,SAAS,EAAC,qBAAqB;QAAAD,QAAA,EAC/BrB,gBAAgB,CAAC0C,GAAG,CAAEC,QAAQ;UAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,iBAAA,EAAAC,kBAAA;UAAA,oBAC3B7G,OAAA;YAAwBoF,SAAS,EAAC,sBAAsB;YAAAD,QAAA,gBAEpDnF,OAAA;cAAKoF,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC5BnF,OAAA;gBAAKoF,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACtBnF,OAAA,CAACb,MAAM;kBACH2H,GAAG,EAAE,CAAAJ,cAAA,GAAAD,QAAQ,CAACM,IAAI,cAAAL,cAAA,eAAbA,cAAA,CAAeM,YAAY,GAAGP,QAAQ,CAACM,IAAI,CAACC,YAAY,GAAGrH,KAAM;kBACtEsH,GAAG,EAAC,SAAS;kBACbrB,IAAI,EAAE,EAAG;kBACTF,IAAI,eAAE1F,OAAA,CAACF,YAAY;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACFxF,OAAA;kBAAKoF,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBACzBnF,OAAA;oBAAMoF,SAAS,EAAC,UAAU;oBAAAD,QAAA,EAAE,EAAAwB,eAAA,GAAAF,QAAQ,CAACM,IAAI,cAAAJ,eAAA,uBAAbA,eAAA,CAAeX,IAAI,KAAI;kBAAW;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtExF,OAAA;oBAAMoF,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,EAC9Bf,cAAc,CAACqC,QAAQ,CAACrE,SAAS;kBAAC;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNxF,OAAA,CAACZ,KAAK;gBACF8H,KAAK,EAAE,EAAAN,iBAAA,GAAAH,QAAQ,CAACU,OAAO,cAAAP,iBAAA,uBAAhBA,iBAAA,CAAkBrE,MAAM,KAAI,CAAE;gBACrC6C,SAAS,EAAC,aAAa;gBACvBgC,QAAQ;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNxF,OAAA;cAAKoF,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC7BnF,OAAA;gBAAIoF,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,EAAEsB,QAAQ,CAACY;cAAK;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpDxF,OAAA;gBAAGoF,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAEsB,QAAQ,CAACa;cAAI;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eAGNxF,OAAA;cAAKoF,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAC3B,EAAA0B,kBAAA,GAAAJ,QAAQ,CAACU,OAAO,cAAAN,kBAAA,uBAAhBA,kBAAA,CAAkBtE,MAAM,IAAG,CAAC,gBACzBvC,OAAA,CAAAE,SAAA;gBAAAiF,QAAA,gBACInF,OAAA;kBAAKoF,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,eAC3BnF,OAAA;oBAAMoF,SAAS,EAAC,eAAe;oBAAAD,QAAA,GAC1BsB,QAAQ,CAACU,OAAO,CAAC5E,MAAM,EAAC,GAAC,EAACkE,QAAQ,CAACU,OAAO,CAAC5E,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS;kBAAA;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACLiB,QAAQ,CAACU,OAAO,CAACX,GAAG,CAAEe,KAAK;kBAAA,IAAAC,WAAA,EAAAC,YAAA;kBAAA,oBACxBzH,OAAA;oBAAqBoF,SAAS,EAAC,cAAc;oBAAAD,QAAA,gBACzCnF,OAAA;sBAAKoF,SAAS,EAAC,cAAc;sBAAAD,QAAA,gBACzBnF,OAAA,CAACb,MAAM;wBACH2H,GAAG,EAAE,CAAAU,WAAA,GAAAD,KAAK,CAACR,IAAI,cAAAS,WAAA,eAAVA,WAAA,CAAYR,YAAY,GAAGO,KAAK,CAACR,IAAI,CAACC,YAAY,GAAGrH,KAAM;wBAChEsH,GAAG,EAAC,SAAS;wBACbrB,IAAI,EAAE,EAAG;wBACTF,IAAI,eAAE1F,OAAA,CAACF,YAAY;0BAAAuF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC,eACFxF,OAAA;wBAAKoF,SAAS,EAAC,iBAAiB;wBAAAD,QAAA,gBAC5BnF,OAAA;0BAAMoF,SAAS,EAAC,gBAAgB;0BAAAD,QAAA,EAAE,EAAAsC,YAAA,GAAAF,KAAK,CAACR,IAAI,cAAAU,YAAA,uBAAVA,YAAA,CAAYzB,IAAI,KAAI;wBAAW;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACzExF,OAAA;0BAAMoF,SAAS,EAAC,gBAAgB;0BAAAD,QAAA,EAC3Bf,cAAc,CAACmD,KAAK,CAACnF,SAAS;wBAAC;0BAAAiD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNxF,OAAA;sBAAKoF,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAEoC,KAAK,CAACjE;oBAAI;sBAAA+B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,GAf3C+B,KAAK,CAAClF,GAAG;oBAAAgD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAgBd,CAAC;gBAAA,CACT,CAAC;cAAA,eACJ,CAAC,gBAEHxF,OAAA;gBAAGoF,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YACtE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGNxF,OAAA;cAAKoF,SAAS,EAAC,kBAAkB;cAAAD,QAAA,eAC7BnF,OAAA,CAAChB,MAAM;gBACH0G,IAAI,eAAE1F,OAAA,CAACH,eAAe;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BG,OAAO,EAAEA,CAAA,KAAMxC,WAAW,CAACsD,QAAQ,CAACpE,GAAG,CAAE;gBACzC+C,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EACnC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGNxF,OAAA;cAAK0H,GAAG,EAAExG,SAAS,CAACuF,QAAQ,CAACpE,GAAG,CAAE;cAAA8C,QAAA,EAC7BtE,eAAe,KAAK4F,QAAQ,CAACpE,GAAG,iBAC7BrC,OAAA;gBAAKoF,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,eAC/BnF,OAAA,CAACd,IAAI;kBAAC6B,IAAI,EAAEA,IAAK;kBAAC8E,QAAQ,EAAEzC,iBAAkB;kBAAC0C,MAAM,EAAC,UAAU;kBAAAX,QAAA,gBAC5DnF,OAAA,CAACd,IAAI,CAAC6G,IAAI;oBACNC,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAC,YAAY;oBAClBC,KAAK,EAAE,CAAC;sBAAEC,QAAQ,EAAE,IAAI;sBAAEpH,OAAO,EAAE;oBAA0B,CAAC,CAAE;oBAAAoG,QAAA,eAEhEnF,OAAA,CAACf,KAAK,CAACoH,QAAQ;sBACXC,IAAI,EAAE,CAAE;sBACRF,WAAW,EAAC,qBAAqB;sBACjChB,SAAS,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,eACZxF,OAAA,CAACd,IAAI,CAAC6G,IAAI;oBAACX,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAChCnF,OAAA,CAAChB,MAAM;sBAACyG,IAAI,EAAC,SAAS;sBAACc,QAAQ,EAAC,QAAQ;sBAACnB,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAEtE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTxF,OAAA,CAAChB,MAAM;sBAAC2G,OAAO,EAAEA,CAAA,KAAM7E,kBAAkB,CAAC,IAAI,CAAE;sBAACsE,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAE9E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YACR;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,GAtGAiB,QAAQ,CAACpE,GAAG;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuGjB,CAAC;QAAA,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLxB,UAAU,GAAG,CAAC,iBACXhE,OAAA;QAAKoF,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC7BnF,OAAA;UAAKoF,SAAS,EAAC,iBAAiB;UAAAD,QAAA,GAAC,UACrB,EAACtB,oBAAoB,GAAG,CAAC,EAAC,GAAC,EAACI,IAAI,CAAC0D,GAAG,CAAC/D,mBAAmB,EAAEnD,SAAS,CAAC8B,MAAM,CAAC,EAAC,MAAI,EAAC9B,SAAS,CAAC8B,MAAM,EAAC,YAC9G;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNxF,OAAA;UAAKoF,SAAS,EAAC,qBAAqB;UAAAD,QAAA,gBAChCnF,OAAA,CAAChB,MAAM;YACH2G,OAAO,EAAEA,CAAA,KAAMxB,gBAAgB,CAAC/C,WAAW,GAAG,CAAC,CAAE;YACjDwG,QAAQ,EAAExG,WAAW,KAAK,CAAE;YAC5BgE,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAC7B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERqC,KAAK,CAACC,IAAI,CAAC;YAAEvF,MAAM,EAAEyB;UAAW,CAAC,EAAE,CAAC+D,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAACxB,GAAG,CAAC9E,IAAI,iBACzD1B,OAAA,CAAChB,MAAM;YAEH2G,OAAO,EAAEA,CAAA,KAAMxB,gBAAgB,CAACzC,IAAI,CAAE;YACtC0D,SAAS,EAAG,kBAAiBhE,WAAW,KAAKM,IAAI,GAAG,QAAQ,GAAG,EAAG,EAAE;YAAAyD,QAAA,EAEnEzD;UAAI,GAJAA,IAAI;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKL,CACX,CAAC,eAEFxF,OAAA,CAAChB,MAAM;YACH2G,OAAO,EAAEA,CAAA,KAAMxB,gBAAgB,CAAC/C,WAAW,GAAG,CAAC,CAAE;YACjDwG,QAAQ,EAAExG,WAAW,KAAK4C,UAAW;YACrCoB,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAC7B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAApF,EAAA,CAtXKD,KAAK;EAAA,QAMQjB,IAAI,CAAC8B,OAAO,EACV3B,WAAW;AAAA;AAAA4I,EAAA,GAP1B9H,KAAK;AAwXX,eAAeA,KAAK;AAAC,IAAA8H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}