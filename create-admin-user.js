// Create admin user for testing
const mongoose = require("mongoose");
const bcryptjs = require("bcryptjs");
const User = require("./server/models/userModel");
require("dotenv").config({ path: './server/.env' });

const createAdminUser = async () => {
  try {
    console.log("🔗 Connecting to MongoDB...");
    await mongoose.connect(process.env.MONGO_URL);
    console.log("✅ Connected to MongoDB");

    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: "<EMAIL>" });
    
    if (existingAdmin) {
      console.log("👤 Admin user already exists:", existingAdmin.email);
      
      // Make sure they're admin
      if (!existingAdmin.isAdmin) {
        await User.findByIdAndUpdate(existingAdmin._id, { isAdmin: true });
        console.log("✅ Updated existing user to admin");
      }
      
      console.log("✅ Admin user ready:", existingAdmin.email);
      return;
    }

    // Create new admin user
    console.log("👤 Creating new admin user...");
    
    const hashedPassword = await bcryptjs.hash("admin123", 12);
    
    const adminUser = new User({
      firstName: "Admin",
      lastName: "User", 
      username: "admin",
      email: "<EMAIL>",
      password: hashedPassword,
      school: "Brainwave Admin",
      level: "primary",
      class: "1",
      phoneNumber: "0700000000",
      isAdmin: true,
      isBlocked: false,
      subscriptionStatus: "active",
      subscriptionPlan: "premium"
    });

    const savedUser = await adminUser.save();
    console.log("🎉 Admin user created successfully!");
    console.log("📧 Email:", savedUser.email);
    console.log("🔑 Password: admin123");
    console.log("👑 Admin status:", savedUser.isAdmin);

  } catch (error) {
    console.error("❌ Error creating admin user:", error.message);
    
    if (error.code === 11000) {
      console.log("💡 User might already exist with duplicate field");
    }
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
    process.exit(0);
  }
};

createAdminUser();
