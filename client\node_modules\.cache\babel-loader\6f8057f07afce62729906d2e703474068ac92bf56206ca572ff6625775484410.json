{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\StudyMaterials\\\\AddStudyMaterialForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Form, message, Select, Upload, Button } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addVideo, addNote, addPastPaper, addBook } from \"../../../apicalls/study\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport { FaUpload, FaVideo, FaFileAlt, FaBook, FaGraduationCap, FaCloudUploadAlt } from \"react-icons/fa\";\nimport \"./AddStudyMaterialForm.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nfunction AddStudyMaterialForm({\n  materialType,\n  onSuccess,\n  onCancel\n}) {\n  _s();\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const [loading, setLoading] = useState(false);\n  const [fileList, setFileList] = useState([]);\n  const [thumbnailList, setThumbnailList] = useState([]);\n  const [videoFileList, setVideoFileList] = useState([]);\n  const [uploadMethod, setUploadMethod] = useState(\"youtube\"); // \"youtube\", \"upload\", or \"s3url\"\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [uploadStatus, setUploadStatus] = useState(\"\"); // \"uploading\", \"processing\", \"complete\"\n  const [uploadSpeed, setUploadSpeed] = useState(0);\n  const [estimatedTime, setEstimatedTime] = useState(0);\n  const [uploadStartTime, setUploadStartTime] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n\n  // Get subjects based on level\n  const getSubjectsForLevel = level => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"primary_kiswahili\":\n        return primaryKiswahiliSubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = level => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"primary_kiswahili\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"secondary\":\n        return [\"1\", \"2\", \"3\", \"4\"];\n      case \"advance\":\n        return [\"5\", \"6\"];\n      default:\n        return [];\n    }\n  };\n  const [availableSubjects, setAvailableSubjects] = useState(primarySubjects);\n  const [availableClasses, setAvailableClasses] = useState([\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"]);\n  const [selectedAdditionalClasses, setSelectedAdditionalClasses] = useState([]);\n  const handleLevelChange = level => {\n    setAvailableSubjects(getSubjectsForLevel(level));\n    setAvailableClasses(getClassesForLevel(level));\n    setSelectedAdditionalClasses([]); // Reset additional classes when level changes\n    form.setFieldsValue({\n      subject: undefined,\n      className: undefined\n    });\n  };\n  const handleAdditionalClassesChange = classes => {\n    setSelectedAdditionalClasses(classes);\n  };\n  const handleCoreClassChange = value => {\n    // Remove the newly selected core class from additional classes if it's there\n    const filteredAdditionalClasses = selectedAdditionalClasses.filter(cls => cls !== value);\n    setSelectedAdditionalClasses(filteredAdditionalClasses);\n  };\n  const handleSubmit = async values => {\n    let timeoutId;\n    try {\n      setLoading(true);\n      setUploadProgress(0);\n      setUploadStatus(\"\");\n      dispatch(ShowLoading());\n\n      // Remove artificial timeout - let the actual API response control loading state\n\n      let response;\n      if (materialType === \"videos\") {\n        // Add additional classes to values for videos\n        const videoValues = {\n          ...values,\n          additionalClasses: selectedAdditionalClasses\n        };\n\n        // For videos, handle YouTube, S3 URL, and file upload methods\n        if (uploadMethod === \"youtube\") {\n          // Send JSON data for YouTube videos\n          setUploadStatus(\"Adding YouTube video...\");\n          response = await addVideo(videoValues);\n        } else if (uploadMethod === \"s3url\") {\n          // Handle S3 URL method with optional thumbnail upload\n          if (thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n            // If thumbnail is provided, create FormData to upload thumbnail\n            const formData = new FormData();\n\n            // Add form fields\n            Object.keys(videoValues).forEach(key => {\n              if (videoValues[key] !== undefined && videoValues[key] !== null) {\n                if (Array.isArray(videoValues[key])) {\n                  // Handle arrays (like additionalClasses)\n                  videoValues[key].forEach(item => formData.append(key, item));\n                } else {\n                  formData.append(key, videoValues[key]);\n                }\n              }\n            });\n\n            // Add thumbnail file\n            formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n            setUploadStatus(\"Uploading thumbnail and adding video...\");\n            response = await addVideo(formData);\n          } else {\n            // No thumbnail, send JSON data\n            setUploadStatus(\"Adding S3 video...\");\n            response = await addVideo(videoValues);\n          }\n        } else {\n          // Create FormData for video file upload\n          const formData = new FormData();\n\n          // Add form fields\n          Object.keys(videoValues).forEach(key => {\n            if (videoValues[key] !== undefined && videoValues[key] !== null) {\n              if (Array.isArray(videoValues[key])) {\n                // Handle arrays (like additionalClasses)\n                videoValues[key].forEach(item => formData.append(key, item));\n              } else {\n                formData.append(key, videoValues[key]);\n              }\n            }\n          });\n\n          // Add video file\n          if (videoFileList.length > 0 && videoFileList[0].originFileObj) {\n            formData.append(\"video\", videoFileList[0].originFileObj);\n            setUploadStatus(\"Uploading video file...\");\n          }\n\n          // Add thumbnail file if provided\n          if (thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n            console.log('📎 Adding thumbnail to upload:', thumbnailList[0].name);\n            formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n          }\n\n          // Upload with enhanced progress tracking\n          setUploadStartTime(Date.now());\n          response = await addVideo(formData, (progress, loaded, total) => {\n            setUploadProgress(progress);\n\n            // Calculate upload speed and estimated time\n            if (uploadStartTime) {\n              const elapsedTime = (Date.now() - uploadStartTime) / 1000; // seconds\n              const uploadedBytes = loaded || total * progress / 100;\n              const speed = uploadedBytes / elapsedTime; // bytes per second\n              const remainingBytes = total - uploadedBytes;\n              const estimatedSeconds = remainingBytes / speed;\n              setUploadSpeed(speed);\n              setEstimatedTime(estimatedSeconds);\n            }\n            if (progress === 100) {\n              setUploadStatus(\"Finalizing upload...\");\n            } else if (progress > 0) {\n              setUploadStatus(`Uploading... ${progress}%`);\n            }\n          });\n        }\n      } else {\n        // For other materials, create FormData\n        const formData = new FormData();\n\n        // Add form fields\n        Object.keys(values).forEach(key => {\n          if (values[key] !== undefined && values[key] !== null) {\n            formData.append(key, values[key]);\n          }\n        });\n\n        // Add files\n        if (fileList.length > 0 && fileList[0].originFileObj) {\n          formData.append(\"document\", fileList[0].originFileObj);\n        }\n        if (materialType === \"books\" && thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n          formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n        }\n\n        // Call appropriate API\n        switch (materialType) {\n          case \"study-notes\":\n            response = await addNote(formData);\n            break;\n          case \"past-papers\":\n            response = await addPastPaper(formData);\n            break;\n          case \"books\":\n            response = await addBook(formData);\n            break;\n          default:\n            throw new Error(\"Invalid material type\");\n        }\n      }\n      if (response && response.status === 201 && response.data.success) {\n        message.success(response.data.message);\n        form.resetFields();\n        setFileList([]);\n        setThumbnailList([]);\n        setVideoFileList([]);\n        setSelectedAdditionalClasses([]);\n        setUploadMethod(\"youtube\");\n        setUploadProgress(0);\n        setUploadStatus(\"\");\n        onSuccess(materialType);\n      } else if (response) {\n        var _response$data;\n        const errorMessage = ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || \"Failed to add material\";\n        message.error(errorMessage);\n      } else {\n        // Show a more helpful message for timeout issues\n        message.warning({\n          content: \"Request timed out, but material may have been added successfully. Please check the materials list to verify.\",\n          duration: 8\n        });\n        console.log(\"⚠️ Request timeout - material may have been added successfully\");\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3;\n      console.error(\"Error adding material:\", error);\n\n      // Handle authentication errors specifically\n      if (error.response && error.response.status === 401) {\n        message.error(\"Authentication failed. Please login again.\");\n        // Redirect to login\n        setTimeout(() => {\n          window.location.href = '/login';\n        }, 1000);\n        return;\n      }\n\n      // Provide specific error messages based on error type\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n        message.warning({\n          content: \"Request timed out. The material may have been added successfully. Please refresh the materials list to check.\",\n          duration: 10\n        });\n        console.log(\"⚠️ Timeout error - material may have been added successfully\");\n      } else if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 413) {\n        message.error(\"File too large. Please use a file smaller than 500MB.\");\n      } else if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 400) {\n        var _error$response$data;\n        message.error(((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"Invalid file or form data.\");\n      } else if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 500) {\n        message.error(\"Server error. Please try again later.\");\n      } else {\n        message.error(\"Upload failed. Please check your internet connection and try again.\");\n      }\n    } finally {\n      // Restore normal loading behavior - only hide loading when request actually completes\n      setLoading(false);\n      setUploadProgress(0);\n      setUploadStatus(\"\");\n      dispatch(HideLoading());\n    }\n  };\n  const uploadProps = {\n    beforeUpload: () => false,\n    // Prevent auto upload\n    maxCount: 1,\n    accept: materialType === \"videos\" ? undefined : \".pdf,.doc,.docx,.ppt,.pptx\"\n  };\n  const videoUploadProps = {\n    beforeUpload: () => false,\n    maxCount: 1,\n    accept: \"video/*\"\n  };\n  const thumbnailUploadProps = {\n    beforeUpload: () => false,\n    maxCount: 1,\n    accept: \"image/*\"\n  };\n  const getMaterialIcon = () => {\n    switch (materialType) {\n      case \"videos\":\n        return /*#__PURE__*/_jsxDEV(FaVideo, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 16\n        }, this);\n      case \"study-notes\":\n        return /*#__PURE__*/_jsxDEV(FaFileAlt, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 16\n        }, this);\n      case \"past-papers\":\n        return /*#__PURE__*/_jsxDEV(FaGraduationCap, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 16\n        }, this);\n      case \"books\":\n        return /*#__PURE__*/_jsxDEV(FaBook, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaFileAlt, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getMaterialTitle = () => {\n    switch (materialType) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return \"Material\";\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"add-material-form\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header-icon\",\n        children: [getMaterialIcon(), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Add New \", getMaterialTitle()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        initialValues: {\n          level: \"primary\",\n          className: \"\",\n          subject: \"\",\n          title: \"\",\n          year: \"\",\n          videoID: \"\",\n          videoUrl: \"\",\n          thumbnailUrl: \"\"\n        },\n        className: \"material-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"Level\",\n            name: \"level\",\n            rules: [{\n              required: true,\n              message: \"Please select a level\"\n            }],\n            className: \"form-item-half\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select level\",\n              onChange: handleLevelChange,\n              size: \"large\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"primary\",\n                children: \"Primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"secondary\",\n                children: \"Secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 371,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"advance\",\n                children: \"Advance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"Class\",\n            name: \"className\",\n            rules: [{\n              required: true,\n              message: \"Please select a class\"\n            }],\n            className: \"form-item-half\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select class\",\n              size: \"large\",\n              onChange: handleCoreClassChange,\n              children: availableClasses.map(cls => /*#__PURE__*/_jsxDEV(Option, {\n                value: cls,\n                children: cls\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 388,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"Subject\",\n          name: \"subject\",\n          rules: [{\n            required: true,\n            message: \"Please select a subject\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"Select subject\",\n            size: \"large\",\n            children: availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(Option, {\n              value: subject,\n              children: subject\n            }, subject, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), materialType === \"videos\" && /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"Additional Classes (Optional)\",\n          className: \"additional-classes-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"additional-classes-info\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Select additional classes that can access this video (besides the core class selected above)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            mode: \"multiple\",\n            placeholder: \"Select additional classes (optional)\",\n            size: \"large\",\n            value: selectedAdditionalClasses,\n            onChange: handleAdditionalClassesChange,\n            style: {\n              width: '100%'\n            },\n            children: availableClasses.filter(cls => cls !== form.getFieldValue('className')) // Exclude the core class\n            .map(cls => /*#__PURE__*/_jsxDEV(Option, {\n              value: cls,\n              children: cls\n            }, cls, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"additional-classes-note\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              children: \"Note: The video will be available to the core class and all selected additional classes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 407,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"Title\",\n          name: \"title\",\n          rules: [{\n            required: true,\n            message: \"Please enter a title\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: `Enter ${getMaterialTitle().toLowerCase()} title`,\n            className: \"form-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this), (materialType === \"past-papers\" || materialType === \"books\") && /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"Year\",\n          name: \"year\",\n          rules: [{\n            required: true,\n            message: \"Please enter the year\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Enter year (e.g., 2023)\",\n            className: \"form-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 13\n        }, this), materialType === \"videos\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"Upload Method\",\n            className: \"upload-method-section\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-method-selector\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `method-option ${uploadMethod === \"youtube\" ? \"active\" : \"\"}`,\n                onClick: () => setUploadMethod(\"youtube\"),\n                children: [/*#__PURE__*/_jsxDEV(FaVideo, {\n                  className: \"method-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"YouTube Video\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Add video using YouTube ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `method-option ${uploadMethod === \"s3url\" ? \"active\" : \"\"}`,\n                onClick: () => setUploadMethod(\"s3url\"),\n                children: [/*#__PURE__*/_jsxDEV(FaCloudUploadAlt, {\n                  className: \"method-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"S3 Object URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Add video using S3 bucket URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `method-option ${uploadMethod === \"upload\" ? \"active\" : \"\"}`,\n                onClick: () => setUploadMethod(\"upload\"),\n                children: [/*#__PURE__*/_jsxDEV(FaCloudUploadAlt, {\n                  className: \"method-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Upload Video File\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Upload video file to server\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 15\n          }, this), uploadMethod === \"youtube\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Video ID (YouTube)\",\n              name: \"videoID\",\n              rules: [{\n                required: uploadMethod === \"youtube\",\n                message: \"Please enter YouTube video ID\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Enter YouTube video ID (e.g., dQw4w9WgXcQ)\",\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Video URL (Optional)\",\n              name: \"videoUrl\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                placeholder: \"Enter video URL (optional)\",\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 508,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Thumbnail URL (Optional)\",\n              name: \"thumbnailUrl\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                placeholder: \"Enter thumbnail URL (optional)\",\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : uploadMethod === \"s3url\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"S3 Object URL\",\n              name: \"videoUrl\",\n              rules: [{\n                required: uploadMethod === \"s3url\",\n                message: \"Please enter S3 object URL\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                placeholder: \"Enter S3 object URL (e.g., https://your-bucket.s3.amazonaws.com/video.mp4)\",\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 537,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Drag & Drop Thumbnail (Optional)\",\n              className: \"upload-section\",\n              children: /*#__PURE__*/_jsxDEV(Upload, {\n                ...thumbnailUploadProps,\n                fileList: thumbnailList,\n                onChange: ({\n                  fileList\n                }) => setThumbnailList(fileList),\n                className: \"thumbnail-upload\",\n                onDrop: e => {\n                  e.preventDefault();\n                  setIsDragOver(false);\n                  const files = Array.from(e.dataTransfer.files);\n                  const imageFiles = files.filter(file => file.type.startsWith('image/'));\n                  if (imageFiles.length > 0) {\n                    const file = imageFiles[0];\n                    // Validate file size (5MB limit)\n                    if (file.size > 5 * 1024 * 1024) {\n                      message.error('Thumbnail file size must be less than 5MB');\n                      return;\n                    }\n                    setThumbnailList([{\n                      uid: '-1',\n                      name: file.name,\n                      status: 'done',\n                      originFileObj: file,\n                      url: URL.createObjectURL(file)\n                    }]);\n                    message.success('Thumbnail uploaded successfully!');\n                  } else {\n                    message.error('Please drop an image file (JPG, PNG, GIF)');\n                  }\n                },\n                onDragOver: e => {\n                  e.preventDefault();\n                  setIsDragOver(true);\n                },\n                onDragEnter: e => {\n                  e.preventDefault();\n                  setIsDragOver(true);\n                },\n                onDragLeave: e => {\n                  e.preventDefault();\n                  setIsDragOver(false);\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `upload-area small thumbnail-drop-zone ${isDragOver ? 'drag-over' : ''}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaUpload, {\n                    className: \"upload-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 591,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: isDragOver ? 'Drop thumbnail here!' : 'Drag & drop thumbnail or click to upload'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 592,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"upload-hint\",\n                    children: \"Auto-generated if not provided\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"upload-hint\",\n                    children: \"Supports JPG, PNG, GIF (Max: 5MB)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 548,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Upload Video File\",\n              className: \"upload-section\",\n              children: /*#__PURE__*/_jsxDEV(Upload, {\n                ...videoUploadProps,\n                fileList: videoFileList,\n                onChange: ({\n                  fileList\n                }) => setVideoFileList(fileList),\n                className: \"video-upload\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"upload-area\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCloudUploadAlt, {\n                    className: \"upload-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 612,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Click or drag video file to upload\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 613,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"upload-hint\",\n                    children: \"Supports MP4, AVI, MOV, WMV (Max: 500MB)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"upload-hint\",\n                    children: \"Large files may take several minutes to upload\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 601,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Upload Custom Thumbnail (Optional)\",\n              className: \"upload-section\",\n              children: /*#__PURE__*/_jsxDEV(Upload, {\n                ...thumbnailUploadProps,\n                fileList: thumbnailList,\n                onChange: ({\n                  fileList\n                }) => setThumbnailList(fileList),\n                className: \"thumbnail-upload\",\n                onDrop: e => {\n                  e.preventDefault();\n                  setIsDragOver(false);\n                  const files = Array.from(e.dataTransfer.files);\n                  const imageFiles = files.filter(file => file.type.startsWith('image/'));\n                  if (imageFiles.length > 0) {\n                    const file = imageFiles[0];\n                    // Validate file size (5MB limit)\n                    if (file.size > 5 * 1024 * 1024) {\n                      message.error('Thumbnail file size must be less than 5MB');\n                      return;\n                    }\n                    setThumbnailList([{\n                      uid: '-1',\n                      name: file.name,\n                      status: 'done',\n                      originFileObj: file,\n                      url: URL.createObjectURL(file)\n                    }]);\n                    message.success('Thumbnail uploaded successfully!');\n                  } else {\n                    message.error('Please drop an image file (JPG, PNG, GIF)');\n                  }\n                },\n                onDragOver: e => {\n                  e.preventDefault();\n                  setIsDragOver(true);\n                },\n                onDragEnter: e => {\n                  e.preventDefault();\n                  setIsDragOver(true);\n                },\n                onDragLeave: e => {\n                  e.preventDefault();\n                  setIsDragOver(false);\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `upload-area small thumbnail-drop-zone ${isDragOver ? 'drag-over' : ''}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaUpload, {\n                    className: \"upload-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 667,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: isDragOver ? 'Drop thumbnail here!' : 'Drag & drop thumbnail or click to upload'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 668,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"upload-hint\",\n                    children: \"Auto-generated if not provided\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 669,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"upload-hint\",\n                    children: \"Supports JPG, PNG, GIF (Max: 5MB)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 666,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 624,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 620,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true), materialType !== \"videos\" && /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: `Upload ${getMaterialTitle()} Document`,\n          className: \"upload-section\",\n          children: /*#__PURE__*/_jsxDEV(Upload, {\n            ...uploadProps,\n            fileList: fileList,\n            onChange: ({\n              fileList\n            }) => setFileList(fileList),\n            className: \"document-upload\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-area\",\n              children: [/*#__PURE__*/_jsxDEV(FaCloudUploadAlt, {\n                className: \"upload-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Click or drag file to upload\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 692,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"upload-hint\",\n                children: \"Supports PDF, DOC, DOCX, PPT, PPTX\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 680,\n          columnNumber: 13\n        }, this), materialType === \"books\" && /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"Upload Thumbnail (Optional)\",\n          className: \"upload-section\",\n          children: /*#__PURE__*/_jsxDEV(Upload, {\n            ...thumbnailUploadProps,\n            fileList: thumbnailList,\n            onChange: ({\n              fileList\n            }) => setThumbnailList(fileList),\n            className: \"thumbnail-upload\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-area small\",\n              children: [/*#__PURE__*/_jsxDEV(FaUpload, {\n                className: \"upload-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 711,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Upload book cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 712,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 710,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 704,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 700,\n          columnNumber: 13\n        }, this), loading && uploadMethod === \"upload\" && materialType === \"videos\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"upload-progress-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"progress-text\",\n                children: uploadStatus\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 723,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"progress-percentage\",\n                children: [uploadProgress, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 724,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 722,\n              columnNumber: 17\n            }, this), uploadSpeed > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-stats\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"upload-speed\",\n                children: [\"\\uD83D\\uDCCA \", (uploadSpeed / (1024 * 1024)).toFixed(2), \" MB/s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 728,\n                columnNumber: 21\n              }, this), estimatedTime > 0 && estimatedTime < 3600 && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"estimated-time\",\n                children: [\"\\u23F1\\uFE0F \", Math.ceil(estimatedTime), \"s remaining\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 732,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 727,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 721,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-fill\",\n              style: {\n                width: `${uploadProgress}%`,\n                transition: 'width 0.3s ease'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 740,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-details\",\n            children: uploadProgress < 100 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"uploading-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83D\\uDCE4 Uploading video file to server...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                children: \"Please keep this tab open until upload completes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 754,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"processing-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83C\\uDFAC Upload complete! Processing video and generating thumbnail...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                children: \"This may take a few moments for large files\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 759,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 757,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 720,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"default\",\n            onClick: onCancel,\n            size: \"large\",\n            className: \"cancel-btn\",\n            disabled: loading,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            loading: loading,\n            size: \"large\",\n            className: \"submit-btn\",\n            children: loading ? uploadMethod === \"upload\" && materialType === \"videos\" ? \"Uploading...\" : \"Adding...\" : `Add ${getMaterialTitle()}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 776,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 335,\n    columnNumber: 5\n  }, this);\n}\n_s(AddStudyMaterialForm, \"7XbTZLh/J6BxbgycpDOggpxju3g=\", false, function () {\n  return [Form.useForm, useDispatch];\n});\n_c = AddStudyMaterialForm;\nexport default AddStudyMaterialForm;\nvar _c;\n$RefreshReg$(_c, \"AddStudyMaterialForm\");", "map": {"version": 3, "names": ["React", "useState", "Form", "message", "Select", "Upload", "<PERSON><PERSON>", "useDispatch", "HideLoading", "ShowLoading", "addVideo", "addNote", "addPastPaper", "addBook", "primarySubjects", "primaryKiswahiliSubjects", "secondarySubjects", "advanceSubjects", "FaUpload", "FaVideo", "FaFileAlt", "FaBook", "FaGraduationCap", "FaCloudUploadAlt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "AddStudyMaterialForm", "materialType", "onSuccess", "onCancel", "_s", "form", "useForm", "dispatch", "loading", "setLoading", "fileList", "setFileList", "thumbnailList", "setThumbnailList", "videoFileList", "setVideoFileList", "uploadMethod", "setUploadMethod", "uploadProgress", "setUploadProgress", "uploadStatus", "setUploadStatus", "uploadSpeed", "setUploadSpeed", "estimatedTime", "setEstimatedTime", "uploadStartTime", "setUploadStartTime", "isDragOver", "setIsDragOver", "getSubjectsForLevel", "level", "getClassesForLevel", "availableSubjects", "setAvailableSubjects", "availableClasses", "setAvailableClasses", "selectedAdditionalClasses", "setSelectedAdditionalClasses", "handleLevelChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subject", "undefined", "className", "handleAdditionalClassesChange", "classes", "handleCoreClassChange", "value", "filteredAdditionalClasses", "filter", "cls", "handleSubmit", "values", "timeoutId", "response", "videoValues", "additionalClasses", "length", "originFileObj", "formData", "FormData", "Object", "keys", "for<PERSON>ach", "key", "Array", "isArray", "item", "append", "console", "log", "name", "Date", "now", "progress", "loaded", "total", "elapsedTime", "uploadedBytes", "speed", "remainingBytes", "estimatedSeconds", "Error", "status", "data", "success", "resetFields", "_response$data", "errorMessage", "error", "warning", "content", "duration", "_error$response", "_error$response2", "_error$response3", "setTimeout", "window", "location", "href", "code", "includes", "_error$response$data", "uploadProps", "beforeUpload", "maxCount", "accept", "videoUploadProps", "thumbnailUploadProps", "getMaterialIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getMaterialTitle", "children", "layout", "onFinish", "initialValues", "title", "year", "videoID", "videoUrl", "thumbnailUrl", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "onChange", "size", "map", "mode", "style", "width", "getFieldValue", "type", "toLowerCase", "onClick", "onDrop", "e", "preventDefault", "files", "from", "dataTransfer", "imageFiles", "file", "startsWith", "uid", "url", "URL", "createObjectURL", "onDragOver", "onDragEnter", "onDragLeave", "toFixed", "Math", "ceil", "transition", "disabled", "htmlType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/StudyMaterials/AddStudyMaterialForm.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { Form, message, Select, Upload, Button } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addVideo, addNote, addPastPaper, addBook } from \"../../../apicalls/study\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport {\n  FaUpload,\n  FaVideo,\n  FaFileAlt,\n  FaBook,\n  FaGraduationCap,\n  FaCloudUploadAlt\n} from \"react-icons/fa\";\nimport \"./AddStudyMaterialForm.css\";\n\nconst { Option } = Select;\n\nfunction AddStudyMaterialForm({ materialType, onSuccess, onCancel }) {\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const [loading, setLoading] = useState(false);\n  const [fileList, setFileList] = useState([]);\n  const [thumbnailList, setThumbnailList] = useState([]);\n  const [videoFileList, setVideoFileList] = useState([]);\n  const [uploadMethod, setUploadMethod] = useState(\"youtube\"); // \"youtube\", \"upload\", or \"s3url\"\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [uploadStatus, setUploadStatus] = useState(\"\"); // \"uploading\", \"processing\", \"complete\"\n  const [uploadSpeed, setUploadSpeed] = useState(0);\n  const [estimatedTime, setEstimatedTime] = useState(0);\n  const [uploadStartTime, setUploadStartTime] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n\n  // Get subjects based on level\n  const getSubjectsForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"primary_kiswahili\":\n        return primaryKiswahiliSubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"primary_kiswahili\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"secondary\":\n        return [\"1\", \"2\", \"3\", \"4\"];\n      case \"advance\":\n        return [\"5\", \"6\"];\n      default:\n        return [];\n    }\n  };\n\n  const [availableSubjects, setAvailableSubjects] = useState(primarySubjects);\n  const [availableClasses, setAvailableClasses] = useState([\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"]);\n  const [selectedAdditionalClasses, setSelectedAdditionalClasses] = useState([]);\n\n  const handleLevelChange = (level) => {\n    setAvailableSubjects(getSubjectsForLevel(level));\n    setAvailableClasses(getClassesForLevel(level));\n    setSelectedAdditionalClasses([]); // Reset additional classes when level changes\n    form.setFieldsValue({ subject: undefined, className: undefined });\n  };\n\n  const handleAdditionalClassesChange = (classes) => {\n    setSelectedAdditionalClasses(classes);\n  };\n\n  const handleCoreClassChange = (value) => {\n    // Remove the newly selected core class from additional classes if it's there\n    const filteredAdditionalClasses = selectedAdditionalClasses.filter(cls => cls !== value);\n    setSelectedAdditionalClasses(filteredAdditionalClasses);\n  };\n\n  const handleSubmit = async (values) => {\n    let timeoutId;\n    try {\n      setLoading(true);\n      setUploadProgress(0);\n      setUploadStatus(\"\");\n      dispatch(ShowLoading());\n\n      // Remove artificial timeout - let the actual API response control loading state\n\n      let response;\n\n      if (materialType === \"videos\") {\n        // Add additional classes to values for videos\n        const videoValues = {\n          ...values,\n          additionalClasses: selectedAdditionalClasses\n        };\n\n        // For videos, handle YouTube, S3 URL, and file upload methods\n        if (uploadMethod === \"youtube\") {\n          // Send JSON data for YouTube videos\n          setUploadStatus(\"Adding YouTube video...\");\n          response = await addVideo(videoValues);\n        } else if (uploadMethod === \"s3url\") {\n          // Handle S3 URL method with optional thumbnail upload\n          if (thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n            // If thumbnail is provided, create FormData to upload thumbnail\n            const formData = new FormData();\n\n            // Add form fields\n            Object.keys(videoValues).forEach(key => {\n              if (videoValues[key] !== undefined && videoValues[key] !== null) {\n                if (Array.isArray(videoValues[key])) {\n                  // Handle arrays (like additionalClasses)\n                  videoValues[key].forEach(item => formData.append(key, item));\n                } else {\n                  formData.append(key, videoValues[key]);\n                }\n              }\n            });\n\n            // Add thumbnail file\n            formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n            setUploadStatus(\"Uploading thumbnail and adding video...\");\n            response = await addVideo(formData);\n          } else {\n            // No thumbnail, send JSON data\n            setUploadStatus(\"Adding S3 video...\");\n            response = await addVideo(videoValues);\n          }\n        } else {\n          // Create FormData for video file upload\n          const formData = new FormData();\n\n          // Add form fields\n          Object.keys(videoValues).forEach(key => {\n            if (videoValues[key] !== undefined && videoValues[key] !== null) {\n              if (Array.isArray(videoValues[key])) {\n                // Handle arrays (like additionalClasses)\n                videoValues[key].forEach(item => formData.append(key, item));\n              } else {\n                formData.append(key, videoValues[key]);\n              }\n            }\n          });\n\n          // Add video file\n          if (videoFileList.length > 0 && videoFileList[0].originFileObj) {\n            formData.append(\"video\", videoFileList[0].originFileObj);\n            setUploadStatus(\"Uploading video file...\");\n          }\n\n          // Add thumbnail file if provided\n          if (thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n            console.log('📎 Adding thumbnail to upload:', thumbnailList[0].name);\n            formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n          }\n\n          // Upload with enhanced progress tracking\n          setUploadStartTime(Date.now());\n          response = await addVideo(formData, (progress, loaded, total) => {\n            setUploadProgress(progress);\n\n            // Calculate upload speed and estimated time\n            if (uploadStartTime) {\n              const elapsedTime = (Date.now() - uploadStartTime) / 1000; // seconds\n              const uploadedBytes = loaded || (total * progress / 100);\n              const speed = uploadedBytes / elapsedTime; // bytes per second\n              const remainingBytes = total - uploadedBytes;\n              const estimatedSeconds = remainingBytes / speed;\n\n              setUploadSpeed(speed);\n              setEstimatedTime(estimatedSeconds);\n            }\n\n            if (progress === 100) {\n              setUploadStatus(\"Finalizing upload...\");\n            } else if (progress > 0) {\n              setUploadStatus(`Uploading... ${progress}%`);\n            }\n          });\n        }\n      } else {\n        // For other materials, create FormData\n        const formData = new FormData();\n        \n        // Add form fields\n        Object.keys(values).forEach(key => {\n          if (values[key] !== undefined && values[key] !== null) {\n            formData.append(key, values[key]);\n          }\n        });\n\n        // Add files\n        if (fileList.length > 0 && fileList[0].originFileObj) {\n          formData.append(\"document\", fileList[0].originFileObj);\n        }\n\n        if (materialType === \"books\" && thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n          formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n        }\n\n        // Call appropriate API\n        switch (materialType) {\n          case \"study-notes\":\n            response = await addNote(formData);\n            break;\n          case \"past-papers\":\n            response = await addPastPaper(formData);\n            break;\n          case \"books\":\n            response = await addBook(formData);\n            break;\n          default:\n            throw new Error(\"Invalid material type\");\n        }\n      }\n\n      if (response && response.status === 201 && response.data.success) {\n        message.success(response.data.message);\n        form.resetFields();\n        setFileList([]);\n        setThumbnailList([]);\n        setVideoFileList([]);\n        setSelectedAdditionalClasses([]);\n        setUploadMethod(\"youtube\");\n        setUploadProgress(0);\n        setUploadStatus(\"\");\n        onSuccess(materialType);\n      } else if (response) {\n        const errorMessage = response.data?.message || \"Failed to add material\";\n        message.error(errorMessage);\n      } else {\n        // Show a more helpful message for timeout issues\n        message.warning({\n          content: \"Request timed out, but material may have been added successfully. Please check the materials list to verify.\",\n          duration: 8,\n        });\n        console.log(\"⚠️ Request timeout - material may have been added successfully\");\n      }\n    } catch (error) {\n      console.error(\"Error adding material:\", error);\n\n      // Handle authentication errors specifically\n      if (error.response && error.response.status === 401) {\n        message.error(\"Authentication failed. Please login again.\");\n        // Redirect to login\n        setTimeout(() => {\n          window.location.href = '/login';\n        }, 1000);\n        return;\n      }\n\n      // Provide specific error messages based on error type\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n        message.warning({\n          content: \"Request timed out. The material may have been added successfully. Please refresh the materials list to check.\",\n          duration: 10,\n        });\n        console.log(\"⚠️ Timeout error - material may have been added successfully\");\n      } else if (error.response?.status === 413) {\n        message.error(\"File too large. Please use a file smaller than 500MB.\");\n      } else if (error.response?.status === 400) {\n        message.error(error.response.data?.message || \"Invalid file or form data.\");\n      } else if (error.response?.status === 500) {\n        message.error(\"Server error. Please try again later.\");\n      } else {\n        message.error(\"Upload failed. Please check your internet connection and try again.\");\n      }\n    } finally {\n      // Restore normal loading behavior - only hide loading when request actually completes\n      setLoading(false);\n      setUploadProgress(0);\n      setUploadStatus(\"\");\n      dispatch(HideLoading());\n    }\n  };\n\n  const uploadProps = {\n    beforeUpload: () => false, // Prevent auto upload\n    maxCount: 1,\n    accept: materialType === \"videos\" ? undefined : \".pdf,.doc,.docx,.ppt,.pptx\",\n  };\n\n  const videoUploadProps = {\n    beforeUpload: () => false,\n    maxCount: 1,\n    accept: \"video/*\",\n  };\n\n  const thumbnailUploadProps = {\n    beforeUpload: () => false,\n    maxCount: 1,\n    accept: \"image/*\",\n  };\n\n  const getMaterialIcon = () => {\n    switch (materialType) {\n      case \"videos\":\n        return <FaVideo />;\n      case \"study-notes\":\n        return <FaFileAlt />;\n      case \"past-papers\":\n        return <FaGraduationCap />;\n      case \"books\":\n        return <FaBook />;\n      default:\n        return <FaFileAlt />;\n    }\n  };\n\n  const getMaterialTitle = () => {\n    switch (materialType) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return \"Material\";\n    }\n  };\n\n  return (\n    <div className=\"add-material-form\">\n      <div className=\"form-card\">\n        <div className=\"form-header-icon\">\n          {getMaterialIcon()}\n          <h3>Add New {getMaterialTitle()}</h3>\n        </div>\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          initialValues={{\n            level: \"primary\",\n            className: \"\",\n            subject: \"\",\n            title: \"\",\n            year: \"\",\n            videoID: \"\",\n            videoUrl: \"\",\n            thumbnailUrl: \"\"\n          }}\n          className=\"material-form\"\n        >\n          <div className=\"form-row\">\n            <Form.Item\n              label=\"Level\"\n              name=\"level\"\n              rules={[{ required: true, message: \"Please select a level\" }]}\n              className=\"form-item-half\"\n            >\n              <Select\n                placeholder=\"Select level\"\n                onChange={handleLevelChange}\n                size=\"large\"\n              >\n                <Option value=\"primary\">Primary</Option>\n                <Option value=\"secondary\">Secondary</Option>\n                <Option value=\"advance\">Advance</Option>\n              </Select>\n            </Form.Item>\n\n            <Form.Item\n              label=\"Class\"\n              name=\"className\"\n              rules={[{ required: true, message: \"Please select a class\" }]}\n              className=\"form-item-half\"\n            >\n              <Select\n                placeholder=\"Select class\"\n                size=\"large\"\n                onChange={handleCoreClassChange}\n              >\n                {availableClasses.map(cls => (\n                  <Option key={cls} value={cls}>{cls}</Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </div>\n\n          <Form.Item\n            label=\"Subject\"\n            name=\"subject\"\n            rules={[{ required: true, message: \"Please select a subject\" }]}\n          >\n            <Select placeholder=\"Select subject\" size=\"large\">\n              {availableSubjects.map(subject => (\n                <Option key={subject} value={subject}>{subject}</Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          {materialType === \"videos\" && (\n            <Form.Item\n              label=\"Additional Classes (Optional)\"\n              className=\"additional-classes-section\"\n            >\n              <div className=\"additional-classes-info\">\n                <p>Select additional classes that can access this video (besides the core class selected above)</p>\n              </div>\n              <Select\n                mode=\"multiple\"\n                placeholder=\"Select additional classes (optional)\"\n                size=\"large\"\n                value={selectedAdditionalClasses}\n                onChange={handleAdditionalClassesChange}\n                style={{ width: '100%' }}\n              >\n                {availableClasses\n                  .filter(cls => cls !== form.getFieldValue('className')) // Exclude the core class\n                  .map(cls => (\n                    <Option key={cls} value={cls}>{cls}</Option>\n                  ))}\n              </Select>\n              <div className=\"additional-classes-note\">\n                <small>Note: The video will be available to the core class and all selected additional classes</small>\n              </div>\n            </Form.Item>\n          )}\n\n          <Form.Item\n            label=\"Title\"\n            name=\"title\"\n            rules={[{ required: true, message: \"Please enter a title\" }]}\n          >\n            <input\n              type=\"text\"\n              placeholder={`Enter ${getMaterialTitle().toLowerCase()} title`}\n              className=\"form-input\"\n            />\n          </Form.Item>\n\n          {(materialType === \"past-papers\" || materialType === \"books\") && (\n            <Form.Item\n              label=\"Year\"\n              name=\"year\"\n              rules={[{ required: true, message: \"Please enter the year\" }]}\n            >\n              <input\n                type=\"text\"\n                placeholder=\"Enter year (e.g., 2023)\"\n                className=\"form-input\"\n              />\n            </Form.Item>\n          )}\n\n          {materialType === \"videos\" && (\n            <>\n              <Form.Item\n                label=\"Upload Method\"\n                className=\"upload-method-section\"\n              >\n                <div className=\"upload-method-selector\">\n                  <div\n                    className={`method-option ${uploadMethod === \"youtube\" ? \"active\" : \"\"}`}\n                    onClick={() => setUploadMethod(\"youtube\")}\n                  >\n                    <FaVideo className=\"method-icon\" />\n                    <span>YouTube Video</span>\n                    <p>Add video using YouTube ID</p>\n                  </div>\n                  <div\n                    className={`method-option ${uploadMethod === \"s3url\" ? \"active\" : \"\"}`}\n                    onClick={() => setUploadMethod(\"s3url\")}\n                  >\n                    <FaCloudUploadAlt className=\"method-icon\" />\n                    <span>S3 Object URL</span>\n                    <p>Add video using S3 bucket URL</p>\n                  </div>\n                  <div\n                    className={`method-option ${uploadMethod === \"upload\" ? \"active\" : \"\"}`}\n                    onClick={() => setUploadMethod(\"upload\")}\n                  >\n                    <FaCloudUploadAlt className=\"method-icon\" />\n                    <span>Upload Video File</span>\n                    <p>Upload video file to server</p>\n                  </div>\n                </div>\n              </Form.Item>\n\n              {uploadMethod === \"youtube\" ? (\n                <>\n                  <Form.Item\n                    label=\"Video ID (YouTube)\"\n                    name=\"videoID\"\n                    rules={[{ required: uploadMethod === \"youtube\", message: \"Please enter YouTube video ID\" }]}\n                  >\n                    <input\n                      type=\"text\"\n                      placeholder=\"Enter YouTube video ID (e.g., dQw4w9WgXcQ)\"\n                      className=\"form-input\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"Video URL (Optional)\"\n                    name=\"videoUrl\"\n                  >\n                    <input\n                      type=\"url\"\n                      placeholder=\"Enter video URL (optional)\"\n                      className=\"form-input\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"Thumbnail URL (Optional)\"\n                    name=\"thumbnailUrl\"\n                  >\n                    <input\n                      type=\"url\"\n                      placeholder=\"Enter thumbnail URL (optional)\"\n                      className=\"form-input\"\n                    />\n                  </Form.Item>\n                </>\n              ) : uploadMethod === \"s3url\" ? (\n                <>\n                  <Form.Item\n                    label=\"S3 Object URL\"\n                    name=\"videoUrl\"\n                    rules={[{ required: uploadMethod === \"s3url\", message: \"Please enter S3 object URL\" }]}\n                  >\n                    <input\n                      type=\"url\"\n                      placeholder=\"Enter S3 object URL (e.g., https://your-bucket.s3.amazonaws.com/video.mp4)\"\n                      className=\"form-input\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"Drag & Drop Thumbnail (Optional)\"\n                    className=\"upload-section\"\n                  >\n                    <Upload\n                      {...thumbnailUploadProps}\n                      fileList={thumbnailList}\n                      onChange={({ fileList }) => setThumbnailList(fileList)}\n                      className=\"thumbnail-upload\"\n                      onDrop={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(false);\n                        const files = Array.from(e.dataTransfer.files);\n                        const imageFiles = files.filter(file => file.type.startsWith('image/'));\n                        if (imageFiles.length > 0) {\n                          const file = imageFiles[0];\n                          // Validate file size (5MB limit)\n                          if (file.size > 5 * 1024 * 1024) {\n                            message.error('Thumbnail file size must be less than 5MB');\n                            return;\n                          }\n                          setThumbnailList([{\n                            uid: '-1',\n                            name: file.name,\n                            status: 'done',\n                            originFileObj: file,\n                            url: URL.createObjectURL(file)\n                          }]);\n                          message.success('Thumbnail uploaded successfully!');\n                        } else {\n                          message.error('Please drop an image file (JPG, PNG, GIF)');\n                        }\n                      }}\n                      onDragOver={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(true);\n                      }}\n                      onDragEnter={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(true);\n                      }}\n                      onDragLeave={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(false);\n                      }}\n                    >\n                      <div className={`upload-area small thumbnail-drop-zone ${isDragOver ? 'drag-over' : ''}`}>\n                        <FaUpload className=\"upload-icon\" />\n                        <p>{isDragOver ? 'Drop thumbnail here!' : 'Drag & drop thumbnail or click to upload'}</p>\n                        <p className=\"upload-hint\">Auto-generated if not provided</p>\n                        <p className=\"upload-hint\">Supports JPG, PNG, GIF (Max: 5MB)</p>\n                      </div>\n                    </Upload>\n                  </Form.Item>\n                </>\n              ) : (\n                <>\n                  <Form.Item\n                    label=\"Upload Video File\"\n                    className=\"upload-section\"\n                  >\n                    <Upload\n                      {...videoUploadProps}\n                      fileList={videoFileList}\n                      onChange={({ fileList }) => setVideoFileList(fileList)}\n                      className=\"video-upload\"\n                    >\n                      <div className=\"upload-area\">\n                        <FaCloudUploadAlt className=\"upload-icon\" />\n                        <p>Click or drag video file to upload</p>\n                        <p className=\"upload-hint\">Supports MP4, AVI, MOV, WMV (Max: 500MB)</p>\n                        <p className=\"upload-hint\">Large files may take several minutes to upload</p>\n                      </div>\n                    </Upload>\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"Upload Custom Thumbnail (Optional)\"\n                    className=\"upload-section\"\n                  >\n                    <Upload\n                      {...thumbnailUploadProps}\n                      fileList={thumbnailList}\n                      onChange={({ fileList }) => setThumbnailList(fileList)}\n                      className=\"thumbnail-upload\"\n                      onDrop={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(false);\n                        const files = Array.from(e.dataTransfer.files);\n                        const imageFiles = files.filter(file => file.type.startsWith('image/'));\n                        if (imageFiles.length > 0) {\n                          const file = imageFiles[0];\n                          // Validate file size (5MB limit)\n                          if (file.size > 5 * 1024 * 1024) {\n                            message.error('Thumbnail file size must be less than 5MB');\n                            return;\n                          }\n                          setThumbnailList([{\n                            uid: '-1',\n                            name: file.name,\n                            status: 'done',\n                            originFileObj: file,\n                            url: URL.createObjectURL(file)\n                          }]);\n                          message.success('Thumbnail uploaded successfully!');\n                        } else {\n                          message.error('Please drop an image file (JPG, PNG, GIF)');\n                        }\n                      }}\n                      onDragOver={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(true);\n                      }}\n                      onDragEnter={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(true);\n                      }}\n                      onDragLeave={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(false);\n                      }}\n                    >\n                      <div className={`upload-area small thumbnail-drop-zone ${isDragOver ? 'drag-over' : ''}`}>\n                        <FaUpload className=\"upload-icon\" />\n                        <p>{isDragOver ? 'Drop thumbnail here!' : 'Drag & drop thumbnail or click to upload'}</p>\n                        <p className=\"upload-hint\">Auto-generated if not provided</p>\n                        <p className=\"upload-hint\">Supports JPG, PNG, GIF (Max: 5MB)</p>\n                      </div>\n                    </Upload>\n                  </Form.Item>\n                </>\n              )}\n            </>\n          )}\n\n          {materialType !== \"videos\" && (\n            <Form.Item\n              label={`Upload ${getMaterialTitle()} Document`}\n              className=\"upload-section\"\n            >\n              <Upload\n                {...uploadProps}\n                fileList={fileList}\n                onChange={({ fileList }) => setFileList(fileList)}\n                className=\"document-upload\"\n              >\n                <div className=\"upload-area\">\n                  <FaCloudUploadAlt className=\"upload-icon\" />\n                  <p>Click or drag file to upload</p>\n                  <p className=\"upload-hint\">Supports PDF, DOC, DOCX, PPT, PPTX</p>\n                </div>\n              </Upload>\n            </Form.Item>\n          )}\n\n          {materialType === \"books\" && (\n            <Form.Item\n              label=\"Upload Thumbnail (Optional)\"\n              className=\"upload-section\"\n            >\n              <Upload\n                {...thumbnailUploadProps}\n                fileList={thumbnailList}\n                onChange={({ fileList }) => setThumbnailList(fileList)}\n                className=\"thumbnail-upload\"\n              >\n                <div className=\"upload-area small\">\n                  <FaUpload className=\"upload-icon\" />\n                  <p>Upload book cover</p>\n                </div>\n              </Upload>\n            </Form.Item>\n          )}\n\n          {/* Enhanced Upload Progress Indicator */}\n          {loading && uploadMethod === \"upload\" && materialType === \"videos\" && (\n            <div className=\"upload-progress-section\">\n              <div className=\"progress-header\">\n                <div className=\"progress-info\">\n                  <span className=\"progress-text\">{uploadStatus}</span>\n                  <span className=\"progress-percentage\">{uploadProgress}%</span>\n                </div>\n                {uploadSpeed > 0 && (\n                  <div className=\"upload-stats\">\n                    <span className=\"upload-speed\">\n                      📊 {(uploadSpeed / (1024 * 1024)).toFixed(2)} MB/s\n                    </span>\n                    {estimatedTime > 0 && estimatedTime < 3600 && (\n                      <span className=\"estimated-time\">\n                        ⏱️ {Math.ceil(estimatedTime)}s remaining\n                      </span>\n                    )}\n                  </div>\n                )}\n              </div>\n\n              <div className=\"progress-bar\">\n                <div\n                  className=\"progress-fill\"\n                  style={{\n                    width: `${uploadProgress}%`,\n                    transition: 'width 0.3s ease'\n                  }}\n                ></div>\n              </div>\n\n              <div className=\"progress-details\">\n                {uploadProgress < 100 ? (\n                  <div className=\"uploading-info\">\n                    <span>📤 Uploading video file to server...</span>\n                    <small>Please keep this tab open until upload completes</small>\n                  </div>\n                ) : (\n                  <div className=\"processing-info\">\n                    <span>🎬 Upload complete! Processing video and generating thumbnail...</span>\n                    <small>This may take a few moments for large files</small>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          <div className=\"form-actions\">\n            <Button\n              type=\"default\"\n              onClick={onCancel}\n              size=\"large\"\n              className=\"cancel-btn\"\n              disabled={loading}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              size=\"large\"\n              className=\"submit-btn\"\n            >\n              {loading ? (\n                uploadMethod === \"upload\" && materialType === \"videos\" ?\n                \"Uploading...\" : \"Adding...\"\n              ) : (\n                `Add ${getMaterialTitle()}`\n              )}\n            </Button>\n          </div>\n        </Form>\n      </div>\n    </div>\n  );\n}\n\nexport default AddStudyMaterialForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,QAAQ,MAAM;AAC5D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,QAAQ,EAAEC,OAAO,EAAEC,YAAY,EAAEC,OAAO,QAAQ,yBAAyB;AAClF,SAASC,eAAe,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,wBAAwB;AACtH,SACEC,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTC,MAAM,EACNC,eAAe,EACfC,gBAAgB,QACX,gBAAgB;AACvB,OAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAM;EAAEC;AAAO,CAAC,GAAGxB,MAAM;AAEzB,SAASyB,oBAAoBA,CAAC;EAAEC,YAAY;EAAEC,SAAS;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACnE,MAAM,CAACC,IAAI,CAAC,GAAGhC,IAAI,CAACiC,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EAC7D,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM0D,mBAAmB,GAAIC,KAAK,IAAK;IACrC,QAAQA,KAAK;MACX,KAAK,SAAS;QACZ,OAAO9C,eAAe;MACxB,KAAK,mBAAmB;QACtB,OAAOC,wBAAwB;MACjC,KAAK,WAAW;QACd,OAAOC,iBAAiB;MAC1B,KAAK,SAAS;QACZ,OAAOC,eAAe;MACxB;QACE,OAAO,EAAE;IACb;EACF,CAAC;;EAED;EACA,MAAM4C,kBAAkB,GAAID,KAAK,IAAK;IACpC,QAAQA,KAAK;MACX,KAAK,SAAS;QACZ,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC5C,KAAK,mBAAmB;QACtB,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC5C,KAAK,WAAW;QACd,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC7B,KAAK,SAAS;QACZ,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;MACnB;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,MAAM,CAACE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9D,QAAQ,CAACa,eAAe,CAAC;EAC3E,MAAM,CAACkD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhE,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;EAC7F,MAAM,CAACiE,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAE9E,MAAMmE,iBAAiB,GAAIR,KAAK,IAAK;IACnCG,oBAAoB,CAACJ,mBAAmB,CAACC,KAAK,CAAC,CAAC;IAChDK,mBAAmB,CAACJ,kBAAkB,CAACD,KAAK,CAAC,CAAC;IAC9CO,4BAA4B,CAAC,EAAE,CAAC,CAAC,CAAC;IAClCjC,IAAI,CAACmC,cAAc,CAAC;MAAEC,OAAO,EAAEC,SAAS;MAAEC,SAAS,EAAED;IAAU,CAAC,CAAC;EACnE,CAAC;EAED,MAAME,6BAA6B,GAAIC,OAAO,IAAK;IACjDP,4BAA4B,CAACO,OAAO,CAAC;EACvC,CAAC;EAED,MAAMC,qBAAqB,GAAIC,KAAK,IAAK;IACvC;IACA,MAAMC,yBAAyB,GAAGX,yBAAyB,CAACY,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAKH,KAAK,CAAC;IACxFT,4BAA4B,CAACU,yBAAyB,CAAC;EACzD,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAIC,SAAS;IACb,IAAI;MACF5C,UAAU,CAAC,IAAI,CAAC;MAChBU,iBAAiB,CAAC,CAAC,CAAC;MACpBE,eAAe,CAAC,EAAE,CAAC;MACnBd,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;;MAEvB;;MAEA,IAAI0E,QAAQ;MAEZ,IAAIrD,YAAY,KAAK,QAAQ,EAAE;QAC7B;QACA,MAAMsD,WAAW,GAAG;UAClB,GAAGH,MAAM;UACTI,iBAAiB,EAAEnB;QACrB,CAAC;;QAED;QACA,IAAIrB,YAAY,KAAK,SAAS,EAAE;UAC9B;UACAK,eAAe,CAAC,yBAAyB,CAAC;UAC1CiC,QAAQ,GAAG,MAAMzE,QAAQ,CAAC0E,WAAW,CAAC;QACxC,CAAC,MAAM,IAAIvC,YAAY,KAAK,OAAO,EAAE;UACnC;UACA,IAAIJ,aAAa,CAAC6C,MAAM,GAAG,CAAC,IAAI7C,aAAa,CAAC,CAAC,CAAC,CAAC8C,aAAa,EAAE;YAC9D;YACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;YAE/B;YACAC,MAAM,CAACC,IAAI,CAACP,WAAW,CAAC,CAACQ,OAAO,CAACC,GAAG,IAAI;cACtC,IAAIT,WAAW,CAACS,GAAG,CAAC,KAAKtB,SAAS,IAAIa,WAAW,CAACS,GAAG,CAAC,KAAK,IAAI,EAAE;gBAC/D,IAAIC,KAAK,CAACC,OAAO,CAACX,WAAW,CAACS,GAAG,CAAC,CAAC,EAAE;kBACnC;kBACAT,WAAW,CAACS,GAAG,CAAC,CAACD,OAAO,CAACI,IAAI,IAAIR,QAAQ,CAACS,MAAM,CAACJ,GAAG,EAAEG,IAAI,CAAC,CAAC;gBAC9D,CAAC,MAAM;kBACLR,QAAQ,CAACS,MAAM,CAACJ,GAAG,EAAET,WAAW,CAACS,GAAG,CAAC,CAAC;gBACxC;cACF;YACF,CAAC,CAAC;;YAEF;YACAL,QAAQ,CAACS,MAAM,CAAC,WAAW,EAAExD,aAAa,CAAC,CAAC,CAAC,CAAC8C,aAAa,CAAC;YAC5DrC,eAAe,CAAC,yCAAyC,CAAC;YAC1DiC,QAAQ,GAAG,MAAMzE,QAAQ,CAAC8E,QAAQ,CAAC;UACrC,CAAC,MAAM;YACL;YACAtC,eAAe,CAAC,oBAAoB,CAAC;YACrCiC,QAAQ,GAAG,MAAMzE,QAAQ,CAAC0E,WAAW,CAAC;UACxC;QACF,CAAC,MAAM;UACL;UACA,MAAMI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;UAE/B;UACAC,MAAM,CAACC,IAAI,CAACP,WAAW,CAAC,CAACQ,OAAO,CAACC,GAAG,IAAI;YACtC,IAAIT,WAAW,CAACS,GAAG,CAAC,KAAKtB,SAAS,IAAIa,WAAW,CAACS,GAAG,CAAC,KAAK,IAAI,EAAE;cAC/D,IAAIC,KAAK,CAACC,OAAO,CAACX,WAAW,CAACS,GAAG,CAAC,CAAC,EAAE;gBACnC;gBACAT,WAAW,CAACS,GAAG,CAAC,CAACD,OAAO,CAACI,IAAI,IAAIR,QAAQ,CAACS,MAAM,CAACJ,GAAG,EAAEG,IAAI,CAAC,CAAC;cAC9D,CAAC,MAAM;gBACLR,QAAQ,CAACS,MAAM,CAACJ,GAAG,EAAET,WAAW,CAACS,GAAG,CAAC,CAAC;cACxC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,IAAIlD,aAAa,CAAC2C,MAAM,GAAG,CAAC,IAAI3C,aAAa,CAAC,CAAC,CAAC,CAAC4C,aAAa,EAAE;YAC9DC,QAAQ,CAACS,MAAM,CAAC,OAAO,EAAEtD,aAAa,CAAC,CAAC,CAAC,CAAC4C,aAAa,CAAC;YACxDrC,eAAe,CAAC,yBAAyB,CAAC;UAC5C;;UAEA;UACA,IAAIT,aAAa,CAAC6C,MAAM,GAAG,CAAC,IAAI7C,aAAa,CAAC,CAAC,CAAC,CAAC8C,aAAa,EAAE;YAC9DW,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE1D,aAAa,CAAC,CAAC,CAAC,CAAC2D,IAAI,CAAC;YACpEZ,QAAQ,CAACS,MAAM,CAAC,WAAW,EAAExD,aAAa,CAAC,CAAC,CAAC,CAAC8C,aAAa,CAAC;UAC9D;;UAEA;UACA/B,kBAAkB,CAAC6C,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;UAC9BnB,QAAQ,GAAG,MAAMzE,QAAQ,CAAC8E,QAAQ,EAAE,CAACe,QAAQ,EAAEC,MAAM,EAAEC,KAAK,KAAK;YAC/DzD,iBAAiB,CAACuD,QAAQ,CAAC;;YAE3B;YACA,IAAIhD,eAAe,EAAE;cACnB,MAAMmD,WAAW,GAAG,CAACL,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG/C,eAAe,IAAI,IAAI,CAAC,CAAC;cAC3D,MAAMoD,aAAa,GAAGH,MAAM,IAAKC,KAAK,GAAGF,QAAQ,GAAG,GAAI;cACxD,MAAMK,KAAK,GAAGD,aAAa,GAAGD,WAAW,CAAC,CAAC;cAC3C,MAAMG,cAAc,GAAGJ,KAAK,GAAGE,aAAa;cAC5C,MAAMG,gBAAgB,GAAGD,cAAc,GAAGD,KAAK;cAE/CxD,cAAc,CAACwD,KAAK,CAAC;cACrBtD,gBAAgB,CAACwD,gBAAgB,CAAC;YACpC;YAEA,IAAIP,QAAQ,KAAK,GAAG,EAAE;cACpBrD,eAAe,CAAC,sBAAsB,CAAC;YACzC,CAAC,MAAM,IAAIqD,QAAQ,GAAG,CAAC,EAAE;cACvBrD,eAAe,CAAE,gBAAeqD,QAAS,GAAE,CAAC;YAC9C;UACF,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACA,MAAMf,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;QAE/B;QACAC,MAAM,CAACC,IAAI,CAACV,MAAM,CAAC,CAACW,OAAO,CAACC,GAAG,IAAI;UACjC,IAAIZ,MAAM,CAACY,GAAG,CAAC,KAAKtB,SAAS,IAAIU,MAAM,CAACY,GAAG,CAAC,KAAK,IAAI,EAAE;YACrDL,QAAQ,CAACS,MAAM,CAACJ,GAAG,EAAEZ,MAAM,CAACY,GAAG,CAAC,CAAC;UACnC;QACF,CAAC,CAAC;;QAEF;QACA,IAAItD,QAAQ,CAAC+C,MAAM,GAAG,CAAC,IAAI/C,QAAQ,CAAC,CAAC,CAAC,CAACgD,aAAa,EAAE;UACpDC,QAAQ,CAACS,MAAM,CAAC,UAAU,EAAE1D,QAAQ,CAAC,CAAC,CAAC,CAACgD,aAAa,CAAC;QACxD;QAEA,IAAIzD,YAAY,KAAK,OAAO,IAAIW,aAAa,CAAC6C,MAAM,GAAG,CAAC,IAAI7C,aAAa,CAAC,CAAC,CAAC,CAAC8C,aAAa,EAAE;UAC1FC,QAAQ,CAACS,MAAM,CAAC,WAAW,EAAExD,aAAa,CAAC,CAAC,CAAC,CAAC8C,aAAa,CAAC;QAC9D;;QAEA;QACA,QAAQzD,YAAY;UAClB,KAAK,aAAa;YAChBqD,QAAQ,GAAG,MAAMxE,OAAO,CAAC6E,QAAQ,CAAC;YAClC;UACF,KAAK,aAAa;YAChBL,QAAQ,GAAG,MAAMvE,YAAY,CAAC4E,QAAQ,CAAC;YACvC;UACF,KAAK,OAAO;YACVL,QAAQ,GAAG,MAAMtE,OAAO,CAAC2E,QAAQ,CAAC;YAClC;UACF;YACE,MAAM,IAAIuB,KAAK,CAAC,uBAAuB,CAAC;QAC5C;MACF;MAEA,IAAI5B,QAAQ,IAAIA,QAAQ,CAAC6B,MAAM,KAAK,GAAG,IAAI7B,QAAQ,CAAC8B,IAAI,CAACC,OAAO,EAAE;QAChE/G,OAAO,CAAC+G,OAAO,CAAC/B,QAAQ,CAAC8B,IAAI,CAAC9G,OAAO,CAAC;QACtC+B,IAAI,CAACiF,WAAW,CAAC,CAAC;QAClB3E,WAAW,CAAC,EAAE,CAAC;QACfE,gBAAgB,CAAC,EAAE,CAAC;QACpBE,gBAAgB,CAAC,EAAE,CAAC;QACpBuB,4BAA4B,CAAC,EAAE,CAAC;QAChCrB,eAAe,CAAC,SAAS,CAAC;QAC1BE,iBAAiB,CAAC,CAAC,CAAC;QACpBE,eAAe,CAAC,EAAE,CAAC;QACnBnB,SAAS,CAACD,YAAY,CAAC;MACzB,CAAC,MAAM,IAAIqD,QAAQ,EAAE;QAAA,IAAAiC,cAAA;QACnB,MAAMC,YAAY,GAAG,EAAAD,cAAA,GAAAjC,QAAQ,CAAC8B,IAAI,cAAAG,cAAA,uBAAbA,cAAA,CAAejH,OAAO,KAAI,wBAAwB;QACvEA,OAAO,CAACmH,KAAK,CAACD,YAAY,CAAC;MAC7B,CAAC,MAAM;QACL;QACAlH,OAAO,CAACoH,OAAO,CAAC;UACdC,OAAO,EAAE,8GAA8G;UACvHC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFvB,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC;MAC/E;IACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;MAAA,IAAAI,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACd1B,OAAO,CAACoB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;MAE9C;MACA,IAAIA,KAAK,CAACnC,QAAQ,IAAImC,KAAK,CAACnC,QAAQ,CAAC6B,MAAM,KAAK,GAAG,EAAE;QACnD7G,OAAO,CAACmH,KAAK,CAAC,4CAA4C,CAAC;QAC3D;QACAO,UAAU,CAAC,MAAM;UACfC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QACjC,CAAC,EAAE,IAAI,CAAC;QACR;MACF;;MAEA;MACA,IAAIV,KAAK,CAACW,IAAI,KAAK,cAAc,IAAIX,KAAK,CAACnH,OAAO,CAAC+H,QAAQ,CAAC,SAAS,CAAC,EAAE;QACtE/H,OAAO,CAACoH,OAAO,CAAC;UACdC,OAAO,EAAE,+GAA+G;UACxHC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFvB,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;MAC7E,CAAC,MAAM,IAAI,EAAAuB,eAAA,GAAAJ,KAAK,CAACnC,QAAQ,cAAAuC,eAAA,uBAAdA,eAAA,CAAgBV,MAAM,MAAK,GAAG,EAAE;QACzC7G,OAAO,CAACmH,KAAK,CAAC,uDAAuD,CAAC;MACxE,CAAC,MAAM,IAAI,EAAAK,gBAAA,GAAAL,KAAK,CAACnC,QAAQ,cAAAwC,gBAAA,uBAAdA,gBAAA,CAAgBX,MAAM,MAAK,GAAG,EAAE;QAAA,IAAAmB,oBAAA;QACzChI,OAAO,CAACmH,KAAK,CAAC,EAAAa,oBAAA,GAAAb,KAAK,CAACnC,QAAQ,CAAC8B,IAAI,cAAAkB,oBAAA,uBAAnBA,oBAAA,CAAqBhI,OAAO,KAAI,4BAA4B,CAAC;MAC7E,CAAC,MAAM,IAAI,EAAAyH,gBAAA,GAAAN,KAAK,CAACnC,QAAQ,cAAAyC,gBAAA,uBAAdA,gBAAA,CAAgBZ,MAAM,MAAK,GAAG,EAAE;QACzC7G,OAAO,CAACmH,KAAK,CAAC,uCAAuC,CAAC;MACxD,CAAC,MAAM;QACLnH,OAAO,CAACmH,KAAK,CAAC,qEAAqE,CAAC;MACtF;IACF,CAAC,SAAS;MACR;MACAhF,UAAU,CAAC,KAAK,CAAC;MACjBU,iBAAiB,CAAC,CAAC,CAAC;MACpBE,eAAe,CAAC,EAAE,CAAC;MACnBd,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAM4H,WAAW,GAAG;IAClBC,YAAY,EAAEA,CAAA,KAAM,KAAK;IAAE;IAC3BC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAEzG,YAAY,KAAK,QAAQ,GAAGyC,SAAS,GAAG;EAClD,CAAC;EAED,MAAMiE,gBAAgB,GAAG;IACvBH,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC;EAED,MAAME,oBAAoB,GAAG;IAC3BJ,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC;EAED,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,QAAQ5G,YAAY;MAClB,KAAK,QAAQ;QACX,oBAAOL,OAAA,CAACN,OAAO;UAAAwH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpB,KAAK,aAAa;QAChB,oBAAOrH,OAAA,CAACL,SAAS;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB,KAAK,aAAa;QAChB,oBAAOrH,OAAA,CAACH,eAAe;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5B,KAAK,OAAO;QACV,oBAAOrH,OAAA,CAACJ,MAAM;UAAAsH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnB;QACE,oBAAOrH,OAAA,CAACL,SAAS;UAAAuH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACxB;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQjH,YAAY;MAClB,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB,KAAK,aAAa;QAChB,OAAO,YAAY;MACrB,KAAK,aAAa;QAChB,OAAO,YAAY;MACrB,KAAK,OAAO;QACV,OAAO,MAAM;MACf;QACE,OAAO,UAAU;IACrB;EACF,CAAC;EAED,oBACEL,OAAA;IAAK+C,SAAS,EAAC,mBAAmB;IAAAwE,QAAA,eAChCvH,OAAA;MAAK+C,SAAS,EAAC,WAAW;MAAAwE,QAAA,gBACxBvH,OAAA;QAAK+C,SAAS,EAAC,kBAAkB;QAAAwE,QAAA,GAC9BN,eAAe,CAAC,CAAC,eAClBjH,OAAA;UAAAuH,QAAA,GAAI,UAAQ,EAACD,gBAAgB,CAAC,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAENrH,OAAA,CAACvB,IAAI;QACHgC,IAAI,EAAEA,IAAK;QACX+G,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAElE,YAAa;QACvBmE,aAAa,EAAE;UACbvF,KAAK,EAAE,SAAS;UAChBY,SAAS,EAAE,EAAE;UACbF,OAAO,EAAE,EAAE;UACX8E,KAAK,EAAE,EAAE;UACTC,IAAI,EAAE,EAAE;UACRC,OAAO,EAAE,EAAE;UACXC,QAAQ,EAAE,EAAE;UACZC,YAAY,EAAE;QAChB,CAAE;QACFhF,SAAS,EAAC,eAAe;QAAAwE,QAAA,gBAEzBvH,OAAA;UAAK+C,SAAS,EAAC,UAAU;UAAAwE,QAAA,gBACvBvH,OAAA,CAACvB,IAAI,CAACuJ,IAAI;YACRC,KAAK,EAAC,OAAO;YACbtD,IAAI,EAAC,OAAO;YACZuD,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEzJ,OAAO,EAAE;YAAwB,CAAC,CAAE;YAC9DqE,SAAS,EAAC,gBAAgB;YAAAwE,QAAA,eAE1BvH,OAAA,CAACrB,MAAM;cACLyJ,WAAW,EAAC,cAAc;cAC1BC,QAAQ,EAAE1F,iBAAkB;cAC5B2F,IAAI,EAAC,OAAO;cAAAf,QAAA,gBAEZvH,OAAA,CAACG,MAAM;gBAACgD,KAAK,EAAC,SAAS;gBAAAoE,QAAA,EAAC;cAAO;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCrH,OAAA,CAACG,MAAM;gBAACgD,KAAK,EAAC,WAAW;gBAAAoE,QAAA,EAAC;cAAS;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5CrH,OAAA,CAACG,MAAM;gBAACgD,KAAK,EAAC,SAAS;gBAAAoE,QAAA,EAAC;cAAO;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZrH,OAAA,CAACvB,IAAI,CAACuJ,IAAI;YACRC,KAAK,EAAC,OAAO;YACbtD,IAAI,EAAC,WAAW;YAChBuD,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEzJ,OAAO,EAAE;YAAwB,CAAC,CAAE;YAC9DqE,SAAS,EAAC,gBAAgB;YAAAwE,QAAA,eAE1BvH,OAAA,CAACrB,MAAM;cACLyJ,WAAW,EAAC,cAAc;cAC1BE,IAAI,EAAC,OAAO;cACZD,QAAQ,EAAEnF,qBAAsB;cAAAqE,QAAA,EAE/BhF,gBAAgB,CAACgG,GAAG,CAACjF,GAAG,iBACvBtD,OAAA,CAACG,MAAM;gBAAWgD,KAAK,EAAEG,GAAI;gBAAAiE,QAAA,EAAEjE;cAAG,GAArBA,GAAG;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA2B,CAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENrH,OAAA,CAACvB,IAAI,CAACuJ,IAAI;UACRC,KAAK,EAAC,SAAS;UACftD,IAAI,EAAC,SAAS;UACduD,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzJ,OAAO,EAAE;UAA0B,CAAC,CAAE;UAAA6I,QAAA,eAEhEvH,OAAA,CAACrB,MAAM;YAACyJ,WAAW,EAAC,gBAAgB;YAACE,IAAI,EAAC,OAAO;YAAAf,QAAA,EAC9ClF,iBAAiB,CAACkG,GAAG,CAAC1F,OAAO,iBAC5B7C,OAAA,CAACG,MAAM;cAAegD,KAAK,EAAEN,OAAQ;cAAA0E,QAAA,EAAE1E;YAAO,GAAjCA,OAAO;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACxD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAEXhH,YAAY,KAAK,QAAQ,iBACxBL,OAAA,CAACvB,IAAI,CAACuJ,IAAI;UACRC,KAAK,EAAC,+BAA+B;UACrClF,SAAS,EAAC,4BAA4B;UAAAwE,QAAA,gBAEtCvH,OAAA;YAAK+C,SAAS,EAAC,yBAAyB;YAAAwE,QAAA,eACtCvH,OAAA;cAAAuH,QAAA,EAAG;YAA4F;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChG,CAAC,eACNrH,OAAA,CAACrB,MAAM;YACL6J,IAAI,EAAC,UAAU;YACfJ,WAAW,EAAC,sCAAsC;YAClDE,IAAI,EAAC,OAAO;YACZnF,KAAK,EAAEV,yBAA0B;YACjC4F,QAAQ,EAAErF,6BAA8B;YACxCyF,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAnB,QAAA,EAExBhF,gBAAgB,CACdc,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAK7C,IAAI,CAACkI,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC;YAAA,CACvDJ,GAAG,CAACjF,GAAG,iBACNtD,OAAA,CAACG,MAAM;cAAWgD,KAAK,EAAEG,GAAI;cAAAiE,QAAA,EAAEjE;YAAG,GAArBA,GAAG;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA2B,CAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACTrH,OAAA;YAAK+C,SAAS,EAAC,yBAAyB;YAAAwE,QAAA,eACtCvH,OAAA;cAAAuH,QAAA,EAAO;YAAuF;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACZ,eAEDrH,OAAA,CAACvB,IAAI,CAACuJ,IAAI;UACRC,KAAK,EAAC,OAAO;UACbtD,IAAI,EAAC,OAAO;UACZuD,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzJ,OAAO,EAAE;UAAuB,CAAC,CAAE;UAAA6I,QAAA,eAE7DvH,OAAA;YACE4I,IAAI,EAAC,MAAM;YACXR,WAAW,EAAG,SAAQd,gBAAgB,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAE,QAAQ;YAC/D9F,SAAS,EAAC;UAAY;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,EAEX,CAAChH,YAAY,KAAK,aAAa,IAAIA,YAAY,KAAK,OAAO,kBAC1DL,OAAA,CAACvB,IAAI,CAACuJ,IAAI;UACRC,KAAK,EAAC,MAAM;UACZtD,IAAI,EAAC,MAAM;UACXuD,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzJ,OAAO,EAAE;UAAwB,CAAC,CAAE;UAAA6I,QAAA,eAE9DvH,OAAA;YACE4I,IAAI,EAAC,MAAM;YACXR,WAAW,EAAC,yBAAyB;YACrCrF,SAAS,EAAC;UAAY;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ,EAEAhH,YAAY,KAAK,QAAQ,iBACxBL,OAAA,CAAAE,SAAA;UAAAqH,QAAA,gBACEvH,OAAA,CAACvB,IAAI,CAACuJ,IAAI;YACRC,KAAK,EAAC,eAAe;YACrBlF,SAAS,EAAC,uBAAuB;YAAAwE,QAAA,eAEjCvH,OAAA;cAAK+C,SAAS,EAAC,wBAAwB;cAAAwE,QAAA,gBACrCvH,OAAA;gBACE+C,SAAS,EAAG,iBAAgB3B,YAAY,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAG,EAAE;gBACzE0H,OAAO,EAAEA,CAAA,KAAMzH,eAAe,CAAC,SAAS,CAAE;gBAAAkG,QAAA,gBAE1CvH,OAAA,CAACN,OAAO;kBAACqD,SAAS,EAAC;gBAAa;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnCrH,OAAA;kBAAAuH,QAAA,EAAM;gBAAa;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1BrH,OAAA;kBAAAuH,QAAA,EAAG;gBAA0B;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNrH,OAAA;gBACE+C,SAAS,EAAG,iBAAgB3B,YAAY,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAG,EAAE;gBACvE0H,OAAO,EAAEA,CAAA,KAAMzH,eAAe,CAAC,OAAO,CAAE;gBAAAkG,QAAA,gBAExCvH,OAAA,CAACF,gBAAgB;kBAACiD,SAAS,EAAC;gBAAa;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5CrH,OAAA;kBAAAuH,QAAA,EAAM;gBAAa;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1BrH,OAAA;kBAAAuH,QAAA,EAAG;gBAA6B;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACNrH,OAAA;gBACE+C,SAAS,EAAG,iBAAgB3B,YAAY,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAG,EAAE;gBACxE0H,OAAO,EAAEA,CAAA,KAAMzH,eAAe,CAAC,QAAQ,CAAE;gBAAAkG,QAAA,gBAEzCvH,OAAA,CAACF,gBAAgB;kBAACiD,SAAS,EAAC;gBAAa;kBAAAmE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5CrH,OAAA;kBAAAuH,QAAA,EAAM;gBAAiB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9BrH,OAAA;kBAAAuH,QAAA,EAAG;gBAA2B;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,EAEXjG,YAAY,KAAK,SAAS,gBACzBpB,OAAA,CAAAE,SAAA;YAAAqH,QAAA,gBACEvH,OAAA,CAACvB,IAAI,CAACuJ,IAAI;cACRC,KAAK,EAAC,oBAAoB;cAC1BtD,IAAI,EAAC,SAAS;cACduD,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE/G,YAAY,KAAK,SAAS;gBAAE1C,OAAO,EAAE;cAAgC,CAAC,CAAE;cAAA6I,QAAA,eAE5FvH,OAAA;gBACE4I,IAAI,EAAC,MAAM;gBACXR,WAAW,EAAC,4CAA4C;gBACxDrF,SAAS,EAAC;cAAY;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZrH,OAAA,CAACvB,IAAI,CAACuJ,IAAI;cACRC,KAAK,EAAC,sBAAsB;cAC5BtD,IAAI,EAAC,UAAU;cAAA4C,QAAA,eAEfvH,OAAA;gBACE4I,IAAI,EAAC,KAAK;gBACVR,WAAW,EAAC,4BAA4B;gBACxCrF,SAAS,EAAC;cAAY;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZrH,OAAA,CAACvB,IAAI,CAACuJ,IAAI;cACRC,KAAK,EAAC,0BAA0B;cAChCtD,IAAI,EAAC,cAAc;cAAA4C,QAAA,eAEnBvH,OAAA;gBACE4I,IAAI,EAAC,KAAK;gBACVR,WAAW,EAAC,gCAAgC;gBAC5CrF,SAAS,EAAC;cAAY;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,eACZ,CAAC,GACDjG,YAAY,KAAK,OAAO,gBAC1BpB,OAAA,CAAAE,SAAA;YAAAqH,QAAA,gBACEvH,OAAA,CAACvB,IAAI,CAACuJ,IAAI;cACRC,KAAK,EAAC,eAAe;cACrBtD,IAAI,EAAC,UAAU;cACfuD,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE/G,YAAY,KAAK,OAAO;gBAAE1C,OAAO,EAAE;cAA6B,CAAC,CAAE;cAAA6I,QAAA,eAEvFvH,OAAA;gBACE4I,IAAI,EAAC,KAAK;gBACVR,WAAW,EAAC,4EAA4E;gBACxFrF,SAAS,EAAC;cAAY;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZrH,OAAA,CAACvB,IAAI,CAACuJ,IAAI;cACRC,KAAK,EAAC,kCAAkC;cACxClF,SAAS,EAAC,gBAAgB;cAAAwE,QAAA,eAE1BvH,OAAA,CAACpB,MAAM;gBAAA,GACDoI,oBAAoB;gBACxBlG,QAAQ,EAAEE,aAAc;gBACxBqH,QAAQ,EAAEA,CAAC;kBAAEvH;gBAAS,CAAC,KAAKG,gBAAgB,CAACH,QAAQ,CAAE;gBACvDiC,SAAS,EAAC,kBAAkB;gBAC5BgG,MAAM,EAAGC,CAAC,IAAK;kBACbA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClBhH,aAAa,CAAC,KAAK,CAAC;kBACpB,MAAMiH,KAAK,GAAG7E,KAAK,CAAC8E,IAAI,CAACH,CAAC,CAACI,YAAY,CAACF,KAAK,CAAC;kBAC9C,MAAMG,UAAU,GAAGH,KAAK,CAAC7F,MAAM,CAACiG,IAAI,IAAIA,IAAI,CAACV,IAAI,CAACW,UAAU,CAAC,QAAQ,CAAC,CAAC;kBACvE,IAAIF,UAAU,CAACxF,MAAM,GAAG,CAAC,EAAE;oBACzB,MAAMyF,IAAI,GAAGD,UAAU,CAAC,CAAC,CAAC;oBAC1B;oBACA,IAAIC,IAAI,CAAChB,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;sBAC/B5J,OAAO,CAACmH,KAAK,CAAC,2CAA2C,CAAC;sBAC1D;oBACF;oBACA5E,gBAAgB,CAAC,CAAC;sBAChBuI,GAAG,EAAE,IAAI;sBACT7E,IAAI,EAAE2E,IAAI,CAAC3E,IAAI;sBACfY,MAAM,EAAE,MAAM;sBACdzB,aAAa,EAAEwF,IAAI;sBACnBG,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;oBAC/B,CAAC,CAAC,CAAC;oBACH5K,OAAO,CAAC+G,OAAO,CAAC,kCAAkC,CAAC;kBACrD,CAAC,MAAM;oBACL/G,OAAO,CAACmH,KAAK,CAAC,2CAA2C,CAAC;kBAC5D;gBACF,CAAE;gBACF+D,UAAU,EAAGZ,CAAC,IAAK;kBACjBA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClBhH,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBACF4H,WAAW,EAAGb,CAAC,IAAK;kBAClBA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClBhH,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBACF6H,WAAW,EAAGd,CAAC,IAAK;kBAClBA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClBhH,aAAa,CAAC,KAAK,CAAC;gBACtB,CAAE;gBAAAsF,QAAA,eAEFvH,OAAA;kBAAK+C,SAAS,EAAG,yCAAwCf,UAAU,GAAG,WAAW,GAAG,EAAG,EAAE;kBAAAuF,QAAA,gBACvFvH,OAAA,CAACP,QAAQ;oBAACsD,SAAS,EAAC;kBAAa;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpCrH,OAAA;oBAAAuH,QAAA,EAAIvF,UAAU,GAAG,sBAAsB,GAAG;kBAA0C;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzFrH,OAAA;oBAAG+C,SAAS,EAAC,aAAa;oBAAAwE,QAAA,EAAC;kBAA8B;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC7DrH,OAAA;oBAAG+C,SAAS,EAAC,aAAa;oBAAAwE,QAAA,EAAC;kBAAiC;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,eACZ,CAAC,gBAEHrH,OAAA,CAAAE,SAAA;YAAAqH,QAAA,gBACEvH,OAAA,CAACvB,IAAI,CAACuJ,IAAI;cACRC,KAAK,EAAC,mBAAmB;cACzBlF,SAAS,EAAC,gBAAgB;cAAAwE,QAAA,eAE1BvH,OAAA,CAACpB,MAAM;gBAAA,GACDmI,gBAAgB;gBACpBjG,QAAQ,EAAEI,aAAc;gBACxBmH,QAAQ,EAAEA,CAAC;kBAAEvH;gBAAS,CAAC,KAAKK,gBAAgB,CAACL,QAAQ,CAAE;gBACvDiC,SAAS,EAAC,cAAc;gBAAAwE,QAAA,eAExBvH,OAAA;kBAAK+C,SAAS,EAAC,aAAa;kBAAAwE,QAAA,gBAC1BvH,OAAA,CAACF,gBAAgB;oBAACiD,SAAS,EAAC;kBAAa;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CrH,OAAA;oBAAAuH,QAAA,EAAG;kBAAkC;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACzCrH,OAAA;oBAAG+C,SAAS,EAAC,aAAa;oBAAAwE,QAAA,EAAC;kBAAwC;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACvErH,OAAA;oBAAG+C,SAAS,EAAC,aAAa;oBAAAwE,QAAA,EAAC;kBAA8C;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZrH,OAAA,CAACvB,IAAI,CAACuJ,IAAI;cACRC,KAAK,EAAC,oCAAoC;cAC1ClF,SAAS,EAAC,gBAAgB;cAAAwE,QAAA,eAE1BvH,OAAA,CAACpB,MAAM;gBAAA,GACDoI,oBAAoB;gBACxBlG,QAAQ,EAAEE,aAAc;gBACxBqH,QAAQ,EAAEA,CAAC;kBAAEvH;gBAAS,CAAC,KAAKG,gBAAgB,CAACH,QAAQ,CAAE;gBACvDiC,SAAS,EAAC,kBAAkB;gBAC5BgG,MAAM,EAAGC,CAAC,IAAK;kBACbA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClBhH,aAAa,CAAC,KAAK,CAAC;kBACpB,MAAMiH,KAAK,GAAG7E,KAAK,CAAC8E,IAAI,CAACH,CAAC,CAACI,YAAY,CAACF,KAAK,CAAC;kBAC9C,MAAMG,UAAU,GAAGH,KAAK,CAAC7F,MAAM,CAACiG,IAAI,IAAIA,IAAI,CAACV,IAAI,CAACW,UAAU,CAAC,QAAQ,CAAC,CAAC;kBACvE,IAAIF,UAAU,CAACxF,MAAM,GAAG,CAAC,EAAE;oBACzB,MAAMyF,IAAI,GAAGD,UAAU,CAAC,CAAC,CAAC;oBAC1B;oBACA,IAAIC,IAAI,CAAChB,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;sBAC/B5J,OAAO,CAACmH,KAAK,CAAC,2CAA2C,CAAC;sBAC1D;oBACF;oBACA5E,gBAAgB,CAAC,CAAC;sBAChBuI,GAAG,EAAE,IAAI;sBACT7E,IAAI,EAAE2E,IAAI,CAAC3E,IAAI;sBACfY,MAAM,EAAE,MAAM;sBACdzB,aAAa,EAAEwF,IAAI;sBACnBG,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;oBAC/B,CAAC,CAAC,CAAC;oBACH5K,OAAO,CAAC+G,OAAO,CAAC,kCAAkC,CAAC;kBACrD,CAAC,MAAM;oBACL/G,OAAO,CAACmH,KAAK,CAAC,2CAA2C,CAAC;kBAC5D;gBACF,CAAE;gBACF+D,UAAU,EAAGZ,CAAC,IAAK;kBACjBA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClBhH,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBACF4H,WAAW,EAAGb,CAAC,IAAK;kBAClBA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClBhH,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBACF6H,WAAW,EAAGd,CAAC,IAAK;kBAClBA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClBhH,aAAa,CAAC,KAAK,CAAC;gBACtB,CAAE;gBAAAsF,QAAA,eAEFvH,OAAA;kBAAK+C,SAAS,EAAG,yCAAwCf,UAAU,GAAG,WAAW,GAAG,EAAG,EAAE;kBAAAuF,QAAA,gBACvFvH,OAAA,CAACP,QAAQ;oBAACsD,SAAS,EAAC;kBAAa;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpCrH,OAAA;oBAAAuH,QAAA,EAAIvF,UAAU,GAAG,sBAAsB,GAAG;kBAA0C;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzFrH,OAAA;oBAAG+C,SAAS,EAAC,aAAa;oBAAAwE,QAAA,EAAC;kBAA8B;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC7DrH,OAAA;oBAAG+C,SAAS,EAAC,aAAa;oBAAAwE,QAAA,EAAC;kBAAiC;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,eACZ,CACH;QAAA,eACD,CACH,EAEAhH,YAAY,KAAK,QAAQ,iBACxBL,OAAA,CAACvB,IAAI,CAACuJ,IAAI;UACRC,KAAK,EAAG,UAASX,gBAAgB,CAAC,CAAE,WAAW;UAC/CvE,SAAS,EAAC,gBAAgB;UAAAwE,QAAA,eAE1BvH,OAAA,CAACpB,MAAM;YAAA,GACD+H,WAAW;YACf7F,QAAQ,EAAEA,QAAS;YACnBuH,QAAQ,EAAEA,CAAC;cAAEvH;YAAS,CAAC,KAAKC,WAAW,CAACD,QAAQ,CAAE;YAClDiC,SAAS,EAAC,iBAAiB;YAAAwE,QAAA,eAE3BvH,OAAA;cAAK+C,SAAS,EAAC,aAAa;cAAAwE,QAAA,gBAC1BvH,OAAA,CAACF,gBAAgB;gBAACiD,SAAS,EAAC;cAAa;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5CrH,OAAA;gBAAAuH,QAAA,EAAG;cAA4B;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnCrH,OAAA;gBAAG+C,SAAS,EAAC,aAAa;gBAAAwE,QAAA,EAAC;cAAkC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACZ,EAEAhH,YAAY,KAAK,OAAO,iBACvBL,OAAA,CAACvB,IAAI,CAACuJ,IAAI;UACRC,KAAK,EAAC,6BAA6B;UACnClF,SAAS,EAAC,gBAAgB;UAAAwE,QAAA,eAE1BvH,OAAA,CAACpB,MAAM;YAAA,GACDoI,oBAAoB;YACxBlG,QAAQ,EAAEE,aAAc;YACxBqH,QAAQ,EAAEA,CAAC;cAAEvH;YAAS,CAAC,KAAKG,gBAAgB,CAACH,QAAQ,CAAE;YACvDiC,SAAS,EAAC,kBAAkB;YAAAwE,QAAA,eAE5BvH,OAAA;cAAK+C,SAAS,EAAC,mBAAmB;cAAAwE,QAAA,gBAChCvH,OAAA,CAACP,QAAQ;gBAACsD,SAAS,EAAC;cAAa;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpCrH,OAAA;gBAAAuH,QAAA,EAAG;cAAiB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACZ,EAGAzG,OAAO,IAAIQ,YAAY,KAAK,QAAQ,IAAIf,YAAY,KAAK,QAAQ,iBAChEL,OAAA;UAAK+C,SAAS,EAAC,yBAAyB;UAAAwE,QAAA,gBACtCvH,OAAA;YAAK+C,SAAS,EAAC,iBAAiB;YAAAwE,QAAA,gBAC9BvH,OAAA;cAAK+C,SAAS,EAAC,eAAe;cAAAwE,QAAA,gBAC5BvH,OAAA;gBAAM+C,SAAS,EAAC,eAAe;gBAAAwE,QAAA,EAAE/F;cAAY;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDrH,OAAA;gBAAM+C,SAAS,EAAC,qBAAqB;gBAAAwE,QAAA,GAAEjG,cAAc,EAAC,GAAC;cAAA;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,EACL3F,WAAW,GAAG,CAAC,iBACd1B,OAAA;cAAK+C,SAAS,EAAC,cAAc;cAAAwE,QAAA,gBAC3BvH,OAAA;gBAAM+C,SAAS,EAAC,cAAc;gBAAAwE,QAAA,GAAC,eAC1B,EAAC,CAAC7F,WAAW,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEqI,OAAO,CAAC,CAAC,CAAC,EAAC,OAC/C;cAAA;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACNzF,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAG,IAAI,iBACxC5B,OAAA;gBAAM+C,SAAS,EAAC,gBAAgB;gBAAAwE,QAAA,GAAC,eAC5B,EAACyC,IAAI,CAACC,IAAI,CAACrI,aAAa,CAAC,EAAC,aAC/B;cAAA;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENrH,OAAA;YAAK+C,SAAS,EAAC,cAAc;YAAAwE,QAAA,eAC3BvH,OAAA;cACE+C,SAAS,EAAC,eAAe;cACzB0F,KAAK,EAAE;gBACLC,KAAK,EAAG,GAAEpH,cAAe,GAAE;gBAC3B4I,UAAU,EAAE;cACd;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENrH,OAAA;YAAK+C,SAAS,EAAC,kBAAkB;YAAAwE,QAAA,EAC9BjG,cAAc,GAAG,GAAG,gBACnBtB,OAAA;cAAK+C,SAAS,EAAC,gBAAgB;cAAAwE,QAAA,gBAC7BvH,OAAA;gBAAAuH,QAAA,EAAM;cAAoC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjDrH,OAAA;gBAAAuH,QAAA,EAAO;cAAgD;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,gBAENrH,OAAA;cAAK+C,SAAS,EAAC,iBAAiB;cAAAwE,QAAA,gBAC9BvH,OAAA;gBAAAuH,QAAA,EAAM;cAAgE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7ErH,OAAA;gBAAAuH,QAAA,EAAO;cAA2C;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDrH,OAAA;UAAK+C,SAAS,EAAC,cAAc;UAAAwE,QAAA,gBAC3BvH,OAAA,CAACnB,MAAM;YACL+J,IAAI,EAAC,SAAS;YACdE,OAAO,EAAEvI,QAAS;YAClB+H,IAAI,EAAC,OAAO;YACZvF,SAAS,EAAC,YAAY;YACtBoH,QAAQ,EAAEvJ,OAAQ;YAAA2G,QAAA,EACnB;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrH,OAAA,CAACnB,MAAM;YACL+J,IAAI,EAAC,SAAS;YACdwB,QAAQ,EAAC,QAAQ;YACjBxJ,OAAO,EAAEA,OAAQ;YACjB0H,IAAI,EAAC,OAAO;YACZvF,SAAS,EAAC,YAAY;YAAAwE,QAAA,EAErB3G,OAAO,GACNQ,YAAY,KAAK,QAAQ,IAAIf,YAAY,KAAK,QAAQ,GACtD,cAAc,GAAG,WAAW,GAE3B,OAAMiH,gBAAgB,CAAC,CAAE;UAC3B;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC7G,EAAA,CAxwBQJ,oBAAoB;EAAA,QACZ3B,IAAI,CAACiC,OAAO,EACV5B,WAAW;AAAA;AAAAuL,EAAA,GAFrBjK,oBAAoB;AA0wB7B,eAAeA,oBAAoB;AAAC,IAAAiK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}