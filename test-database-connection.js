// Test database connection directly
const mongoose = require('mongoose');
require('dotenv').config({ path: './server/.env' });

async function testDatabaseConnection() {
  try {
    console.log('🔍 Testing MongoDB Atlas Connection...');
    console.log('📍 Database URL exists:', !!process.env.MONGO_URL);
    
    if (!process.env.MONGO_URL) {
      console.log('❌ MONGO_URL not found in environment variables');
      return;
    }
    
    // Show partial URL for debugging (hide credentials)
    const urlParts = process.env.MONGO_URL.split('@');
    if (urlParts.length > 1) {
      console.log('📍 Database host:', urlParts[1].split('/')[0]);
      console.log('📍 Database name:', urlParts[1].split('/')[1]?.split('?')[0]);
    }
    
    console.log('\n🔗 Attempting connection...');
    
    // Connection options optimized for Atlas
    const options = {
      serverSelectionTimeoutMS: 10000, // 10 seconds
      socketTimeoutMS: 15000, // 15 seconds
      connectTimeoutMS: 10000, // 10 seconds
      family: 4, // Use IPv4
      maxPoolSize: 5,
      minPoolSize: 1,
      retryWrites: true,
      retryReads: true
    };
    
    const startTime = Date.now();
    
    await mongoose.connect(process.env.MONGO_URL, options);
    
    const connectTime = Date.now() - startTime;
    console.log(`✅ Database connected successfully in ${connectTime}ms!`);
    
    // Test basic operations
    console.log('\n🧪 Testing database operations...');
    
    // Test 1: List collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log(`✅ Collections found: ${collections.length}`);
    
    // Test 2: Test users collection specifically
    const User = require('./server/models/userModel');
    const userCount = await User.countDocuments().maxTimeMS(5000);
    console.log(`✅ Users in database: ${userCount}`);
    
    // Test 3: Try to find admin user
    const adminUser = await User.findOne({ email: '<EMAIL>' }).maxTimeMS(5000);
    if (adminUser) {
      console.log('✅ Admin user found:', adminUser.email);
      console.log('👑 Admin status:', adminUser.isAdmin);
    } else {
      console.log('⚠️ Admin user not found - may need to create one');
    }
    
    console.log('\n🎉 Database connection test completed successfully!');
    console.log('✅ MongoDB Atlas is accessible');
    console.log('✅ Database operations working');
    console.log('✅ Ready for server startup');
    
  } catch (error) {
    console.error('\n❌ Database connection failed:', error.message);
    
    if (error.message.includes('ETIMEOUT') || error.message.includes('timeout')) {
      console.log('\n🔧 TIMEOUT ISSUE DETECTED');
      console.log('💡 Possible solutions:');
      console.log('1. Check internet connection');
      console.log('2. Verify MongoDB Atlas IP whitelist (add 0.0.0.0/0 for testing)');
      console.log('3. Check if MongoDB Atlas cluster is running');
      console.log('4. Try using different DNS servers (*******, *******)');
      console.log('5. Check firewall settings');
    }
    
    if (error.message.includes('authentication')) {
      console.log('\n🔐 AUTHENTICATION ISSUE');
      console.log('💡 Check MongoDB Atlas username/password in .env file');
    }
    
    if (error.message.includes('network')) {
      console.log('\n🌐 NETWORK ISSUE');
      console.log('💡 Cannot reach MongoDB Atlas servers');
    }
    
  } finally {
    if (mongoose.connection.readyState === 1) {
      await mongoose.disconnect();
      console.log('🔌 Disconnected from database');
    }
    process.exit(0);
  }
}

testDatabaseConnection();
