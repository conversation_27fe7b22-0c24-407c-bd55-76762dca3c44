{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Forum\\\\index.js\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n{\n  questions.filter(question => question && question.user).map(question => {\n    var _question$user, _question$user2, _question$user3, _question$user4, _question$replies;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start justify-between mb-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3 flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: question.user,\n                size: \"sm\",\n                showOnlineStatus: false,\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 11,\n                columnNumber: 13\n              }, this), question.user && question.user.isOnline && /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'absolute',\n                  bottom: '-2px',\n                  right: '-2px',\n                  width: '12px',\n                  height: '12px',\n                  backgroundColor: '#22c55e',\n                  borderRadius: '50%',\n                  border: '2px solid #ffffff',\n                  boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\n                  zIndex: 10\n                },\n                title: \"Online\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 21,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 10,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"min-w-0 flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-gray-900 text-sm sm:text-base truncate\",\n                  children: ((_question$user = question.user) === null || _question$user === void 0 ? void 0 : _question$user.name) || 'Unknown User'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 40,\n                  columnNumber: 15\n                }, this), (((_question$user2 = question.user) === null || _question$user2 === void 0 ? void 0 : _question$user2.role) === 'admin' || ((_question$user3 = question.user) === null || _question$user3 === void 0 ? void 0 : _question$user3.isAdmin)) && /*#__PURE__*/_jsxDEV(MdVerified, {\n                  className: \"w-4 h-4 text-blue-500 flex-shrink-0\",\n                  title: \"Verified Admin\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 44,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 39,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs sm:text-sm text-gray-500 mt-0.5\",\n                children: (() => {\n                  try {\n                    const date = new Date(question.createdAt);\n                    if (isNaN(date.getTime())) {\n                      return 'Just now';\n                    }\n                    return date.toLocaleDateString('en-US', {\n                      year: 'numeric',\n                      month: 'short',\n                      day: 'numeric',\n                      hour: '2-digit',\n                      minute: '2-digit'\n                    });\n                  } catch {\n                    return 'Just now';\n                  }\n                })()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 9,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-1 sm:space-x-2 ml-2\",\n            children: (userData._id === ((_question$user4 = question.user) === null || _question$user4 === void 0 ? void 0 : _question$user4._id) || userData.isAdmin) && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleEdit(question),\n                className: \"flex items-center px-2 py-2 sm:px-3 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\",\n                children: /*#__PURE__*/_jsxDEV(FaPencilAlt, {\n                  className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 77,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 73,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleDelete(question),\n                className: \"flex items-center px-2 py-2 sm:px-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\",\n                children: /*#__PURE__*/_jsxDEV(MdDelete, {\n                  className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 pb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg sm:text-xl font-bold text-gray-900 mb-2 leading-tight\",\n          children: /*#__PURE__*/_jsxDEV(ContentRenderer, {\n            text: question.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-700 leading-relaxed mb-4\",\n          children: /*#__PURE__*/_jsxDEV(ContentRenderer, {\n            text: question.body\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 9\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between pt-3 border-t border-gray-100\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => toggleReplies(question._id),\n              className: \"action-btn flex items-center text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4 mr-1.5\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.001 8.001 0 01-7.291-4.678l-.709 2.122a.5.5 0 01-.948-.316L4.82 13.5a.5.5 0 01.316-.948l3.628.732a.5.5 0 01.316.948l-2.122-.709A8 8 0 1021 12z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 13\n              }, this), expandedReplies[question._id] ? 'Hide' : 'View', \" Replies (\", ((_question$replies = question.replies) === null || _question$replies === void 0 ? void 0 : _question$replies.length) || 0, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleReply(question._id),\n              className: \"action-btn flex items-center text-sm hover:text-green-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"ri-reply-line mr-1.5 text-base\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 13\n              }, this), \"Reply\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 9\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center text-xs text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(MdMessage, {\n              className: \"w-3 h-3 mr-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: question.replies.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 9\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 7\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [editQuestion && editQuestion._id === question._id && /*#__PURE__*/_jsxDEV(Form, {\n          form: form2,\n          onFinish: handleUpdateQuestion,\n          layout: \"vertical\",\n          initialValues: {\n            title: editQuestion.title,\n            body: editQuestion.body\n          },\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"title\",\n            label: \"Title\",\n            rules: [{\n              required: true,\n              message: \"Please enter the title\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input, {\n              style: {\n                padding: \"18px 12px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            name: \"body\",\n            label: \"Body\",\n            rules: [{\n              required: true,\n              message: \"Please enter the body\"\n            }],\n            children: /*#__PURE__*/_jsxDEV(Input.TextArea, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: \"Update Question\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleCancelUpdate,\n              style: {\n                marginLeft: 10\n              },\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 9\n        }, this), expandedReplies[question._id] && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reply-section mt-3\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-semibold text-gray-800 mb-3 flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: \"ri-chat-3-line mr-2 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 15\n              }, this), \"Replies (\", question.replies.filter(reply => reply && reply.user).length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 9\n        }, this), replyQuestionId === question._id && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"reply-form mt-3 p-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: /*#__PURE__*/_jsxDEV(ProfilePicture, {\n                user: userData,\n                size: \"sm\",\n                showOnlineStatus: false,\n                style: {\n                  width: '32px',\n                  height: '32px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: /*#__PURE__*/_jsxDEV(Form, {\n                form: form,\n                onFinish: handleReplySubmit,\n                layout: \"vertical\",\n                className: \"mb-0\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"text\",\n                  className: \"mb-3\",\n                  rules: [{\n                    required: true,\n                    message: \"Please enter your reply\"\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                    rows: 3,\n                    className: \"text-sm border-gray-300 rounded-lg\",\n                    placeholder: \"Write your reply here...\",\n                    style: {\n                      resize: 'none',\n                      borderRadius: '8px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  className: \"mb-0\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      size: \"small\",\n                      children: \"Reply\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      onClick: () => setReplyQuestionId(null),\n                      size: \"small\",\n                      children: \"Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 214,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true)]\n    }, question._id, true, {\n      fileName: _jsxFileName,\n      lineNumber: 2,\n      columnNumber: 3\n    }, this);\n  });\n}", "map": {"version": 3, "names": ["questions", "filter", "question", "user", "map", "_question$user", "_question$user2", "_question$user3", "_question$user4", "_question$replies", "_jsxDEV", "className", "children", "ProfilePicture", "size", "showOnlineStatus", "style", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isOnline", "position", "bottom", "right", "backgroundColor", "borderRadius", "border", "boxShadow", "zIndex", "title", "name", "role", "isAdmin", "MdVerified", "date", "Date", "createdAt", "isNaN", "getTime", "toLocaleDateString", "year", "month", "day", "hour", "minute", "userData", "_id", "_Fragment", "onClick", "handleEdit", "FaPencilAlt", "handleDelete", "MdDelete", "Content<PERSON><PERSON><PERSON>", "text", "body", "toggleReplies", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "expandedReplies", "replies", "length", "handleReply", "MdMessage", "editQuestion", "Form", "form", "form2", "onFinish", "handleUpdateQuestion", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "message", "Input", "padding", "TextArea", "<PERSON><PERSON>", "type", "htmlType", "handleCancelUpdate", "marginLeft", "reply", "replyQuestionId", "handleReplySubmit", "rows", "placeholder", "resize", "setReplyQuestionId"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Forum/index.js"], "sourcesContent": ["{questions.filter(question => question && question.user).map((question) => (\r\n  <div\r\n    key={question._id}\r\n    className=\"bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 overflow-hidden\"\r\n  >\r\n    {/* Question Header */}\r\n    <div className=\"p-4\">\r\n      <div className=\"flex items-start justify-between mb-2\">\r\n        <div className=\"flex items-start space-x-3 flex-1\">\r\n          <div className=\"relative\">\r\n            <ProfilePicture\r\n              user={question.user}\r\n              size=\"sm\"\r\n              showOnlineStatus={false}\r\n              style={{\r\n                width: '32px',\r\n                height: '32px'\r\n              }}\r\n            />\r\n            {question.user && question.user.isOnline && (\r\n              <div\r\n                style={{\r\n                  position: 'absolute',\r\n                  bottom: '-2px',\r\n                  right: '-2px',\r\n                  width: '12px',\r\n                  height: '12px',\r\n                  backgroundColor: '#22c55e',\r\n                  borderRadius: '50%',\r\n                  border: '2px solid #ffffff',\r\n                  boxShadow: '0 2px 8px rgba(34, 197, 94, 0.6)',\r\n                  zIndex: 10\r\n                }}\r\n                title=\"Online\"\r\n              />\r\n            )}\r\n          </div>\r\n          <div className=\"min-w-0 flex-1\">\r\n            <div className=\"flex items-center space-x-1\">\r\n              <h4 className=\"font-semibold text-gray-900 text-sm sm:text-base truncate\">\r\n                {question.user?.name || 'Unknown User'}\r\n              </h4>\r\n              {(question.user?.role === 'admin' || question.user?.isAdmin) && (\r\n                <MdVerified className=\"w-4 h-4 text-blue-500 flex-shrink-0\" title=\"Verified Admin\" />\r\n              )}\r\n            </div>\r\n            <p className=\"text-xs sm:text-sm text-gray-500 mt-0.5\">\r\n              {(() => {\r\n                try {\r\n                  const date = new Date(question.createdAt);\r\n                  if (isNaN(date.getTime())) {\r\n                    return 'Just now';\r\n                  }\r\n                  return date.toLocaleDateString('en-US', {\r\n                    year: 'numeric',\r\n                    month: 'short',\r\n                    day: 'numeric',\r\n                    hour: '2-digit',\r\n                    minute: '2-digit'\r\n                  });\r\n                } catch {\r\n                  return 'Just now';\r\n                }\r\n              })()}\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Action Buttons */}\r\n        <div className=\"flex items-center space-x-1 sm:space-x-2 ml-2\">\r\n          {(userData._id === question.user?._id || userData.isAdmin) && (\r\n            <>\r\n              <button\r\n                onClick={() => handleEdit(question)}\r\n                className=\"flex items-center px-2 py-2 sm:px-3 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200\"\r\n              >\r\n                <FaPencilAlt className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n              </button>\r\n              <button\r\n                onClick={() => handleDelete(question)}\r\n                className=\"flex items-center px-2 py-2 sm:px-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\"\r\n              >\r\n                <MdDelete className=\"w-3 h-3 sm:w-4 sm:h-4\" />\r\n              </button>\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    {/* Question Content */}\r\n    <div className=\"px-4 pb-4\">\r\n      <h3 className=\"text-lg sm:text-xl font-bold text-gray-900 mb-2 leading-tight\">\r\n        <ContentRenderer text={question.title} />\r\n      </h3>\r\n      <div className=\"text-gray-700 leading-relaxed mb-4\">\r\n        <ContentRenderer text={question.body} />\r\n      </div>\r\n\r\n      {/* Action Bar */}\r\n      <div className=\"flex items-center justify-between pt-3 border-t border-gray-100\">\r\n        <div className=\"flex items-center space-x-4\">\r\n          <button\r\n            onClick={() => toggleReplies(question._id)}\r\n            className=\"action-btn flex items-center text-sm\"\r\n          >\r\n            <svg className=\"w-4 h-4 mr-1.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.001 8.001 0 01-7.291-4.678l-.709 2.122a.5.5 0 01-.948-.316L4.82 13.5a.5.5 0 01.316-.948l3.628.732a.5.5 0 01.316.948l-2.122-.709A8 8 0 1021 12z\" />\r\n            </svg>\r\n            {expandedReplies[question._id] ? 'Hide' : 'View'} Replies ({question.replies?.length || 0})\r\n          </button>\r\n          <button\r\n            onClick={() => handleReply(question._id)}\r\n            className=\"action-btn flex items-center text-sm hover:text-green-600\"\r\n          >\r\n            <i className=\"ri-reply-line mr-1.5 text-base\"></i>\r\n            Reply\r\n          </button>\r\n        </div>\r\n        <div className=\"flex items-center text-xs text-gray-500\">\r\n          <MdMessage className=\"w-3 h-3 mr-1\" />\r\n          <span>{question.replies.length}</span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    {/* Conditional Sections */}\r\n    <>\r\n      {editQuestion && editQuestion._id === question._id && (\r\n        <Form\r\n          form={form2}\r\n          onFinish={handleUpdateQuestion}\r\n          layout=\"vertical\"\r\n          initialValues={{\r\n            title: editQuestion.title,\r\n            body: editQuestion.body,\r\n          }}\r\n        >\r\n          <Form.Item\r\n            name=\"title\"\r\n            label=\"Title\"\r\n            rules={[{ required: true, message: \"Please enter the title\" }]}\r\n          >\r\n            <Input style={{ padding: \"18px 12px\" }} />\r\n          </Form.Item>\r\n          <Form.Item\r\n            name=\"body\"\r\n            label=\"Body\"\r\n            rules={[{ required: true, message: \"Please enter the body\" }]}\r\n          >\r\n            <Input.TextArea />\r\n          </Form.Item>\r\n          <Form.Item>\r\n            <Button type=\"primary\" htmlType=\"submit\">\r\n              Update Question\r\n            </Button>\r\n            <Button onClick={handleCancelUpdate} style={{ marginLeft: 10 }}>\r\n              Cancel\r\n            </Button>\r\n          </Form.Item>\r\n        </Form>\r\n      )}\r\n\r\n      {expandedReplies[question._id] && (\r\n        <div className=\"reply-section mt-3\">\r\n          <div className=\"p-4\">\r\n            <h4 className=\"text-sm font-semibold text-gray-800 mb-3 flex items-center\">\r\n              <i className=\"ri-chat-3-line mr-2 text-blue-600\"></i>\r\n              Replies ({question.replies.filter(reply => reply && reply.user).length})\r\n            </h4>\r\n            <div className=\"space-y-3\">\r\n              {/* Replies go here */}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {replyQuestionId === question._id && (\r\n        <div className=\"reply-form mt-3 p-4\">\r\n          <div className=\"flex items-start space-x-3\">\r\n            <div className=\"relative\">\r\n              <ProfilePicture\r\n                user={userData}\r\n                size=\"sm\"\r\n                showOnlineStatus={false}\r\n                style={{\r\n                  width: '32px',\r\n                  height: '32px'\r\n                }}\r\n              />\r\n            </div>\r\n            <div className=\"flex-1\">\r\n              <Form form={form} onFinish={handleReplySubmit} layout=\"vertical\" className=\"mb-0\">\r\n                <Form.Item\r\n                  name=\"text\"\r\n                  className=\"mb-3\"\r\n                  rules={[{ required: true, message: \"Please enter your reply\" }]}\r\n                >\r\n                  <Input.TextArea\r\n                    rows={3}\r\n                    className=\"text-sm border-gray-300 rounded-lg\"\r\n                    placeholder=\"Write your reply here...\"\r\n                    style={{\r\n                      resize: 'none',\r\n                      borderRadius: '8px'\r\n                    }}\r\n                  />\r\n                </Form.Item>\r\n                <Form.Item className=\"mb-0\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Button type=\"primary\" htmlType=\"submit\" size=\"small\">\r\n                      Reply\r\n                    </Button>\r\n                    <Button onClick={() => setReplyQuestionId(null)} size=\"small\">\r\n                      Cancel\r\n                    </Button>\r\n                  </div>\r\n                </Form.Item>\r\n              </Form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  </div>\r\n))}\r\n"], "mappings": ";;;AAAA;EAACA,SAAS,CAACC,MAAM,CAACC,QAAQ,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAACC,GAAG,CAAEF,QAAQ;IAAA,IAAAG,cAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,iBAAA;IAAA,oBACpEC,OAAA;MAEEC,SAAS,EAAC,kHAAkH;MAAAC,QAAA,gBAG5HF,OAAA;QAAKC,SAAS,EAAC,KAAK;QAAAC,QAAA,eAClBF,OAAA;UAAKC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDF,OAAA;YAAKC,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDF,OAAA;cAAKC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBF,OAAA,CAACG,cAAc;gBACbV,IAAI,EAAED,QAAQ,CAACC,IAAK;gBACpBW,IAAI,EAAC,IAAI;gBACTC,gBAAgB,EAAE,KAAM;gBACxBC,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACDpB,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACC,IAAI,CAACoB,QAAQ,iBACtCb,OAAA;gBACEM,KAAK,EAAE;kBACLQ,QAAQ,EAAE,UAAU;kBACpBC,MAAM,EAAE,MAAM;kBACdC,KAAK,EAAE,MAAM;kBACbT,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdS,eAAe,EAAE,SAAS;kBAC1BC,YAAY,EAAE,KAAK;kBACnBC,MAAM,EAAE,mBAAmB;kBAC3BC,SAAS,EAAE,kCAAkC;kBAC7CC,MAAM,EAAE;gBACV,CAAE;gBACFC,KAAK,EAAC;cAAQ;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNZ,OAAA;cAAKC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BF,OAAA;gBAAKC,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,gBAC1CF,OAAA;kBAAIC,SAAS,EAAC,2DAA2D;kBAAAC,QAAA,EACtE,EAAAP,cAAA,GAAAH,QAAQ,CAACC,IAAI,cAAAE,cAAA,uBAAbA,cAAA,CAAe4B,IAAI,KAAI;gBAAc;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,EACJ,CAAC,EAAAhB,eAAA,GAAAJ,QAAQ,CAACC,IAAI,cAAAG,eAAA,uBAAbA,eAAA,CAAe4B,IAAI,MAAK,OAAO,MAAA3B,eAAA,GAAIL,QAAQ,CAACC,IAAI,cAAAI,eAAA,uBAAbA,eAAA,CAAe4B,OAAO,mBACzDzB,OAAA,CAAC0B,UAAU;kBAACzB,SAAS,EAAC,qCAAqC;kBAACqB,KAAK,EAAC;gBAAgB;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CACrF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACNZ,OAAA;gBAAGC,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACnD,CAAC,MAAM;kBACN,IAAI;oBACF,MAAMyB,IAAI,GAAG,IAAIC,IAAI,CAACpC,QAAQ,CAACqC,SAAS,CAAC;oBACzC,IAAIC,KAAK,CAACH,IAAI,CAACI,OAAO,CAAC,CAAC,CAAC,EAAE;sBACzB,OAAO,UAAU;oBACnB;oBACA,OAAOJ,IAAI,CAACK,kBAAkB,CAAC,OAAO,EAAE;sBACtCC,IAAI,EAAE,SAAS;sBACfC,KAAK,EAAE,OAAO;sBACdC,GAAG,EAAE,SAAS;sBACdC,IAAI,EAAE,SAAS;sBACfC,MAAM,EAAE;oBACV,CAAC,CAAC;kBACJ,CAAC,CAAC,MAAM;oBACN,OAAO,UAAU;kBACnB;gBACF,CAAC,EAAE;cAAC;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNZ,OAAA;YAAKC,SAAS,EAAC,+CAA+C;YAAAC,QAAA,EAC3D,CAACoC,QAAQ,CAACC,GAAG,OAAAzC,eAAA,GAAKN,QAAQ,CAACC,IAAI,cAAAK,eAAA,uBAAbA,eAAA,CAAeyC,GAAG,KAAID,QAAQ,CAACb,OAAO,kBACvDzB,OAAA,CAAAwC,SAAA;cAAAtC,QAAA,gBACEF,OAAA;gBACEyC,OAAO,EAAEA,CAAA,KAAMC,UAAU,CAAClD,QAAQ,CAAE;gBACpCS,SAAS,EAAC,8GAA8G;gBAAAC,QAAA,eAExHF,OAAA,CAAC2C,WAAW;kBAAC1C,SAAS,EAAC;gBAAuB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACTZ,OAAA;gBACEyC,OAAO,EAAEA,CAAA,KAAMG,YAAY,CAACpD,QAAQ,CAAE;gBACtCS,SAAS,EAAC,4GAA4G;gBAAAC,QAAA,eAEtHF,OAAA,CAAC6C,QAAQ;kBAAC5C,SAAS,EAAC;gBAAuB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC;YAAA,eACT;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNZ,OAAA;QAAKC,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBF,OAAA;UAAIC,SAAS,EAAC,+DAA+D;UAAAC,QAAA,eAC3EF,OAAA,CAAC8C,eAAe;YAACC,IAAI,EAAEvD,QAAQ,CAAC8B;UAAM;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC,CAAC,eACLZ,OAAA;UAAKC,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eACjDF,OAAA,CAAC8C,eAAe;YAACC,IAAI,EAAEvD,QAAQ,CAACwD;UAAK;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAGNZ,OAAA;UAAKC,SAAS,EAAC,iEAAiE;UAAAC,QAAA,gBAC9EF,OAAA;YAAKC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CF,OAAA;cACEyC,OAAO,EAAEA,CAAA,KAAMQ,aAAa,CAACzD,QAAQ,CAAC+C,GAAG,CAAE;cAC3CtC,SAAS,EAAC,sCAAsC;cAAAC,QAAA,gBAEhDF,OAAA;gBAAKC,SAAS,EAAC,gBAAgB;gBAACiD,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAlD,QAAA,eACnFF,OAAA;kBAAMqD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAA0M;kBAAA/C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/Q,CAAC,EACL6C,eAAe,CAACjE,QAAQ,CAAC+C,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM,EAAC,YAAU,EAAC,EAAAxC,iBAAA,GAAAP,QAAQ,CAACkE,OAAO,cAAA3D,iBAAA,uBAAhBA,iBAAA,CAAkB4D,MAAM,KAAI,CAAC,EAAC,GAC5F;YAAA;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTZ,OAAA;cACEyC,OAAO,EAAEA,CAAA,KAAMmB,WAAW,CAACpE,QAAQ,CAAC+C,GAAG,CAAE;cACzCtC,SAAS,EAAC,2DAA2D;cAAAC,QAAA,gBAErEF,OAAA;gBAAGC,SAAS,EAAC;cAAgC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,SAEpD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACNZ,OAAA;YAAKC,SAAS,EAAC,yCAAyC;YAAAC,QAAA,gBACtDF,OAAA,CAAC6D,SAAS;cAAC5D,SAAS,EAAC;YAAc;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtCZ,OAAA;cAAAE,QAAA,EAAOV,QAAQ,CAACkE,OAAO,CAACC;YAAM;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNZ,OAAA,CAAAwC,SAAA;QAAAtC,QAAA,GACG4D,YAAY,IAAIA,YAAY,CAACvB,GAAG,KAAK/C,QAAQ,CAAC+C,GAAG,iBAChDvC,OAAA,CAAC+D,IAAI;UACHC,IAAI,EAAEC,KAAM;UACZC,QAAQ,EAAEC,oBAAqB;UAC/BC,MAAM,EAAC,UAAU;UACjBC,aAAa,EAAE;YACb/C,KAAK,EAAEwC,YAAY,CAACxC,KAAK;YACzB0B,IAAI,EAAEc,YAAY,CAACd;UACrB,CAAE;UAAA9C,QAAA,gBAEFF,OAAA,CAAC+D,IAAI,CAACO,IAAI;YACR/C,IAAI,EAAC,OAAO;YACZgD,KAAK,EAAC,OAAO;YACbC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEC,OAAO,EAAE;YAAyB,CAAC,CAAE;YAAAxE,QAAA,eAE/DF,OAAA,CAAC2E,KAAK;cAACrE,KAAK,EAAE;gBAAEsE,OAAO,EAAE;cAAY;YAAE;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eACZZ,OAAA,CAAC+D,IAAI,CAACO,IAAI;YACR/C,IAAI,EAAC,MAAM;YACXgD,KAAK,EAAC,MAAM;YACZC,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEC,OAAO,EAAE;YAAwB,CAAC,CAAE;YAAAxE,QAAA,eAE9DF,OAAA,CAAC2E,KAAK,CAACE,QAAQ;cAAApE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACZZ,OAAA,CAAC+D,IAAI,CAACO,IAAI;YAAApE,QAAA,gBACRF,OAAA,CAAC8E,MAAM;cAACC,IAAI,EAAC,SAAS;cAACC,QAAQ,EAAC,QAAQ;cAAA9E,QAAA,EAAC;YAEzC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTZ,OAAA,CAAC8E,MAAM;cAACrC,OAAO,EAAEwC,kBAAmB;cAAC3E,KAAK,EAAE;gBAAE4E,UAAU,EAAE;cAAG,CAAE;cAAAhF,QAAA,EAAC;YAEhE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACP,EAEA6C,eAAe,CAACjE,QAAQ,CAAC+C,GAAG,CAAC,iBAC5BvC,OAAA;UAAKC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCF,OAAA;YAAKC,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBF,OAAA;cAAIC,SAAS,EAAC,4DAA4D;cAAAC,QAAA,gBACxEF,OAAA;gBAAGC,SAAS,EAAC;cAAmC;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,aAC5C,EAACpB,QAAQ,CAACkE,OAAO,CAACnE,MAAM,CAAC4F,KAAK,IAAIA,KAAK,IAAIA,KAAK,CAAC1F,IAAI,CAAC,CAACkE,MAAM,EAAC,GACzE;YAAA;cAAAlD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLZ,OAAA;cAAKC,SAAS,EAAC;YAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAErB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAwE,eAAe,KAAK5F,QAAQ,CAAC+C,GAAG,iBAC/BvC,OAAA;UAAKC,SAAS,EAAC,qBAAqB;UAAAC,QAAA,eAClCF,OAAA;YAAKC,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzCF,OAAA;cAAKC,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBF,OAAA,CAACG,cAAc;gBACbV,IAAI,EAAE6C,QAAS;gBACflC,IAAI,EAAC,IAAI;gBACTC,gBAAgB,EAAE,KAAM;gBACxBC,KAAK,EAAE;kBACLC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE;gBACV;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNZ,OAAA;cAAKC,SAAS,EAAC,QAAQ;cAAAC,QAAA,eACrBF,OAAA,CAAC+D,IAAI;gBAACC,IAAI,EAAEA,IAAK;gBAACE,QAAQ,EAAEmB,iBAAkB;gBAACjB,MAAM,EAAC,UAAU;gBAACnE,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC/EF,OAAA,CAAC+D,IAAI,CAACO,IAAI;kBACR/C,IAAI,EAAC,MAAM;kBACXtB,SAAS,EAAC,MAAM;kBAChBuE,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAEC,OAAO,EAAE;kBAA0B,CAAC,CAAE;kBAAAxE,QAAA,eAEhEF,OAAA,CAAC2E,KAAK,CAACE,QAAQ;oBACbS,IAAI,EAAE,CAAE;oBACRrF,SAAS,EAAC,oCAAoC;oBAC9CsF,WAAW,EAAC,0BAA0B;oBACtCjF,KAAK,EAAE;sBACLkF,MAAM,EAAE,MAAM;sBACdtE,YAAY,EAAE;oBAChB;kBAAE;oBAAAT,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC,eACZZ,OAAA,CAAC+D,IAAI,CAACO,IAAI;kBAACrE,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACzBF,OAAA;oBAAKC,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtCF,OAAA,CAAC8E,MAAM;sBAACC,IAAI,EAAC,SAAS;sBAACC,QAAQ,EAAC,QAAQ;sBAAC5E,IAAI,EAAC,OAAO;sBAAAF,QAAA,EAAC;oBAEtD;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTZ,OAAA,CAAC8E,MAAM;sBAACrC,OAAO,EAAEA,CAAA,KAAMgD,kBAAkB,CAAC,IAAI,CAAE;sBAACrF,IAAI,EAAC,OAAO;sBAAAF,QAAA,EAAC;oBAE9D;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA,eACD,CAAC;IAAA,GA7NEpB,QAAQ,CAAC+C,GAAG;MAAA9B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA8Nd,CAAC;EAAA,CACP,CAAC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}