// Test environment variables
require('dotenv').config({ path: './server/.env' });

console.log('🧪 Testing Environment Variables...');
console.log('📍 NODE_ENV:', process.env.NODE_ENV);
console.log('📍 PORT:', process.env.PORT);
console.log('📍 MONGO_URL exists:', !!process.env.MONGO_URL);
console.log('📍 JWT_SECRET exists:', !!process.env.JWT_SECRET);

if (process.env.MONGO_URL) {
  console.log('📍 MONGO_URL preview:', process.env.MONGO_URL.substring(0, 50) + '...');
}

console.log('✅ Environment test complete');
