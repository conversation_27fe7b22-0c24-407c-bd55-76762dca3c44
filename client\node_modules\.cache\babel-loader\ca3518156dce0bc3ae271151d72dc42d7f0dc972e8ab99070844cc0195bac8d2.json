{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Forum\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport './index.css';\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message, Button, Input, Form, Avatar, Badge, Tag } from \"antd\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\nimport image from '../../../assets/person.png';\nimport { PlusOutlined, MessageOutlined, EyeOutlined, UserOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Forum = () => {\n  _s();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [userData, setUserData] = useState('');\n  const [questions, setQuestions] = useState([]);\n  const [expandedReplies, setExpandedReplies] = useState({});\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const [replyRefs, setReplyRefs] = useState({});\n\n  // Pagination states\n  const [currentPage, setCurrentPage] = useState(1);\n  const [questionsPerPage] = useState(5);\n  const [totalQuestions, setTotalQuestions] = useState(0);\n  const fetchQuestions = async () => {\n    try {\n      const response = await getAllQuestions();\n      if (response.success) {\n        setQuestions(response.data.reverse());\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchQuestions();\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n    dispatch(HideLoading());\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      dispatch(ShowLoading());\n      getUserData();\n    }\n  }, []);\n  const toggleReplies = questionId => {\n    setExpandedReplies(prevExpandedReplies => ({\n      ...prevExpandedReplies,\n      [questionId]: !prevExpandedReplies[questionId]\n    }));\n  };\n  const handleAskQuestion = async values => {\n    try {\n      const response = await addQuestion(values);\n      if (response.success) {\n        message.success(response.message);\n        setAskQuestionVisible(false);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleReply = questionId => {\n    setReplyQuestionId(questionId);\n  };\n  const handleReplySubmit = async values => {\n    try {\n      const payload = {\n        questionId: replyQuestionId,\n        text: values.text\n      };\n      const response = await addReply(payload);\n      if (response.success) {\n        message.success(response.message);\n        setReplyQuestionId(null);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\n      setReplyRefs(prevRefs => ({\n        ...prevRefs,\n        [replyQuestionId]: /*#__PURE__*/React.createRef()\n      }));\n    }\n  }, [replyQuestionId, replyRefs]);\n  useEffect(() => {\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\n      replyRefs[replyQuestionId].current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [replyQuestionId, replyRefs]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"Forum\",\n      children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n        title: \"Forum\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"divider\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Welcome to the forum! Feel free to ask questions, share your thoughts, and engage with the community.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setAskQuestionVisible(true),\n          style: {\n            marginBottom: 20\n          },\n          children: \"Ask a Question\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 21\n      }, this), askQuestionVisible && /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        onFinish: handleAskQuestion,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"title\",\n          label: \"Title\",\n          rules: [{\n            required: true,\n            message: 'Please enter the title'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            style: {\n              padding: '18px 12px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"body\",\n          label: \"Body\",\n          rules: [{\n            required: true,\n            message: 'Please enter the body'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            children: \"Ask Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setAskQuestionVisible(false),\n            style: {\n              marginLeft: 10\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 25\n      }, this), questions.map(question => {\n        var _question$user, _question$user2, _question$replies;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"forum-question-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"question\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-row\",\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                src: (_question$user = question.user) !== null && _question$user !== void 0 && _question$user.profileImage ? question.user.profileImage : image,\n                alt: \"profile\",\n                size: 50\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: ((_question$user2 = question.user) === null || _question$user2 === void 0 ? void 0 : _question$user2.name) || 'Anonymous'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"title\",\n              children: question.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"body\",\n              children: question.body\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => toggleReplies(question._id),\n              children: expandedReplies[question._id] ? \"Collapse Replies\" : \"Expand Replies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => handleReply(question._id),\n              children: \"Reply\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 29\n          }, this), expandedReplies[question._id] && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"replies\",\n            children: ((_question$replies = question.replies) === null || _question$replies === void 0 ? void 0 : _question$replies.map(reply => {\n              var _reply$user, _reply$user2;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reply\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"profile-row\",\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    src: (_reply$user = reply.user) !== null && _reply$user !== void 0 && _reply$user.profileImage ? reply.user.profileImage : image,\n                    alt: \"profile\",\n                    size: 50\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: ((_reply$user2 = reply.user) === null || _reply$user2 === void 0 ? void 0 : _reply$user2.name) || 'Anonymous'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text\",\n                  children: reply.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 45\n                }, this)]\n              }, reply._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 41\n              }, this);\n            })) || /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No replies yet.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: replyRefs[question._id],\n            children: replyQuestionId === question._id && /*#__PURE__*/_jsxDEV(Form, {\n              form: form,\n              onFinish: handleReplySubmit,\n              layout: \"vertical\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"text\",\n                label: \"Your Reply\",\n                rules: [{\n                  required: true,\n                  message: 'Please enter your reply'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                  rows: 4\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  children: \"Submit Reply\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: () => setReplyQuestionId(null),\n                  style: {\n                    marginLeft: 10\n                  },\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 29\n          }, this)]\n        }, question._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 25\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 9\n  }, this);\n};\n_s(Forum, \"Q3mjrzM5QCjbgkH5fGALxIqRNSI=\", false, function () {\n  return [Form.useForm, useDispatch];\n});\n_c = Forum;\nexport default Forum;\nvar _c;\n$RefreshReg$(_c, \"Forum\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getUserInfo", "message", "<PERSON><PERSON>", "Input", "Form", "Avatar", "Badge", "Tag", "Page<PERSON><PERSON>le", "useDispatch", "HideLoading", "ShowLoading", "addQuestion", "addReply", "getAllQuestions", "image", "PlusOutlined", "MessageOutlined", "EyeOutlined", "UserOutlined", "jsxDEV", "_jsxDEV", "Forum", "_s", "isAdmin", "setIsAdmin", "userData", "setUserData", "questions", "setQuestions", "expandedReplies", "setExpandedReplies", "askQuestionVisible", "setAskQuestionVisible", "replyQuestionId", "setReplyQuestionId", "form", "useForm", "dispatch", "replyRefs", "setReplyRefs", "currentPage", "setCurrentPage", "questionsPerPage", "totalQuestions", "setTotalQuestions", "fetchQuestions", "response", "success", "data", "reverse", "error", "getUserData", "localStorage", "getItem", "toggleReplies", "questionId", "prevExpandedReplies", "handleAskQuestion", "values", "resetFields", "handleReply", "handleReplySubmit", "payload", "text", "prevRefs", "createRef", "current", "scrollIntoView", "behavior", "children", "className", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "marginBottom", "onFinish", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "padding", "TextArea", "type", "htmlType", "marginLeft", "map", "question", "_question$user", "_question$user2", "_question$replies", "src", "user", "profileImage", "alt", "size", "body", "_id", "replies", "reply", "_reply$user", "_reply$user2", "ref", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport './index.css';\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Badge, Tag } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\r\nimport image from '../../../assets/person.png';\r\nimport { PlusOutlined, MessageOutlined, EyeOutlined, UserOutlined } from '@ant-design/icons';\r\n\r\nconst Forum = () => {\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [userData, setUserData] = useState('');\r\n    const [questions, setQuestions] = useState([]);\r\n    const [expandedReplies, setExpandedReplies] = useState({});\r\n    const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n    const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n    const [form] = Form.useForm();\r\n    const dispatch = useDispatch();\r\n    const [replyRefs, setReplyRefs] = useState({});\r\n\r\n    // Pagination states\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [questionsPerPage] = useState(5);\r\n    const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n    const fetchQuestions = async () => {\r\n        try {\r\n            const response = await getAllQuestions();\r\n            if (response.success) {\r\n                setQuestions(response.data.reverse());\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchQuestions();\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n        dispatch(HideLoading());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const toggleReplies = (questionId) => {\r\n        setExpandedReplies((prevExpandedReplies) => ({\r\n            ...prevExpandedReplies,\r\n            [questionId]: !prevExpandedReplies[questionId],\r\n        }));\r\n    };\r\n\r\n    const handleAskQuestion = async (values) => {\r\n        try {\r\n            const response = await addQuestion(values);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setAskQuestionVisible(false);\r\n                form.resetFields();\r\n                await fetchQuestions();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    const handleReply = (questionId) => {\r\n        setReplyQuestionId(questionId);\r\n    };\r\n\r\n    const handleReplySubmit = async (values) => {\r\n        try {\r\n            const payload = {\r\n                questionId: replyQuestionId,\r\n                text: values.text\r\n            };\r\n            const response = await addReply(payload);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setReplyQuestionId(null);\r\n                form.resetFields();\r\n                await fetchQuestions();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n            setReplyRefs((prevRefs) => ({\r\n                ...prevRefs,\r\n                [replyQuestionId]: React.createRef(),\r\n            }));\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n            replyRefs[replyQuestionId].current.scrollIntoView({ behavior: 'smooth' });\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    return (\r\n        <div>\r\n            {!isAdmin && (\r\n                <div className=\"Forum\">\r\n                    <PageTitle title=\"Forum\" />\r\n                    <div className=\"divider\"></div>\r\n\r\n                    <div>\r\n                        <p>Welcome to the forum! Feel free to ask questions, share your thoughts, and engage with the community.</p>\r\n                        <Button onClick={() => setAskQuestionVisible(true)} style={{ marginBottom: 20 }}>\r\n                            Ask a Question\r\n                        </Button>\r\n                    </div>\r\n\r\n                    {askQuestionVisible && (\r\n                        <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\">\r\n                            <Form.Item name=\"title\" label=\"Title\" rules={[{ required: true, message: 'Please enter the title' }]}>\r\n                                <Input style={{ padding: '18px 12px' }} />\r\n                            </Form.Item>\r\n                            <Form.Item name=\"body\" label=\"Body\" rules={[{ required: true, message: 'Please enter the body' }]}>\r\n                                <Input.TextArea />\r\n                            </Form.Item>\r\n                            <Form.Item>\r\n                                <Button type=\"primary\" htmlType=\"submit\">\r\n                                    Ask Question\r\n                                </Button>\r\n                                <Button onClick={() => setAskQuestionVisible(false)} style={{ marginLeft: 10 }}>\r\n                                    Cancel\r\n                                </Button>\r\n                            </Form.Item>\r\n                        </Form>\r\n                    )}\r\n\r\n                    {questions.map((question) => (\r\n                        <div key={question._id} className=\"forum-question-container\">\r\n                            <div className=\"question\">\r\n                                <div className=\"profile-row\">\r\n                                    <Avatar src={question.user?.profileImage ? question.user.profileImage : image} alt=\"profile\" size={50} />\r\n                                    <p>{question.user?.name || 'Anonymous'}</p>\r\n                                </div>\r\n                                <div className=\"title\">{question.title}</div>\r\n                                <div className=\"body\">{question.body}</div>\r\n                                <Button onClick={() => toggleReplies(question._id)}>\r\n                                    {expandedReplies[question._id] ? \"Collapse Replies\" : \"Expand Replies\"}\r\n                                </Button>\r\n                                <Button onClick={() => handleReply(question._id)}>Reply</Button>\r\n                            </div>\r\n                            {expandedReplies[question._id] && (\r\n                                <div className=\"replies\">\r\n                                    {question.replies?.map((reply) => (\r\n                                        <div key={reply._id} className=\"reply\">\r\n                                            <div className=\"profile-row\">\r\n                                                <Avatar src={reply.user?.profileImage ? reply.user.profileImage : image} alt=\"profile\" size={50} />\r\n                                                <p>{reply.user?.name || 'Anonymous'}</p>\r\n                                            </div>\r\n                                            <div className=\"text\">{reply.text}</div>\r\n                                        </div>\r\n                                    )) || <p>No replies yet.</p>}\r\n                                </div>\r\n                            )}\r\n                            <div ref={replyRefs[question._id]}>\r\n                                {replyQuestionId === question._id && (\r\n                                    <Form form={form} onFinish={handleReplySubmit} layout=\"vertical\">\r\n                                        <Form.Item name=\"text\" label=\"Your Reply\" rules={[{ required: true, message: 'Please enter your reply' }]}>\r\n                                            <Input.TextArea rows={4} />\r\n                                        </Form.Item>\r\n                                        <Form.Item>\r\n                                            <Button type=\"primary\" htmlType=\"submit\">\r\n                                                Submit Reply\r\n                                            </Button>\r\n                                            <Button onClick={() => setReplyQuestionId(null)} style={{ marginLeft: 10 }}>\r\n                                                Cancel\r\n                                            </Button>\r\n                                        </Form.Item>\r\n                                    </Form>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n                    ))}\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Forum;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,QAAQ,MAAM;AACvE,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,WAAW,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,yBAAyB;AAChF,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,SAASC,YAAY,EAAEC,eAAe,EAAEC,WAAW,EAAEC,YAAY,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7F,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACmC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuC,IAAI,CAAC,GAAGhC,IAAI,CAACiC,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC8C,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EACtC,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAMiD,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMjC,eAAe,CAAC,CAAC;MACxC,IAAIiC,QAAQ,CAACC,OAAO,EAAE;QAClBnB,YAAY,CAACkB,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;MACzC,CAAC,MAAM;QACHjD,OAAO,CAACkD,KAAK,CAACJ,QAAQ,CAAC9C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOkD,KAAK,EAAE;MACZlD,OAAO,CAACkD,KAAK,CAACA,KAAK,CAAClD,OAAO,CAAC;IAChC;EACJ,CAAC;EAED,MAAMmD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAML,QAAQ,GAAG,MAAM/C,WAAW,CAAC,CAAC;MACpC,IAAI+C,QAAQ,CAACC,OAAO,EAAE;QAClB,IAAID,QAAQ,CAACE,IAAI,CAACzB,OAAO,EAAE;UACvBC,UAAU,CAAC,IAAI,CAAC;QACpB,CAAC,MAAM;UACHA,UAAU,CAAC,KAAK,CAAC;UACjBE,WAAW,CAACoB,QAAQ,CAACE,IAAI,CAAC;UAC1B,MAAMH,cAAc,CAAC,CAAC;QAC1B;MACJ,CAAC,MAAM;QACH7C,OAAO,CAACkD,KAAK,CAACJ,QAAQ,CAAC9C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOkD,KAAK,EAAE;MACZlD,OAAO,CAACkD,KAAK,CAACA,KAAK,CAAClD,OAAO,CAAC;IAChC;IACAqC,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;EAC3B,CAAC;EAEDZ,SAAS,CAAC,MAAM;IACZ,IAAIuD,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MAC/BhB,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;MACvByC,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAIC,UAAU,IAAK;IAClCzB,kBAAkB,CAAE0B,mBAAmB,KAAM;MACzC,GAAGA,mBAAmB;MACtB,CAACD,UAAU,GAAG,CAACC,mBAAmB,CAACD,UAAU;IACjD,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IACxC,IAAI;MACA,MAAMZ,QAAQ,GAAG,MAAMnC,WAAW,CAAC+C,MAAM,CAAC;MAC1C,IAAIZ,QAAQ,CAACC,OAAO,EAAE;QAClB/C,OAAO,CAAC+C,OAAO,CAACD,QAAQ,CAAC9C,OAAO,CAAC;QACjCgC,qBAAqB,CAAC,KAAK,CAAC;QAC5BG,IAAI,CAACwB,WAAW,CAAC,CAAC;QAClB,MAAMd,cAAc,CAAC,CAAC;MAC1B,CAAC,MAAM;QACH7C,OAAO,CAACkD,KAAK,CAACJ,QAAQ,CAAC9C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOkD,KAAK,EAAE;MACZlD,OAAO,CAACkD,KAAK,CAACA,KAAK,CAAClD,OAAO,CAAC;IAChC;EACJ,CAAC;EAED,MAAM4D,WAAW,GAAIL,UAAU,IAAK;IAChCrB,kBAAkB,CAACqB,UAAU,CAAC;EAClC,CAAC;EAED,MAAMM,iBAAiB,GAAG,MAAOH,MAAM,IAAK;IACxC,IAAI;MACA,MAAMI,OAAO,GAAG;QACZP,UAAU,EAAEtB,eAAe;QAC3B8B,IAAI,EAAEL,MAAM,CAACK;MACjB,CAAC;MACD,MAAMjB,QAAQ,GAAG,MAAMlC,QAAQ,CAACkD,OAAO,CAAC;MACxC,IAAIhB,QAAQ,CAACC,OAAO,EAAE;QAClB/C,OAAO,CAAC+C,OAAO,CAACD,QAAQ,CAAC9C,OAAO,CAAC;QACjCkC,kBAAkB,CAAC,IAAI,CAAC;QACxBC,IAAI,CAACwB,WAAW,CAAC,CAAC;QAClB,MAAMd,cAAc,CAAC,CAAC;MAC1B,CAAC,MAAM;QACH7C,OAAO,CAACkD,KAAK,CAACJ,QAAQ,CAAC9C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOkD,KAAK,EAAE;MACZlD,OAAO,CAACkD,KAAK,CAACA,KAAK,CAAClD,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDH,SAAS,CAAC,MAAM;IACZ,IAAIoC,eAAe,IAAI,CAACK,SAAS,CAACL,eAAe,CAAC,EAAE;MAChDM,YAAY,CAAEyB,QAAQ,KAAM;QACxB,GAAGA,QAAQ;QACX,CAAC/B,eAAe,gBAAGtC,KAAK,CAACsE,SAAS,CAAC;MACvC,CAAC,CAAC,CAAC;IACP;EACJ,CAAC,EAAE,CAAChC,eAAe,EAAEK,SAAS,CAAC,CAAC;EAEhCzC,SAAS,CAAC,MAAM;IACZ,IAAIoC,eAAe,IAAIK,SAAS,CAACL,eAAe,CAAC,EAAE;MAC/CK,SAAS,CAACL,eAAe,CAAC,CAACiC,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC7E;EACJ,CAAC,EAAE,CAACnC,eAAe,EAAEK,SAAS,CAAC,CAAC;EAEhC,oBACIlB,OAAA;IAAAiD,QAAA,EACK,CAAC9C,OAAO,iBACLH,OAAA;MAAKkD,SAAS,EAAC,OAAO;MAAAD,QAAA,gBAClBjD,OAAA,CAACb,SAAS;QAACgE,KAAK,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3BvD,OAAA;QAAKkD,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE/BvD,OAAA;QAAAiD,QAAA,gBACIjD,OAAA;UAAAiD,QAAA,EAAG;QAAqG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5GvD,OAAA,CAACnB,MAAM;UAAC2E,OAAO,EAAEA,CAAA,KAAM5C,qBAAqB,CAAC,IAAI,CAAE;UAAC6C,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAT,QAAA,EAAC;QAEjF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,EAEL5C,kBAAkB,iBACfX,OAAA,CAACjB,IAAI;QAACgC,IAAI,EAAEA,IAAK;QAAC4C,QAAQ,EAAEtB,iBAAkB;QAACuB,MAAM,EAAC,UAAU;QAAAX,QAAA,gBAC5DjD,OAAA,CAACjB,IAAI,CAAC8E,IAAI;UAACC,IAAI,EAAC,OAAO;UAACC,KAAK,EAAC,OAAO;UAACC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErF,OAAO,EAAE;UAAyB,CAAC,CAAE;UAAAqE,QAAA,eACjGjD,OAAA,CAAClB,KAAK;YAAC2E,KAAK,EAAE;cAAES,OAAO,EAAE;YAAY;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACZvD,OAAA,CAACjB,IAAI,CAAC8E,IAAI;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC,MAAM;UAACC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErF,OAAO,EAAE;UAAwB,CAAC,CAAE;UAAAqE,QAAA,eAC9FjD,OAAA,CAAClB,KAAK,CAACqF,QAAQ;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACZvD,OAAA,CAACjB,IAAI,CAAC8E,IAAI;UAAAZ,QAAA,gBACNjD,OAAA,CAACnB,MAAM;YAACuF,IAAI,EAAC,SAAS;YAACC,QAAQ,EAAC,QAAQ;YAAApB,QAAA,EAAC;UAEzC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvD,OAAA,CAACnB,MAAM;YAAC2E,OAAO,EAAEA,CAAA,KAAM5C,qBAAqB,CAAC,KAAK,CAAE;YAAC6C,KAAK,EAAE;cAAEa,UAAU,EAAE;YAAG,CAAE;YAAArB,QAAA,EAAC;UAEhF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACT,EAEAhD,SAAS,CAACgE,GAAG,CAAEC,QAAQ;QAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,iBAAA;QAAA,oBACpB3E,OAAA;UAAwBkD,SAAS,EAAC,0BAA0B;UAAAD,QAAA,gBACxDjD,OAAA;YAAKkD,SAAS,EAAC,UAAU;YAAAD,QAAA,gBACrBjD,OAAA;cAAKkD,SAAS,EAAC,aAAa;cAAAD,QAAA,gBACxBjD,OAAA,CAAChB,MAAM;gBAAC4F,GAAG,EAAE,CAAAH,cAAA,GAAAD,QAAQ,CAACK,IAAI,cAAAJ,cAAA,eAAbA,cAAA,CAAeK,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACC,YAAY,GAAGpF,KAAM;gBAACqF,GAAG,EAAC,SAAS;gBAACC,IAAI,EAAE;cAAG;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzGvD,OAAA;gBAAAiD,QAAA,EAAI,EAAAyB,eAAA,GAAAF,QAAQ,CAACK,IAAI,cAAAH,eAAA,uBAAbA,eAAA,CAAeZ,IAAI,KAAI;cAAW;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNvD,OAAA;cAAKkD,SAAS,EAAC,OAAO;cAAAD,QAAA,EAAEuB,QAAQ,CAACrB;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CvD,OAAA;cAAKkD,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAEuB,QAAQ,CAACS;YAAI;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CvD,OAAA,CAACnB,MAAM;cAAC2E,OAAO,EAAEA,CAAA,KAAMtB,aAAa,CAACsC,QAAQ,CAACU,GAAG,CAAE;cAAAjC,QAAA,EAC9CxC,eAAe,CAAC+D,QAAQ,CAACU,GAAG,CAAC,GAAG,kBAAkB,GAAG;YAAgB;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACTvD,OAAA,CAACnB,MAAM;cAAC2E,OAAO,EAAEA,CAAA,KAAMhB,WAAW,CAACgC,QAAQ,CAACU,GAAG,CAAE;cAAAjC,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,EACL9C,eAAe,CAAC+D,QAAQ,CAACU,GAAG,CAAC,iBAC1BlF,OAAA;YAAKkD,SAAS,EAAC,SAAS;YAAAD,QAAA,EACnB,EAAA0B,iBAAA,GAAAH,QAAQ,CAACW,OAAO,cAAAR,iBAAA,uBAAhBA,iBAAA,CAAkBJ,GAAG,CAAEa,KAAK;cAAA,IAAAC,WAAA,EAAAC,YAAA;cAAA,oBACzBtF,OAAA;gBAAqBkD,SAAS,EAAC,OAAO;gBAAAD,QAAA,gBAClCjD,OAAA;kBAAKkD,SAAS,EAAC,aAAa;kBAAAD,QAAA,gBACxBjD,OAAA,CAAChB,MAAM;oBAAC4F,GAAG,EAAE,CAAAS,WAAA,GAAAD,KAAK,CAACP,IAAI,cAAAQ,WAAA,eAAVA,WAAA,CAAYP,YAAY,GAAGM,KAAK,CAACP,IAAI,CAACC,YAAY,GAAGpF,KAAM;oBAACqF,GAAG,EAAC,SAAS;oBAACC,IAAI,EAAE;kBAAG;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnGvD,OAAA;oBAAAiD,QAAA,EAAI,EAAAqC,YAAA,GAAAF,KAAK,CAACP,IAAI,cAAAS,YAAA,uBAAVA,YAAA,CAAYxB,IAAI,KAAI;kBAAW;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACNvD,OAAA;kBAAKkD,SAAS,EAAC,MAAM;kBAAAD,QAAA,EAAEmC,KAAK,CAACzC;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GALlC6B,KAAK,CAACF,GAAG;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMd,CAAC;YAAA,CACT,CAAC,kBAAIvD,OAAA;cAAAiD,QAAA,EAAG;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CACR,eACDvD,OAAA;YAAKuF,GAAG,EAAErE,SAAS,CAACsD,QAAQ,CAACU,GAAG,CAAE;YAAAjC,QAAA,EAC7BpC,eAAe,KAAK2D,QAAQ,CAACU,GAAG,iBAC7BlF,OAAA,CAACjB,IAAI;cAACgC,IAAI,EAAEA,IAAK;cAAC4C,QAAQ,EAAElB,iBAAkB;cAACmB,MAAM,EAAC,UAAU;cAAAX,QAAA,gBAC5DjD,OAAA,CAACjB,IAAI,CAAC8E,IAAI;gBAACC,IAAI,EAAC,MAAM;gBAACC,KAAK,EAAC,YAAY;gBAACC,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAErF,OAAO,EAAE;gBAA0B,CAAC,CAAE;gBAAAqE,QAAA,eACtGjD,OAAA,CAAClB,KAAK,CAACqF,QAAQ;kBAACqB,IAAI,EAAE;gBAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACZvD,OAAA,CAACjB,IAAI,CAAC8E,IAAI;gBAAAZ,QAAA,gBACNjD,OAAA,CAACnB,MAAM;kBAACuF,IAAI,EAAC,SAAS;kBAACC,QAAQ,EAAC,QAAQ;kBAAApB,QAAA,EAAC;gBAEzC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvD,OAAA,CAACnB,MAAM;kBAAC2E,OAAO,EAAEA,CAAA,KAAM1C,kBAAkB,CAAC,IAAI,CAAE;kBAAC2C,KAAK,EAAE;oBAAEa,UAAU,EAAE;kBAAG,CAAE;kBAAArB,QAAA,EAAC;gBAE5E;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA,GA1CAiB,QAAQ,CAACU,GAAG;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2CjB,CAAC;MAAA,CACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAArD,EAAA,CAzMKD,KAAK;EAAA,QAOQlB,IAAI,CAACiC,OAAO,EACV5B,WAAW;AAAA;AAAAqG,EAAA,GAR1BxF,KAAK;AA2MX,eAAeA,KAAK;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}