// Fix database timeout issues
const mongoose = require("mongoose");
require("dotenv").config({ path: './server/.env' });

async function testAndFixDatabase() {
  try {
    console.log("🔍 Testing MongoDB Atlas Connection...");
    
    // Test with optimized connection settings
    const options = {
      serverSelectionTimeoutMS: 5000,   // Reduced from 30s to 5s
      socketTimeoutMS: 10000,           // Reduced from 45s to 10s
      connectTimeoutMS: 5000,           // Reduced from 30s to 5s
      family: 4,                        // Use IPv4
      maxPoolSize: 5,                   // Reduced pool size
      minPoolSize: 1,                   // Minimum connections
      retryWrites: true,
      retryReads: true
    };

    console.log("🔗 Attempting connection with optimized settings...");
    
    await mongoose.connect(process.env.MONGO_URL, options);
    console.log("✅ MongoDB connection successful!");
    
    // Test a simple query
    console.log("🧪 Testing database query...");
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log(`✅ Database query successful! Found ${collections.length} collections`);
    
    // Test user collection specifically
    console.log("👤 Testing users collection...");
    const User = require("./server/models/userModel");
    const userCount = await User.countDocuments();
    console.log(`✅ Users collection accessible! Found ${userCount} users`);
    
    console.log("\n🎉 Database connection is working!");
    console.log("💡 The timeout issue might be intermittent network problems.");
    console.log("💡 Try the admin panel again - it should work now.");
    
  } catch (error) {
    console.error("❌ Database connection failed:", error.message);
    
    if (error.message.includes('ETIMEOUT') || error.message.includes('timeout')) {
      console.log("\n🔧 TIMEOUT ISSUE DETECTED");
      console.log("💡 Possible solutions:");
      console.log("1. Check your internet connection");
      console.log("2. Add your IP to MongoDB Atlas whitelist (0.0.0.0/0 for testing)");
      console.log("3. Change DNS servers to Google DNS (*******, *******)");
      console.log("4. Try using a VPN if your ISP blocks MongoDB Atlas");
      console.log("5. Use local MongoDB for development");
    }
    
    if (error.message.includes('authentication')) {
      console.log("\n🔐 AUTHENTICATION ISSUE");
      console.log("💡 Check MongoDB Atlas username/password in .env file");
    }
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
    process.exit(0);
  }
}

testAndFixDatabase();
