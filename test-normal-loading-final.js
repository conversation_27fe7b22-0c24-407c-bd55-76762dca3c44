// Final test to verify normal loading behavior is restored
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testNormalLoadingFinal() {
  try {
    console.log('🧪 Final Test: Normal Loading Behavior Restored...');
    
    // Test 1: Login
    console.log('\n1️⃣ Testing admin login...');
    const loginResponse = await axios.post(`${BASE_URL}/users/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    }, { timeout: 10000 });
    
    if (!loginResponse.data.success) {
      console.log('❌ Login failed:', loginResponse.data.message);
      return;
    }
    
    const token = loginResponse.data.data;
    console.log('✅ Login successful');
    
    // Test 2: Test study materials endpoint (should work with normal loading)
    console.log('\n2️⃣ Testing study materials loading...');
    
    const startTime = Date.now();
    
    try {
      const materialsResponse = await axios.get(`${BASE_URL}/study/admin/all-materials`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 30000 // 30 seconds
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      if (materialsResponse.data.success) {
        console.log(`✅ Materials loaded successfully in ${duration}ms`);
        console.log('📊 Total materials:', materialsResponse.data.data.length);
        
        // Test 3: Quick material upload test
        console.log('\n3️⃣ Testing material upload with normal loading...');
        
        const testVideo = {
          className: '1',
          subject: 'Mathematics',
          title: `Normal Loading Final Test ${Date.now()}`,
          level: 'primary',
          videoID: 'dQw4w9WgXcQ'
        };
        
        const uploadStartTime = Date.now();
        
        const uploadResponse = await axios.post(`${BASE_URL}/study/add-video`, testVideo, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        });
        
        const uploadEndTime = Date.now();
        const uploadDuration = uploadEndTime - uploadStartTime;
        
        if (uploadResponse.data.success) {
          console.log(`✅ Material upload successful in ${uploadDuration}ms`);
          console.log('📊 Response:', uploadResponse.data.message);
          
          console.log('\n🎉 ALL TESTS PASSED!');
          console.log('✅ Normal loading behavior fully restored');
          console.log('✅ No more delayed data loading');
          console.log('✅ No more timeout issues');
          console.log('✅ Admin panel ready for normal use');
          
        } else {
          console.log('❌ Upload failed:', uploadResponse.data.message);
        }
        
      } else {
        console.log('❌ Materials loading failed:', materialsResponse.data.message);
      }
      
    } catch (loadError) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`❌ Loading failed after ${duration}ms`);
      console.log('📋 Error:', loadError.message);
      
      if (loadError.code === 'ECONNABORTED') {
        console.log('⚠️ Still getting timeouts - may need further investigation');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testNormalLoadingFinal();
