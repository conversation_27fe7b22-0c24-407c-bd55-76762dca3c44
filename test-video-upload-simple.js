// Simple test for video upload to identify timeout issue
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testVideoUpload() {
  try {
    console.log('🧪 Testing Video Upload with Simple Data...');
    
    // First, let's test if we can reach the server
    console.log('\n1️⃣ Testing server connectivity...');
    const healthResponse = await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
    console.log('✅ Server health:', healthResponse.data.status);
    
    // Test with a simple admin login (you may need to adjust credentials)
    console.log('\n2️⃣ Testing admin authentication...');
    
    // Try common admin credentials
    const adminCredentials = [
      { email: '<EMAIL>', password: 'admin123' },
      { email: '<EMAIL>', password: 'admin123' },
      { email: '<EMAIL>', password: 'admin123' }
    ];
    
    let token = null;
    
    for (const creds of adminCredentials) {
      try {
        console.log(`   Trying: ${creds.email}`);
        const loginResponse = await axios.post(`${BASE_URL}/users/login`, creds, { timeout: 10000 });
        
        if (loginResponse.data.success) {
          console.log('✅ Login successful with:', creds.email);
          token = loginResponse.data.data;
          break;
        }
      } catch (loginError) {
        console.log(`   ❌ Failed: ${loginError.response?.data?.message || loginError.message}`);
      }
    }
    
    if (!token) {
      console.log('❌ Could not authenticate with any admin credentials');
      console.log('💡 You may need to create an admin user first');
      return;
    }
    
    // Test 3: Try a simple video upload with minimal data
    console.log('\n3️⃣ Testing simple video upload...');
    
    const simpleVideoData = {
      className: '1',
      subject: 'Mathematics', 
      title: 'Test Video - Simple',
      level: 'primary',
      videoID: 'dQw4w9WgXcQ' // Simple YouTube video ID
    };
    
    console.log('📤 Sending video data:', simpleVideoData);
    
    try {
      const startTime = Date.now();
      
      const uploadResponse = await axios.post(`${BASE_URL}/study/add-video`, simpleVideoData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000 // 30 second timeout
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`✅ Video upload successful in ${duration}ms`);
      console.log('📊 Response:', uploadResponse.data);
      
    } catch (uploadError) {
      const errorInfo = {
        message: uploadError.message,
        code: uploadError.code,
        status: uploadError.response?.status,
        statusText: uploadError.response?.statusText,
        responseData: uploadError.response?.data,
        isTimeout: uploadError.code === 'ECONNABORTED' || uploadError.message.includes('timeout')
      };
      
      console.log('❌ Video upload failed:');
      console.log('📋 Error details:', JSON.stringify(errorInfo, null, 2));
      
      if (errorInfo.isTimeout) {
        console.log('⏰ TIMEOUT DETECTED - This is the issue!');
        console.log('💡 Possible causes:');
        console.log('   - Database connection is slow');
        console.log('   - Server is processing too slowly');
        console.log('   - AWS S3 connection issues');
        console.log('   - MongoDB Atlas network issues');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testVideoUpload();
