{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Forum\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport './index.css';\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message, Button, Input, Form, Avatar, Badge, Tag } from \"antd\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\nimport image from '../../../assets/person.png';\nimport { PlusOutlined, MessageOutlined, EyeOutlined, UserOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Forum = () => {\n  _s();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [userData, setUserData] = useState('');\n  const [questions, setQuestions] = useState([]);\n  const [expandedReplies, setExpandedReplies] = useState({});\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const [replyRefs, setReplyRefs] = useState({});\n\n  // Pagination states\n  const [currentPage, setCurrentPage] = useState(1);\n  const [questionsPerPage] = useState(5);\n  const [totalQuestions, setTotalQuestions] = useState(0);\n  const fetchQuestions = async (page = currentPage) => {\n    try {\n      const response = await getAllQuestions({\n        page,\n        limit: questionsPerPage\n      });\n      if (response.success) {\n        setQuestions(response.data.reverse());\n        setTotalQuestions(response.total || response.data.length);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchQuestions();\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n    dispatch(HideLoading());\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      dispatch(ShowLoading());\n      getUserData();\n    }\n  }, []);\n  const toggleReplies = questionId => {\n    setExpandedReplies(prevExpandedReplies => ({\n      ...prevExpandedReplies,\n      [questionId]: !prevExpandedReplies[questionId]\n    }));\n  };\n  const handleAskQuestion = async values => {\n    try {\n      const response = await addQuestion(values);\n      if (response.success) {\n        message.success(response.message);\n        setAskQuestionVisible(false);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleReply = questionId => {\n    setReplyQuestionId(questionId);\n  };\n  const handleReplySubmit = async values => {\n    try {\n      const payload = {\n        questionId: replyQuestionId,\n        text: values.text\n      };\n      const response = await addReply(payload);\n      if (response.success) {\n        message.success(response.message);\n        setReplyQuestionId(null);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\n      setReplyRefs(prevRefs => ({\n        ...prevRefs,\n        [replyQuestionId]: /*#__PURE__*/React.createRef()\n      }));\n    }\n  }, [replyQuestionId, replyRefs]);\n  useEffect(() => {\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\n      replyRefs[replyQuestionId].current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [replyQuestionId, replyRefs]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"Forum\",\n      children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n        title: \"Forum\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"divider\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Welcome to the forum! Feel free to ask questions, share your thoughts, and engage with the community.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setAskQuestionVisible(true),\n          style: {\n            marginBottom: 20\n          },\n          children: \"Ask a Question\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 21\n      }, this), askQuestionVisible && /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        onFinish: handleAskQuestion,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"title\",\n          label: \"Title\",\n          rules: [{\n            required: true,\n            message: 'Please enter the title'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            style: {\n              padding: '18px 12px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"body\",\n          label: \"Body\",\n          rules: [{\n            required: true,\n            message: 'Please enter the body'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            children: \"Ask Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setAskQuestionVisible(false),\n            style: {\n              marginLeft: 10\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 25\n      }, this), questions.map(question => {\n        var _question$user, _question$user2, _question$replies;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"forum-question-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"question\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-row\",\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                src: (_question$user = question.user) !== null && _question$user !== void 0 && _question$user.profileImage ? question.user.profileImage : image,\n                alt: \"profile\",\n                size: 50\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: ((_question$user2 = question.user) === null || _question$user2 === void 0 ? void 0 : _question$user2.name) || 'Anonymous'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"title\",\n              children: question.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"body\",\n              children: question.body\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => toggleReplies(question._id),\n              children: expandedReplies[question._id] ? \"Collapse Replies\" : \"Expand Replies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => handleReply(question._id),\n              children: \"Reply\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 29\n          }, this), expandedReplies[question._id] && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"replies\",\n            children: ((_question$replies = question.replies) === null || _question$replies === void 0 ? void 0 : _question$replies.map(reply => {\n              var _reply$user, _reply$user2;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reply\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"profile-row\",\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    src: (_reply$user = reply.user) !== null && _reply$user !== void 0 && _reply$user.profileImage ? reply.user.profileImage : image,\n                    alt: \"profile\",\n                    size: 50\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 183,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: ((_reply$user2 = reply.user) === null || _reply$user2 === void 0 ? void 0 : _reply$user2.name) || 'Anonymous'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 184,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text\",\n                  children: reply.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 45\n                }, this)]\n              }, reply._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 41\n              }, this);\n            })) || /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No replies yet.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: replyRefs[question._id],\n            children: replyQuestionId === question._id && /*#__PURE__*/_jsxDEV(Form, {\n              form: form,\n              onFinish: handleReplySubmit,\n              layout: \"vertical\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"text\",\n                label: \"Your Reply\",\n                rules: [{\n                  required: true,\n                  message: 'Please enter your reply'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                  rows: 4\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  children: \"Submit Reply\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: () => setReplyQuestionId(null),\n                  style: {\n                    marginLeft: 10\n                  },\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 29\n          }, this)]\n        }, question._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 25\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 132,\n    columnNumber: 9\n  }, this);\n};\n_s(Forum, \"Q3mjrzM5QCjbgkH5fGALxIqRNSI=\", false, function () {\n  return [Form.useForm, useDispatch];\n});\n_c = Forum;\nexport default Forum;\nvar _c;\n$RefreshReg$(_c, \"Forum\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getUserInfo", "message", "<PERSON><PERSON>", "Input", "Form", "Avatar", "Badge", "Tag", "Page<PERSON><PERSON>le", "useDispatch", "HideLoading", "ShowLoading", "addQuestion", "addReply", "getAllQuestions", "image", "PlusOutlined", "MessageOutlined", "EyeOutlined", "UserOutlined", "jsxDEV", "_jsxDEV", "Forum", "_s", "isAdmin", "setIsAdmin", "userData", "setUserData", "questions", "setQuestions", "expandedReplies", "setExpandedReplies", "askQuestionVisible", "setAskQuestionVisible", "replyQuestionId", "setReplyQuestionId", "form", "useForm", "dispatch", "replyRefs", "setReplyRefs", "currentPage", "setCurrentPage", "questionsPerPage", "totalQuestions", "setTotalQuestions", "fetchQuestions", "page", "response", "limit", "success", "data", "reverse", "total", "length", "error", "getUserData", "localStorage", "getItem", "toggleReplies", "questionId", "prevExpandedReplies", "handleAskQuestion", "values", "resetFields", "handleReply", "handleReplySubmit", "payload", "text", "prevRefs", "createRef", "current", "scrollIntoView", "behavior", "children", "className", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "marginBottom", "onFinish", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "padding", "TextArea", "type", "htmlType", "marginLeft", "map", "question", "_question$user", "_question$user2", "_question$replies", "src", "user", "profileImage", "alt", "size", "body", "_id", "replies", "reply", "_reply$user", "_reply$user2", "ref", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport './index.css';\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Badge, Tag } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\r\nimport image from '../../../assets/person.png';\r\nimport { PlusOutlined, MessageOutlined, EyeOutlined, UserOutlined } from '@ant-design/icons';\r\n\r\nconst Forum = () => {\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [userData, setUserData] = useState('');\r\n    const [questions, setQuestions] = useState([]);\r\n    const [expandedReplies, setExpandedReplies] = useState({});\r\n    const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n    const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n    const [form] = Form.useForm();\r\n    const dispatch = useDispatch();\r\n    const [replyRefs, setReplyRefs] = useState({});\r\n\r\n    // Pagination states\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [questionsPerPage] = useState(5);\r\n    const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n    const fetchQuestions = async (page = currentPage) => {\r\n        try {\r\n            const response = await getAllQuestions({ page, limit: questionsPerPage });\r\n            if (response.success) {\r\n                setQuestions(response.data.reverse());\r\n                setTotalQuestions(response.total || response.data.length);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchQuestions();\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n        dispatch(HideLoading());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const toggleReplies = (questionId) => {\r\n        setExpandedReplies((prevExpandedReplies) => ({\r\n            ...prevExpandedReplies,\r\n            [questionId]: !prevExpandedReplies[questionId],\r\n        }));\r\n    };\r\n\r\n    const handleAskQuestion = async (values) => {\r\n        try {\r\n            const response = await addQuestion(values);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setAskQuestionVisible(false);\r\n                form.resetFields();\r\n                await fetchQuestions();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    const handleReply = (questionId) => {\r\n        setReplyQuestionId(questionId);\r\n    };\r\n\r\n    const handleReplySubmit = async (values) => {\r\n        try {\r\n            const payload = {\r\n                questionId: replyQuestionId,\r\n                text: values.text\r\n            };\r\n            const response = await addReply(payload);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setReplyQuestionId(null);\r\n                form.resetFields();\r\n                await fetchQuestions();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n            setReplyRefs((prevRefs) => ({\r\n                ...prevRefs,\r\n                [replyQuestionId]: React.createRef(),\r\n            }));\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n            replyRefs[replyQuestionId].current.scrollIntoView({ behavior: 'smooth' });\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    return (\r\n        <div>\r\n            {!isAdmin && (\r\n                <div className=\"Forum\">\r\n                    <PageTitle title=\"Forum\" />\r\n                    <div className=\"divider\"></div>\r\n\r\n                    <div>\r\n                        <p>Welcome to the forum! Feel free to ask questions, share your thoughts, and engage with the community.</p>\r\n                        <Button onClick={() => setAskQuestionVisible(true)} style={{ marginBottom: 20 }}>\r\n                            Ask a Question\r\n                        </Button>\r\n                    </div>\r\n\r\n                    {askQuestionVisible && (\r\n                        <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\">\r\n                            <Form.Item name=\"title\" label=\"Title\" rules={[{ required: true, message: 'Please enter the title' }]}>\r\n                                <Input style={{ padding: '18px 12px' }} />\r\n                            </Form.Item>\r\n                            <Form.Item name=\"body\" label=\"Body\" rules={[{ required: true, message: 'Please enter the body' }]}>\r\n                                <Input.TextArea />\r\n                            </Form.Item>\r\n                            <Form.Item>\r\n                                <Button type=\"primary\" htmlType=\"submit\">\r\n                                    Ask Question\r\n                                </Button>\r\n                                <Button onClick={() => setAskQuestionVisible(false)} style={{ marginLeft: 10 }}>\r\n                                    Cancel\r\n                                </Button>\r\n                            </Form.Item>\r\n                        </Form>\r\n                    )}\r\n\r\n                    {questions.map((question) => (\r\n                        <div key={question._id} className=\"forum-question-container\">\r\n                            <div className=\"question\">\r\n                                <div className=\"profile-row\">\r\n                                    <Avatar src={question.user?.profileImage ? question.user.profileImage : image} alt=\"profile\" size={50} />\r\n                                    <p>{question.user?.name || 'Anonymous'}</p>\r\n                                </div>\r\n                                <div className=\"title\">{question.title}</div>\r\n                                <div className=\"body\">{question.body}</div>\r\n                                <Button onClick={() => toggleReplies(question._id)}>\r\n                                    {expandedReplies[question._id] ? \"Collapse Replies\" : \"Expand Replies\"}\r\n                                </Button>\r\n                                <Button onClick={() => handleReply(question._id)}>Reply</Button>\r\n                            </div>\r\n                            {expandedReplies[question._id] && (\r\n                                <div className=\"replies\">\r\n                                    {question.replies?.map((reply) => (\r\n                                        <div key={reply._id} className=\"reply\">\r\n                                            <div className=\"profile-row\">\r\n                                                <Avatar src={reply.user?.profileImage ? reply.user.profileImage : image} alt=\"profile\" size={50} />\r\n                                                <p>{reply.user?.name || 'Anonymous'}</p>\r\n                                            </div>\r\n                                            <div className=\"text\">{reply.text}</div>\r\n                                        </div>\r\n                                    )) || <p>No replies yet.</p>}\r\n                                </div>\r\n                            )}\r\n                            <div ref={replyRefs[question._id]}>\r\n                                {replyQuestionId === question._id && (\r\n                                    <Form form={form} onFinish={handleReplySubmit} layout=\"vertical\">\r\n                                        <Form.Item name=\"text\" label=\"Your Reply\" rules={[{ required: true, message: 'Please enter your reply' }]}>\r\n                                            <Input.TextArea rows={4} />\r\n                                        </Form.Item>\r\n                                        <Form.Item>\r\n                                            <Button type=\"primary\" htmlType=\"submit\">\r\n                                                Submit Reply\r\n                                            </Button>\r\n                                            <Button onClick={() => setReplyQuestionId(null)} style={{ marginLeft: 10 }}>\r\n                                                Cancel\r\n                                            </Button>\r\n                                        </Form.Item>\r\n                                    </Form>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n                    ))}\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Forum;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,QAAQ,MAAM;AACvE,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,WAAW,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,yBAAyB;AAChF,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,SAASC,YAAY,EAAEC,eAAe,EAAEC,WAAW,EAAEC,YAAY,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7F,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACmC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuC,IAAI,CAAC,GAAGhC,IAAI,CAACiC,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC8C,gBAAgB,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC;EACtC,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAMiD,cAAc,GAAG,MAAAA,CAAOC,IAAI,GAAGN,WAAW,KAAK;IACjD,IAAI;MACA,MAAMO,QAAQ,GAAG,MAAMlC,eAAe,CAAC;QAAEiC,IAAI;QAAEE,KAAK,EAAEN;MAAiB,CAAC,CAAC;MACzE,IAAIK,QAAQ,CAACE,OAAO,EAAE;QAClBrB,YAAY,CAACmB,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;QACrCP,iBAAiB,CAACG,QAAQ,CAACK,KAAK,IAAIL,QAAQ,CAACG,IAAI,CAACG,MAAM,CAAC;MAC7D,CAAC,MAAM;QACHrD,OAAO,CAACsD,KAAK,CAACP,QAAQ,CAAC/C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACZtD,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;IAChC;EACJ,CAAC;EAED,MAAMuD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMR,QAAQ,GAAG,MAAMhD,WAAW,CAAC,CAAC;MACpC,IAAIgD,QAAQ,CAACE,OAAO,EAAE;QAClB,IAAIF,QAAQ,CAACG,IAAI,CAAC3B,OAAO,EAAE;UACvBC,UAAU,CAAC,IAAI,CAAC;QACpB,CAAC,MAAM;UACHA,UAAU,CAAC,KAAK,CAAC;UACjBE,WAAW,CAACqB,QAAQ,CAACG,IAAI,CAAC;UAC1B,MAAML,cAAc,CAAC,CAAC;QAC1B;MACJ,CAAC,MAAM;QACH7C,OAAO,CAACsD,KAAK,CAACP,QAAQ,CAAC/C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACZtD,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;IAChC;IACAqC,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;EAC3B,CAAC;EAEDZ,SAAS,CAAC,MAAM;IACZ,IAAI2D,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MAC/BpB,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;MACvB6C,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAIC,UAAU,IAAK;IAClC7B,kBAAkB,CAAE8B,mBAAmB,KAAM;MACzC,GAAGA,mBAAmB;MACtB,CAACD,UAAU,GAAG,CAACC,mBAAmB,CAACD,UAAU;IACjD,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IACxC,IAAI;MACA,MAAMf,QAAQ,GAAG,MAAMpC,WAAW,CAACmD,MAAM,CAAC;MAC1C,IAAIf,QAAQ,CAACE,OAAO,EAAE;QAClBjD,OAAO,CAACiD,OAAO,CAACF,QAAQ,CAAC/C,OAAO,CAAC;QACjCgC,qBAAqB,CAAC,KAAK,CAAC;QAC5BG,IAAI,CAAC4B,WAAW,CAAC,CAAC;QAClB,MAAMlB,cAAc,CAAC,CAAC;MAC1B,CAAC,MAAM;QACH7C,OAAO,CAACsD,KAAK,CAACP,QAAQ,CAAC/C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACZtD,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;IAChC;EACJ,CAAC;EAED,MAAMgE,WAAW,GAAIL,UAAU,IAAK;IAChCzB,kBAAkB,CAACyB,UAAU,CAAC;EAClC,CAAC;EAED,MAAMM,iBAAiB,GAAG,MAAOH,MAAM,IAAK;IACxC,IAAI;MACA,MAAMI,OAAO,GAAG;QACZP,UAAU,EAAE1B,eAAe;QAC3BkC,IAAI,EAAEL,MAAM,CAACK;MACjB,CAAC;MACD,MAAMpB,QAAQ,GAAG,MAAMnC,QAAQ,CAACsD,OAAO,CAAC;MACxC,IAAInB,QAAQ,CAACE,OAAO,EAAE;QAClBjD,OAAO,CAACiD,OAAO,CAACF,QAAQ,CAAC/C,OAAO,CAAC;QACjCkC,kBAAkB,CAAC,IAAI,CAAC;QACxBC,IAAI,CAAC4B,WAAW,CAAC,CAAC;QAClB,MAAMlB,cAAc,CAAC,CAAC;MAC1B,CAAC,MAAM;QACH7C,OAAO,CAACsD,KAAK,CAACP,QAAQ,CAAC/C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACZtD,OAAO,CAACsD,KAAK,CAACA,KAAK,CAACtD,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDH,SAAS,CAAC,MAAM;IACZ,IAAIoC,eAAe,IAAI,CAACK,SAAS,CAACL,eAAe,CAAC,EAAE;MAChDM,YAAY,CAAE6B,QAAQ,KAAM;QACxB,GAAGA,QAAQ;QACX,CAACnC,eAAe,gBAAGtC,KAAK,CAAC0E,SAAS,CAAC;MACvC,CAAC,CAAC,CAAC;IACP;EACJ,CAAC,EAAE,CAACpC,eAAe,EAAEK,SAAS,CAAC,CAAC;EAEhCzC,SAAS,CAAC,MAAM;IACZ,IAAIoC,eAAe,IAAIK,SAAS,CAACL,eAAe,CAAC,EAAE;MAC/CK,SAAS,CAACL,eAAe,CAAC,CAACqC,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC7E;EACJ,CAAC,EAAE,CAACvC,eAAe,EAAEK,SAAS,CAAC,CAAC;EAEhC,oBACIlB,OAAA;IAAAqD,QAAA,EACK,CAAClD,OAAO,iBACLH,OAAA;MAAKsD,SAAS,EAAC,OAAO;MAAAD,QAAA,gBAClBrD,OAAA,CAACb,SAAS;QAACoE,KAAK,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3B3D,OAAA;QAAKsD,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE/B3D,OAAA;QAAAqD,QAAA,gBACIrD,OAAA;UAAAqD,QAAA,EAAG;QAAqG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5G3D,OAAA,CAACnB,MAAM;UAAC+E,OAAO,EAAEA,CAAA,KAAMhD,qBAAqB,CAAC,IAAI,CAAE;UAACiD,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAT,QAAA,EAAC;QAEjF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,EAELhD,kBAAkB,iBACfX,OAAA,CAACjB,IAAI;QAACgC,IAAI,EAAEA,IAAK;QAACgD,QAAQ,EAAEtB,iBAAkB;QAACuB,MAAM,EAAC,UAAU;QAAAX,QAAA,gBAC5DrD,OAAA,CAACjB,IAAI,CAACkF,IAAI;UAACC,IAAI,EAAC,OAAO;UAACC,KAAK,EAAC,OAAO;UAACC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzF,OAAO,EAAE;UAAyB,CAAC,CAAE;UAAAyE,QAAA,eACjGrD,OAAA,CAAClB,KAAK;YAAC+E,KAAK,EAAE;cAAES,OAAO,EAAE;YAAY;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACZ3D,OAAA,CAACjB,IAAI,CAACkF,IAAI;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC,MAAM;UAACC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzF,OAAO,EAAE;UAAwB,CAAC,CAAE;UAAAyE,QAAA,eAC9FrD,OAAA,CAAClB,KAAK,CAACyF,QAAQ;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACZ3D,OAAA,CAACjB,IAAI,CAACkF,IAAI;UAAAZ,QAAA,gBACNrD,OAAA,CAACnB,MAAM;YAAC2F,IAAI,EAAC,SAAS;YAACC,QAAQ,EAAC,QAAQ;YAAApB,QAAA,EAAC;UAEzC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACT3D,OAAA,CAACnB,MAAM;YAAC+E,OAAO,EAAEA,CAAA,KAAMhD,qBAAqB,CAAC,KAAK,CAAE;YAACiD,KAAK,EAAE;cAAEa,UAAU,EAAE;YAAG,CAAE;YAAArB,QAAA,EAAC;UAEhF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACT,EAEApD,SAAS,CAACoE,GAAG,CAAEC,QAAQ;QAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,iBAAA;QAAA,oBACpB/E,OAAA;UAAwBsD,SAAS,EAAC,0BAA0B;UAAAD,QAAA,gBACxDrD,OAAA;YAAKsD,SAAS,EAAC,UAAU;YAAAD,QAAA,gBACrBrD,OAAA;cAAKsD,SAAS,EAAC,aAAa;cAAAD,QAAA,gBACxBrD,OAAA,CAAChB,MAAM;gBAACgG,GAAG,EAAE,CAAAH,cAAA,GAAAD,QAAQ,CAACK,IAAI,cAAAJ,cAAA,eAAbA,cAAA,CAAeK,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACC,YAAY,GAAGxF,KAAM;gBAACyF,GAAG,EAAC,SAAS;gBAACC,IAAI,EAAE;cAAG;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzG3D,OAAA;gBAAAqD,QAAA,EAAI,EAAAyB,eAAA,GAAAF,QAAQ,CAACK,IAAI,cAAAH,eAAA,uBAAbA,eAAA,CAAeZ,IAAI,KAAI;cAAW;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACN3D,OAAA;cAAKsD,SAAS,EAAC,OAAO;cAAAD,QAAA,EAAEuB,QAAQ,CAACrB;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7C3D,OAAA;cAAKsD,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAEuB,QAAQ,CAACS;YAAI;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3C3D,OAAA,CAACnB,MAAM;cAAC+E,OAAO,EAAEA,CAAA,KAAMtB,aAAa,CAACsC,QAAQ,CAACU,GAAG,CAAE;cAAAjC,QAAA,EAC9C5C,eAAe,CAACmE,QAAQ,CAACU,GAAG,CAAC,GAAG,kBAAkB,GAAG;YAAgB;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACT3D,OAAA,CAACnB,MAAM;cAAC+E,OAAO,EAAEA,CAAA,KAAMhB,WAAW,CAACgC,QAAQ,CAACU,GAAG,CAAE;cAAAjC,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,EACLlD,eAAe,CAACmE,QAAQ,CAACU,GAAG,CAAC,iBAC1BtF,OAAA;YAAKsD,SAAS,EAAC,SAAS;YAAAD,QAAA,EACnB,EAAA0B,iBAAA,GAAAH,QAAQ,CAACW,OAAO,cAAAR,iBAAA,uBAAhBA,iBAAA,CAAkBJ,GAAG,CAAEa,KAAK;cAAA,IAAAC,WAAA,EAAAC,YAAA;cAAA,oBACzB1F,OAAA;gBAAqBsD,SAAS,EAAC,OAAO;gBAAAD,QAAA,gBAClCrD,OAAA;kBAAKsD,SAAS,EAAC,aAAa;kBAAAD,QAAA,gBACxBrD,OAAA,CAAChB,MAAM;oBAACgG,GAAG,EAAE,CAAAS,WAAA,GAAAD,KAAK,CAACP,IAAI,cAAAQ,WAAA,eAAVA,WAAA,CAAYP,YAAY,GAAGM,KAAK,CAACP,IAAI,CAACC,YAAY,GAAGxF,KAAM;oBAACyF,GAAG,EAAC,SAAS;oBAACC,IAAI,EAAE;kBAAG;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnG3D,OAAA;oBAAAqD,QAAA,EAAI,EAAAqC,YAAA,GAAAF,KAAK,CAACP,IAAI,cAAAS,YAAA,uBAAVA,YAAA,CAAYxB,IAAI,KAAI;kBAAW;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACN3D,OAAA;kBAAKsD,SAAS,EAAC,MAAM;kBAAAD,QAAA,EAAEmC,KAAK,CAACzC;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GALlC6B,KAAK,CAACF,GAAG;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMd,CAAC;YAAA,CACT,CAAC,kBAAI3D,OAAA;cAAAqD,QAAA,EAAG;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CACR,eACD3D,OAAA;YAAK2F,GAAG,EAAEzE,SAAS,CAAC0D,QAAQ,CAACU,GAAG,CAAE;YAAAjC,QAAA,EAC7BxC,eAAe,KAAK+D,QAAQ,CAACU,GAAG,iBAC7BtF,OAAA,CAACjB,IAAI;cAACgC,IAAI,EAAEA,IAAK;cAACgD,QAAQ,EAAElB,iBAAkB;cAACmB,MAAM,EAAC,UAAU;cAAAX,QAAA,gBAC5DrD,OAAA,CAACjB,IAAI,CAACkF,IAAI;gBAACC,IAAI,EAAC,MAAM;gBAACC,KAAK,EAAC,YAAY;gBAACC,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAEzF,OAAO,EAAE;gBAA0B,CAAC,CAAE;gBAAAyE,QAAA,eACtGrD,OAAA,CAAClB,KAAK,CAACyF,QAAQ;kBAACqB,IAAI,EAAE;gBAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACZ3D,OAAA,CAACjB,IAAI,CAACkF,IAAI;gBAAAZ,QAAA,gBACNrD,OAAA,CAACnB,MAAM;kBAAC2F,IAAI,EAAC,SAAS;kBAACC,QAAQ,EAAC,QAAQ;kBAAApB,QAAA,EAAC;gBAEzC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT3D,OAAA,CAACnB,MAAM;kBAAC+E,OAAO,EAAEA,CAAA,KAAM9C,kBAAkB,CAAC,IAAI,CAAE;kBAAC+C,KAAK,EAAE;oBAAEa,UAAU,EAAE;kBAAG,CAAE;kBAAArB,QAAA,EAAC;gBAE5E;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA,GA1CAiB,QAAQ,CAACU,GAAG;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2CjB,CAAC;MAAA,CACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAzD,EAAA,CA1MKD,KAAK;EAAA,QAOQlB,IAAI,CAACiC,OAAO,EACV5B,WAAW;AAAA;AAAAyG,EAAA,GAR1B5F,KAAK;AA4MX,eAAeA,KAAK;AAAC,IAAA4F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}