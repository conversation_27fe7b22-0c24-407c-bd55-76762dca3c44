@echo off
echo ========================================
echo    Starting BrainWave Application
echo ========================================

echo [1/3] Starting Server...
start "BrainWave Server" cmd /k "cd /d %~dp0 && echo Starting Server... && node simple-server.js"

echo Waiting 5 seconds for server to start...
timeout /t 5 /nobreak >nul

echo [2/3] Starting Client...
start "BrainWave Client" cmd /k "cd /d %~dp0client && echo Starting Client... && npm start"

echo Waiting 3 seconds for client to start...
timeout /t 3 /nobreak >nul

echo [3/3] Opening Browser...
start http://localhost:3000

echo ========================================
echo    All Services Started!
echo ========================================
echo.
echo 🔗 Frontend: http://localhost:3000
echo 🔗 Backend:  http://localhost:5000
echo.
echo Services are running in separate windows.
echo Close the service windows when done.
echo.
pause
