{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\StudyMaterials\\\\StudyMaterialManager.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from \"react\";\nimport { Table, Button, Space, Select, Input, message, Modal, Tag, Tooltip } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { getAllStudyMaterials, deleteVideo, deleteNote, deletePastPaper, deleteBook, deleteLiterature } from \"../../../apicalls/study\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport { FaVideo, FaFileAlt, FaBook, FaGraduationCap, FaEdit, FaTrash, <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aSearch } from \"react-icons/fa\";\nimport \"./StudyMaterialManager.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  Search\n} = Input;\nfunction StudyMaterialManager({\n  onEdit\n}) {\n  _s();\n  const dispatch = useDispatch();\n  const [materials, setMaterials] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [filters, setFilters] = useState({\n    materialType: \"\",\n    level: \"\",\n    className: \"\",\n    subject: \"\"\n  });\n  const [searchText, setSearchText] = useState(\"\");\n\n  // Get subjects based on level\n  const getSubjectsForLevel = level => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = level => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\"];\n      case \"secondary\":\n        return [\"6\", \"7\", \"8\", \"9\", \"10\", \"11\"];\n      case \"advance\":\n        return [\"12\", \"13\"];\n      default:\n        return [];\n    }\n  };\n\n  // Fetch materials\n  const fetchMaterials = async () => {\n    try {\n      setLoading(true);\n      // Skip global loading for faster admin experience\n\n      const response = await getAllStudyMaterials(filters);\n      if (response.status === 200 && response.data.success) {\n        setMaterials(response.data.data || []);\n      } else {\n        message.error(\"Failed to fetch study materials\");\n        setMaterials([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching materials:\", error);\n      message.error(\"Failed to fetch study materials\");\n      setMaterials([]);\n    } finally {\n      setLoading(false);\n      // No global loading spinner for faster UX\n    }\n  };\n\n  useEffect(() => {\n    fetchMaterials();\n  }, [filters]);\n\n  // Handle filter changes\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => {\n      const newFilters = {\n        ...prev,\n        [key]: value\n      };\n\n      // Reset dependent filters\n      if (key === \"level\") {\n        newFilters.className = \"\";\n        newFilters.subject = \"\";\n      }\n      return newFilters;\n    });\n  };\n\n  // Handle delete\n  const handleDelete = async material => {\n    Modal.confirm({\n      title: `Delete ${material.type.replace(\"-\", \" \")}`,\n      content: `Are you sure you want to delete \"${material.title}\"? This action cannot be undone.`,\n      okText: \"Delete\",\n      okType: \"danger\",\n      cancelText: \"Cancel\",\n      onOk: async () => {\n        try {\n          // Skip global loading for faster delete operation\n\n          let response;\n          switch (material.type) {\n            case \"videos\":\n              response = await deleteVideo(material._id);\n              break;\n            case \"study-notes\":\n              response = await deleteNote(material._id);\n              break;\n            case \"past-papers\":\n              response = await deletePastPaper(material._id);\n              break;\n            case \"books\":\n              response = await deleteBook(material._id);\n              break;\n            case \"literature\":\n              response = await deleteLiterature(material._id);\n              break;\n            default:\n              throw new Error(\"Invalid material type\");\n          }\n          if (response.status === 200 && response.data.success) {\n            message.success(response.data.message);\n            fetchMaterials(); // Refresh the list\n          } else {\n            var _response$data;\n            message.error(((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || \"Failed to delete material\");\n          }\n        } catch (error) {\n          console.error(\"Error deleting material:\", error);\n          message.error(\"Failed to delete material\");\n        } finally {\n          dispatch(HideLoading());\n        }\n      }\n    });\n  };\n\n  // Get material type icon\n  const getMaterialIcon = type => {\n    switch (type) {\n      case \"videos\":\n        return /*#__PURE__*/_jsxDEV(FaVideo, {\n          className: \"material-icon video\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 16\n        }, this);\n      case \"study-notes\":\n        return /*#__PURE__*/_jsxDEV(FaFileAlt, {\n          className: \"material-icon note\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 16\n        }, this);\n      case \"past-papers\":\n        return /*#__PURE__*/_jsxDEV(FaGraduationCap, {\n          className: \"material-icon paper\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 16\n        }, this);\n      case \"books\":\n        return /*#__PURE__*/_jsxDEV(FaBook, {\n          className: \"material-icon book\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaFileAlt, {\n          className: \"material-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 16\n        }, this);\n    }\n  };\n\n  // Get material type label\n  const getMaterialTypeLabel = type => {\n    switch (type) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return type;\n    }\n  };\n\n  // Filter materials based on search text\n  const filteredMaterials = materials.filter(material => material.title.toLowerCase().includes(searchText.toLowerCase()) || material.subject.toLowerCase().includes(searchText.toLowerCase()) || material.className.toLowerCase().includes(searchText.toLowerCase()));\n\n  // Table columns\n  const columns = [{\n    title: \"Material\",\n    key: \"material\",\n    width: \"30%\",\n    render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"material-info\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"material-header\",\n        children: [getMaterialIcon(record.type), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"material-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"material-title\",\n            children: record.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"material-meta\",\n            children: [/*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: getMaterialTypeLabel(record.type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"meta-text\",\n              children: [record.subject, \" \\u2022 Class \", record.className]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: \"Level\",\n    dataIndex: \"level\",\n    key: \"level\",\n    width: \"10%\",\n    render: level => /*#__PURE__*/_jsxDEV(Tag, {\n      color: level === \"primary\" ? \"green\" : level === \"secondary\" ? \"orange\" : \"purple\",\n      children: level.charAt(0).toUpperCase() + level.slice(1)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: \"Class\",\n    dataIndex: \"className\",\n    key: \"className\",\n    width: \"10%\",\n    render: className => /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"class-badge\",\n      children: [\"Class \", className]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 30\n    }, this)\n  }, {\n    title: \"Subject\",\n    dataIndex: \"subject\",\n    key: \"subject\",\n    width: \"15%\"\n  }, {\n    title: \"Year\",\n    dataIndex: \"year\",\n    key: \"year\",\n    width: \"10%\",\n    render: year => year || \"-\"\n  }, {\n    title: \"Actions\",\n    key: \"actions\",\n    width: \"25%\",\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Edit material\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          onClick: () => onEdit(record),\n          children: \"Edit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Delete material\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(FaTrash, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 21\n          }, this),\n          size: \"small\",\n          onClick: () => handleDelete(record),\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"study-material-manager\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"manager-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Study Materials Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Manage all uploaded study materials - edit, delete, and organize content\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"filters-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filters-row\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Material Type:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"All Types\",\n            value: filters.materialType || undefined,\n            onChange: value => handleFilterChange(\"materialType\", value),\n            allowClear: true,\n            style: {\n              width: 150\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"videos\",\n              children: \"Videos\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"study-notes\",\n              children: \"Study Notes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"past-papers\",\n              children: \"Past Papers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"books\",\n              children: \"Books\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Level:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"All Levels\",\n            value: filters.level || undefined,\n            onChange: value => handleFilterChange(\"level\", value),\n            allowClear: true,\n            style: {\n              width: 120\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"primary\",\n              children: \"Primary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"secondary\",\n              children: \"Secondary\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"advance\",\n              children: \"Advance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), filters.level && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Class:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"All Classes\",\n            value: filters.className || undefined,\n            onChange: value => handleFilterChange(\"className\", value),\n            allowClear: true,\n            style: {\n              width: 120\n            },\n            children: getClassesForLevel(filters.level).map(cls => /*#__PURE__*/_jsxDEV(Option, {\n              value: cls,\n              children: [\"Class \", cls]\n            }, cls, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 13\n        }, this), filters.level && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Subject:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"All Subjects\",\n            value: filters.subject || undefined,\n            onChange: value => handleFilterChange(\"subject\", value),\n            allowClear: true,\n            style: {\n              width: 150\n            },\n            children: getSubjectsForLevel(filters.level).map(subject => /*#__PURE__*/_jsxDEV(Option, {\n              value: subject,\n              children: subject\n            }, subject, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Search:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Search, {\n            placeholder: \"Search materials...\",\n            value: searchText,\n            onChange: e => setSearchText(e.target.value),\n            style: {\n              width: 200\n            },\n            allowClear: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"materials-table\",\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: filteredMaterials,\n        rowKey: \"_id\",\n        loading: loading,\n        pagination: {\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `Total ${total} materials`\n        },\n        scroll: {\n          x: 1000\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 374,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 286,\n    columnNumber: 5\n  }, this);\n}\n_s(StudyMaterialManager, \"FrxIS2V+7XxMMpb6mBvZGicScfE=\", false, function () {\n  return [useDispatch];\n});\n_c = StudyMaterialManager;\nexport default StudyMaterialManager;\nvar _c;\n$RefreshReg$(_c, \"StudyMaterialManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Space", "Select", "Input", "message", "Modal", "Tag", "<PERSON><PERSON><PERSON>", "useDispatch", "HideLoading", "ShowLoading", "getAllStudyMaterials", "deleteVideo", "deleteNote", "deletePastPaper", "deleteBook", "deleteLiterature", "primarySubjects", "secondarySubjects", "advanceSubjects", "FaVideo", "FaFileAlt", "FaBook", "FaGraduationCap", "FaEdit", "FaTrash", "FaEye", "FaFilter", "FaSearch", "jsxDEV", "_jsxDEV", "Option", "Search", "StudyMaterialManager", "onEdit", "_s", "dispatch", "materials", "setMaterials", "loading", "setLoading", "filters", "setFilters", "materialType", "level", "className", "subject", "searchText", "setSearchText", "getSubjectsForLevel", "getClassesForLevel", "fetchMaterials", "response", "status", "data", "success", "error", "console", "handleFilterChange", "key", "value", "prev", "newFilters", "handleDelete", "material", "confirm", "title", "type", "replace", "content", "okText", "okType", "cancelText", "onOk", "_id", "Error", "_response$data", "getMaterialIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getMaterialTypeLabel", "filteredMaterials", "filter", "toLowerCase", "includes", "columns", "width", "render", "_", "record", "children", "color", "dataIndex", "char<PERSON>t", "toUpperCase", "slice", "year", "icon", "size", "onClick", "danger", "placeholder", "undefined", "onChange", "allowClear", "style", "map", "cls", "e", "target", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "scroll", "x", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/StudyMaterials/StudyMaterialManager.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { Table, Button, Space, Select, Input, message, Modal, Tag, Tooltip } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport {\n  getAllStudyMaterials,\n  deleteVideo,\n  deleteNote,\n  deletePastPaper,\n  deleteBook,\n  deleteLiterature\n} from \"../../../apicalls/study\";\nimport { primarySubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport {\n  FaVideo,\n  FaFileAlt,\n  FaBook,\n  FaGraduationCap,\n  FaEdit,\n  FaTrash,\n  FaEye,\n  FaFilter,\n  FaSearch\n} from \"react-icons/fa\";\nimport \"./StudyMaterialManager.css\";\n\nconst { Option } = Select;\nconst { Search } = Input;\n\nfunction StudyMaterialManager({ onEdit }) {\n  const dispatch = useDispatch();\n  const [materials, setMaterials] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [filters, setFilters] = useState({\n    materialType: \"\",\n    level: \"\",\n    className: \"\",\n    subject: \"\"\n  });\n  const [searchText, setSearchText] = useState(\"\");\n\n  // Get subjects based on level\n  const getSubjectsForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\"];\n      case \"secondary\":\n        return [\"6\", \"7\", \"8\", \"9\", \"10\", \"11\"];\n      case \"advance\":\n        return [\"12\", \"13\"];\n      default:\n        return [];\n    }\n  };\n\n  // Fetch materials\n  const fetchMaterials = async () => {\n    try {\n      setLoading(true);\n      // Skip global loading for faster admin experience\n\n      const response = await getAllStudyMaterials(filters);\n\n      if (response.status === 200 && response.data.success) {\n        setMaterials(response.data.data || []);\n      } else {\n        message.error(\"Failed to fetch study materials\");\n        setMaterials([]);\n      }\n    } catch (error) {\n      console.error(\"Error fetching materials:\", error);\n      message.error(\"Failed to fetch study materials\");\n      setMaterials([]);\n    } finally {\n      setLoading(false);\n      // No global loading spinner for faster UX\n    }\n  };\n\n  useEffect(() => {\n    fetchMaterials();\n  }, [filters]);\n\n  // Handle filter changes\n  const handleFilterChange = (key, value) => {\n    setFilters(prev => {\n      const newFilters = { ...prev, [key]: value };\n      \n      // Reset dependent filters\n      if (key === \"level\") {\n        newFilters.className = \"\";\n        newFilters.subject = \"\";\n      }\n      \n      return newFilters;\n    });\n  };\n\n  // Handle delete\n  const handleDelete = async (material) => {\n    Modal.confirm({\n      title: `Delete ${material.type.replace(\"-\", \" \")}`,\n      content: `Are you sure you want to delete \"${material.title}\"? This action cannot be undone.`,\n      okText: \"Delete\",\n      okType: \"danger\",\n      cancelText: \"Cancel\",\n      onOk: async () => {\n        try {\n          // Skip global loading for faster delete operation\n\n          let response;\n          switch (material.type) {\n            case \"videos\":\n              response = await deleteVideo(material._id);\n              break;\n            case \"study-notes\":\n              response = await deleteNote(material._id);\n              break;\n            case \"past-papers\":\n              response = await deletePastPaper(material._id);\n              break;\n            case \"books\":\n              response = await deleteBook(material._id);\n              break;\n            case \"literature\":\n              response = await deleteLiterature(material._id);\n              break;\n            default:\n              throw new Error(\"Invalid material type\");\n          }\n\n          if (response.status === 200 && response.data.success) {\n            message.success(response.data.message);\n            fetchMaterials(); // Refresh the list\n          } else {\n            message.error(response.data?.message || \"Failed to delete material\");\n          }\n        } catch (error) {\n          console.error(\"Error deleting material:\", error);\n          message.error(\"Failed to delete material\");\n        } finally {\n          dispatch(HideLoading());\n        }\n      }\n    });\n  };\n\n  // Get material type icon\n  const getMaterialIcon = (type) => {\n    switch (type) {\n      case \"videos\":\n        return <FaVideo className=\"material-icon video\" />;\n      case \"study-notes\":\n        return <FaFileAlt className=\"material-icon note\" />;\n      case \"past-papers\":\n        return <FaGraduationCap className=\"material-icon paper\" />;\n      case \"books\":\n        return <FaBook className=\"material-icon book\" />;\n      default:\n        return <FaFileAlt className=\"material-icon\" />;\n    }\n  };\n\n  // Get material type label\n  const getMaterialTypeLabel = (type) => {\n    switch (type) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return type;\n    }\n  };\n\n  // Filter materials based on search text\n  const filteredMaterials = materials.filter(material =>\n    material.title.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.subject.toLowerCase().includes(searchText.toLowerCase()) ||\n    material.className.toLowerCase().includes(searchText.toLowerCase())\n  );\n\n  // Table columns\n  const columns = [\n    {\n      title: \"Material\",\n      key: \"material\",\n      width: \"30%\",\n      render: (_, record) => (\n        <div className=\"material-info\">\n          <div className=\"material-header\">\n            {getMaterialIcon(record.type)}\n            <div className=\"material-details\">\n              <div className=\"material-title\">{record.title}</div>\n              <div className=\"material-meta\">\n                <Tag color=\"blue\">{getMaterialTypeLabel(record.type)}</Tag>\n                <span className=\"meta-text\">{record.subject} • Class {record.className}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: \"Level\",\n      dataIndex: \"level\",\n      key: \"level\",\n      width: \"10%\",\n      render: (level) => (\n        <Tag color={level === \"primary\" ? \"green\" : level === \"secondary\" ? \"orange\" : \"purple\"}>\n          {level.charAt(0).toUpperCase() + level.slice(1)}\n        </Tag>\n      ),\n    },\n    {\n      title: \"Class\",\n      dataIndex: \"className\",\n      key: \"className\",\n      width: \"10%\",\n      render: (className) => <span className=\"class-badge\">Class {className}</span>,\n    },\n    {\n      title: \"Subject\",\n      dataIndex: \"subject\",\n      key: \"subject\",\n      width: \"15%\",\n    },\n    {\n      title: \"Year\",\n      dataIndex: \"year\",\n      key: \"year\",\n      width: \"10%\",\n      render: (year) => year || \"-\",\n    },\n    {\n      title: \"Actions\",\n      key: \"actions\",\n      width: \"25%\",\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"Edit material\">\n            <Button\n              type=\"primary\"\n              icon={<FaEdit />}\n              size=\"small\"\n              onClick={() => onEdit(record)}\n            >\n              Edit\n            </Button>\n          </Tooltip>\n          \n          <Tooltip title=\"Delete material\">\n            <Button\n              danger\n              icon={<FaTrash />}\n              size=\"small\"\n              onClick={() => handleDelete(record)}\n            >\n              Delete\n            </Button>\n          </Tooltip>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"study-material-manager\">\n      <div className=\"manager-header\">\n        <h2>Study Materials Management</h2>\n        <p>Manage all uploaded study materials - edit, delete, and organize content</p>\n      </div>\n\n      {/* Filters */}\n      <div className=\"filters-section\">\n        <div className=\"filters-row\">\n          <div className=\"filter-group\">\n            <label>Material Type:</label>\n            <Select\n              placeholder=\"All Types\"\n              value={filters.materialType || undefined}\n              onChange={(value) => handleFilterChange(\"materialType\", value)}\n              allowClear\n              style={{ width: 150 }}\n            >\n              <Option value=\"videos\">Videos</Option>\n              <Option value=\"study-notes\">Study Notes</Option>\n              <Option value=\"past-papers\">Past Papers</Option>\n              <Option value=\"books\">Books</Option>\n            </Select>\n          </div>\n\n          <div className=\"filter-group\">\n            <label>Level:</label>\n            <Select\n              placeholder=\"All Levels\"\n              value={filters.level || undefined}\n              onChange={(value) => handleFilterChange(\"level\", value)}\n              allowClear\n              style={{ width: 120 }}\n            >\n              <Option value=\"primary\">Primary</Option>\n              <Option value=\"secondary\">Secondary</Option>\n              <Option value=\"advance\">Advance</Option>\n            </Select>\n          </div>\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Class:</label>\n              <Select\n                placeholder=\"All Classes\"\n                value={filters.className || undefined}\n                onChange={(value) => handleFilterChange(\"className\", value)}\n                allowClear\n                style={{ width: 120 }}\n              >\n                {getClassesForLevel(filters.level).map(cls => (\n                  <Option key={cls} value={cls}>Class {cls}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          {filters.level && (\n            <div className=\"filter-group\">\n              <label>Subject:</label>\n              <Select\n                placeholder=\"All Subjects\"\n                value={filters.subject || undefined}\n                onChange={(value) => handleFilterChange(\"subject\", value)}\n                allowClear\n                style={{ width: 150 }}\n              >\n                {getSubjectsForLevel(filters.level).map(subject => (\n                  <Option key={subject} value={subject}>{subject}</Option>\n                ))}\n              </Select>\n            </div>\n          )}\n\n          <div className=\"filter-group\">\n            <label>Search:</label>\n            <Search\n              placeholder=\"Search materials...\"\n              value={searchText}\n              onChange={(e) => setSearchText(e.target.value)}\n              style={{ width: 200 }}\n              allowClear\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Materials Table */}\n      <div className=\"materials-table\">\n        <Table\n          columns={columns}\n          dataSource={filteredMaterials}\n          rowKey=\"_id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `Total ${total} materials`,\n          }}\n          scroll={{ x: 1000 }}\n        />\n      </div>\n    </div>\n  );\n}\n\nexport default StudyMaterialManager;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAEC,OAAO,QAAQ,MAAM;AACxF,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SACEC,oBAAoB,EACpBC,WAAW,EACXC,UAAU,EACVC,eAAe,EACfC,UAAU,EACVC,gBAAgB,QACX,yBAAyB;AAChC,SAASC,eAAe,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,wBAAwB;AAC5F,SACEC,OAAO,EACPC,SAAS,EACTC,MAAM,EACNC,eAAe,EACfC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,QAAQ,QACH,gBAAgB;AACvB,OAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAM;EAAEC;AAAO,CAAC,GAAG7B,MAAM;AACzB,MAAM;EAAE8B;AAAO,CAAC,GAAG7B,KAAK;AAExB,SAAS8B,oBAAoBA,CAAC;EAAEC;AAAO,CAAC,EAAE;EAAAC,EAAA;EACxC,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC;IACrC8C,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,EAAE;IACTC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAMoD,mBAAmB,GAAIL,KAAK,IAAK;IACrC,QAAQA,KAAK;MACX,KAAK,SAAS;QACZ,OAAO3B,eAAe;MACxB,KAAK,WAAW;QACd,OAAOC,iBAAiB;MAC1B,KAAK,SAAS;QACZ,OAAOC,eAAe;MACxB;QACE,OAAO,EAAE;IACb;EACF,CAAC;;EAED;EACA,MAAM+B,kBAAkB,GAAIN,KAAK,IAAK;IACpC,QAAQA,KAAK;MACX,KAAK,SAAS;QACZ,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAClC,KAAK,WAAW;QACd,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;MACzC,KAAK,SAAS;QACZ,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;MACrB;QACE,OAAO,EAAE;IACb;EACF,CAAC;;EAED;EACA,MAAMO,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB;;MAEA,MAAMY,QAAQ,GAAG,MAAMzC,oBAAoB,CAAC8B,OAAO,CAAC;MAEpD,IAAIW,QAAQ,CAACC,MAAM,KAAK,GAAG,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACpDjB,YAAY,CAACc,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACxC,CAAC,MAAM;QACLlD,OAAO,CAACoD,KAAK,CAAC,iCAAiC,CAAC;QAChDlB,YAAY,CAAC,EAAE,CAAC;MAClB;IACF,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDpD,OAAO,CAACoD,KAAK,CAAC,iCAAiC,CAAC;MAChDlB,YAAY,CAAC,EAAE,CAAC;IAClB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;MACjB;IACF;EACF,CAAC;;EAED1C,SAAS,CAAC,MAAM;IACdqD,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACV,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMiB,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACzClB,UAAU,CAACmB,IAAI,IAAI;MACjB,MAAMC,UAAU,GAAG;QAAE,GAAGD,IAAI;QAAE,CAACF,GAAG,GAAGC;MAAM,CAAC;;MAE5C;MACA,IAAID,GAAG,KAAK,OAAO,EAAE;QACnBG,UAAU,CAACjB,SAAS,GAAG,EAAE;QACzBiB,UAAU,CAAChB,OAAO,GAAG,EAAE;MACzB;MAEA,OAAOgB,UAAU;IACnB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvC3D,KAAK,CAAC4D,OAAO,CAAC;MACZC,KAAK,EAAG,UAASF,QAAQ,CAACG,IAAI,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAE,EAAC;MAClDC,OAAO,EAAG,oCAAmCL,QAAQ,CAACE,KAAM,kCAAiC;MAC7FI,MAAM,EAAE,QAAQ;MAChBC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,QAAQ;MACpBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF;;UAEA,IAAIrB,QAAQ;UACZ,QAAQY,QAAQ,CAACG,IAAI;YACnB,KAAK,QAAQ;cACXf,QAAQ,GAAG,MAAMxC,WAAW,CAACoD,QAAQ,CAACU,GAAG,CAAC;cAC1C;YACF,KAAK,aAAa;cAChBtB,QAAQ,GAAG,MAAMvC,UAAU,CAACmD,QAAQ,CAACU,GAAG,CAAC;cACzC;YACF,KAAK,aAAa;cAChBtB,QAAQ,GAAG,MAAMtC,eAAe,CAACkD,QAAQ,CAACU,GAAG,CAAC;cAC9C;YACF,KAAK,OAAO;cACVtB,QAAQ,GAAG,MAAMrC,UAAU,CAACiD,QAAQ,CAACU,GAAG,CAAC;cACzC;YACF,KAAK,YAAY;cACftB,QAAQ,GAAG,MAAMpC,gBAAgB,CAACgD,QAAQ,CAACU,GAAG,CAAC;cAC/C;YACF;cACE,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;UAC5C;UAEA,IAAIvB,QAAQ,CAACC,MAAM,KAAK,GAAG,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;YACpDnD,OAAO,CAACmD,OAAO,CAACH,QAAQ,CAACE,IAAI,CAAClD,OAAO,CAAC;YACtC+C,cAAc,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,MAAM;YAAA,IAAAyB,cAAA;YACLxE,OAAO,CAACoD,KAAK,CAAC,EAAAoB,cAAA,GAAAxB,QAAQ,CAACE,IAAI,cAAAsB,cAAA,uBAAbA,cAAA,CAAexE,OAAO,KAAI,2BAA2B,CAAC;UACtE;QACF,CAAC,CAAC,OAAOoD,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChDpD,OAAO,CAACoD,KAAK,CAAC,2BAA2B,CAAC;QAC5C,CAAC,SAAS;UACRpB,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;QACzB;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMoE,eAAe,GAAIV,IAAI,IAAK;IAChC,QAAQA,IAAI;MACV,KAAK,QAAQ;QACX,oBAAOrC,OAAA,CAACV,OAAO;UAACyB,SAAS,EAAC;QAAqB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpD,KAAK,aAAa;QAChB,oBAAOnD,OAAA,CAACT,SAAS;UAACwB,SAAS,EAAC;QAAoB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACrD,KAAK,aAAa;QAChB,oBAAOnD,OAAA,CAACP,eAAe;UAACsB,SAAS,EAAC;QAAqB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5D,KAAK,OAAO;QACV,oBAAOnD,OAAA,CAACR,MAAM;UAACuB,SAAS,EAAC;QAAoB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAClD;QACE,oBAAOnD,OAAA,CAACT,SAAS;UAACwB,SAAS,EAAC;QAAe;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAClD;EACF,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAIf,IAAI,IAAK;IACrC,QAAQA,IAAI;MACV,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB,KAAK,aAAa;QAChB,OAAO,YAAY;MACrB,KAAK,aAAa;QAChB,OAAO,YAAY;MACrB,KAAK,OAAO;QACV,OAAO,MAAM;MACf;QACE,OAAOA,IAAI;IACf;EACF,CAAC;;EAED;EACA,MAAMgB,iBAAiB,GAAG9C,SAAS,CAAC+C,MAAM,CAACpB,QAAQ,IACjDA,QAAQ,CAACE,KAAK,CAACmB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvC,UAAU,CAACsC,WAAW,CAAC,CAAC,CAAC,IAC/DrB,QAAQ,CAAClB,OAAO,CAACuC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvC,UAAU,CAACsC,WAAW,CAAC,CAAC,CAAC,IACjErB,QAAQ,CAACnB,SAAS,CAACwC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvC,UAAU,CAACsC,WAAW,CAAC,CAAC,CACpE,CAAC;;EAED;EACA,MAAME,OAAO,GAAG,CACd;IACErB,KAAK,EAAE,UAAU;IACjBP,GAAG,EAAE,UAAU;IACf6B,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAM,kBAChB7D,OAAA;MAAKe,SAAS,EAAC,eAAe;MAAA+C,QAAA,eAC5B9D,OAAA;QAAKe,SAAS,EAAC,iBAAiB;QAAA+C,QAAA,GAC7Bf,eAAe,CAACc,MAAM,CAACxB,IAAI,CAAC,eAC7BrC,OAAA;UAAKe,SAAS,EAAC,kBAAkB;UAAA+C,QAAA,gBAC/B9D,OAAA;YAAKe,SAAS,EAAC,gBAAgB;YAAA+C,QAAA,EAAED,MAAM,CAACzB;UAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpDnD,OAAA;YAAKe,SAAS,EAAC,eAAe;YAAA+C,QAAA,gBAC5B9D,OAAA,CAACxB,GAAG;cAACuF,KAAK,EAAC,MAAM;cAAAD,QAAA,EAAEV,oBAAoB,CAACS,MAAM,CAACxB,IAAI;YAAC;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3DnD,OAAA;cAAMe,SAAS,EAAC,WAAW;cAAA+C,QAAA,GAAED,MAAM,CAAC7C,OAAO,EAAC,gBAAS,EAAC6C,MAAM,CAAC9C,SAAS;YAAA;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEf,KAAK,EAAE,OAAO;IACd4B,SAAS,EAAE,OAAO;IAClBnC,GAAG,EAAE,OAAO;IACZ6B,KAAK,EAAE,KAAK;IACZC,MAAM,EAAG7C,KAAK,iBACZd,OAAA,CAACxB,GAAG;MAACuF,KAAK,EAAEjD,KAAK,KAAK,SAAS,GAAG,OAAO,GAAGA,KAAK,KAAK,WAAW,GAAG,QAAQ,GAAG,QAAS;MAAAgD,QAAA,EACrFhD,KAAK,CAACmD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGpD,KAAK,CAACqD,KAAK,CAAC,CAAC;IAAC;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C;EAET,CAAC,EACD;IACEf,KAAK,EAAE,OAAO;IACd4B,SAAS,EAAE,WAAW;IACtBnC,GAAG,EAAE,WAAW;IAChB6B,KAAK,EAAE,KAAK;IACZC,MAAM,EAAG5C,SAAS,iBAAKf,OAAA;MAAMe,SAAS,EAAC,aAAa;MAAA+C,QAAA,GAAC,QAAM,EAAC/C,SAAS;IAAA;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO;EAC9E,CAAC,EACD;IACEf,KAAK,EAAE,SAAS;IAChB4B,SAAS,EAAE,SAAS;IACpBnC,GAAG,EAAE,SAAS;IACd6B,KAAK,EAAE;EACT,CAAC,EACD;IACEtB,KAAK,EAAE,MAAM;IACb4B,SAAS,EAAE,MAAM;IACjBnC,GAAG,EAAE,MAAM;IACX6B,KAAK,EAAE,KAAK;IACZC,MAAM,EAAGS,IAAI,IAAKA,IAAI,IAAI;EAC5B,CAAC,EACD;IACEhC,KAAK,EAAE,SAAS;IAChBP,GAAG,EAAE,SAAS;IACd6B,KAAK,EAAE,KAAK;IACZC,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAM,kBAChB7D,OAAA,CAAC7B,KAAK;MAAA2F,QAAA,gBACJ9D,OAAA,CAACvB,OAAO;QAAC2D,KAAK,EAAC,eAAe;QAAA0B,QAAA,eAC5B9D,OAAA,CAAC9B,MAAM;UACLmE,IAAI,EAAC,SAAS;UACdgC,IAAI,eAAErE,OAAA,CAACN,MAAM;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACjBmB,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEA,CAAA,KAAMnE,MAAM,CAACyD,MAAM,CAAE;UAAAC,QAAA,EAC/B;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEVnD,OAAA,CAACvB,OAAO;QAAC2D,KAAK,EAAC,iBAAiB;QAAA0B,QAAA,eAC9B9D,OAAA,CAAC9B,MAAM;UACLsG,MAAM;UACNH,IAAI,eAAErE,OAAA,CAACL,OAAO;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAClBmB,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEA,CAAA,KAAMtC,YAAY,CAAC4B,MAAM,CAAE;UAAAC,QAAA,EACrC;QAED;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAEX,CAAC,CACF;EAED,oBACEnD,OAAA;IAAKe,SAAS,EAAC,wBAAwB;IAAA+C,QAAA,gBACrC9D,OAAA;MAAKe,SAAS,EAAC,gBAAgB;MAAA+C,QAAA,gBAC7B9D,OAAA;QAAA8D,QAAA,EAAI;MAA0B;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCnD,OAAA;QAAA8D,QAAA,EAAG;MAAwE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5E,CAAC,eAGNnD,OAAA;MAAKe,SAAS,EAAC,iBAAiB;MAAA+C,QAAA,eAC9B9D,OAAA;QAAKe,SAAS,EAAC,aAAa;QAAA+C,QAAA,gBAC1B9D,OAAA;UAAKe,SAAS,EAAC,cAAc;UAAA+C,QAAA,gBAC3B9D,OAAA;YAAA8D,QAAA,EAAO;UAAc;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7BnD,OAAA,CAAC5B,MAAM;YACLqG,WAAW,EAAC,WAAW;YACvB3C,KAAK,EAAEnB,OAAO,CAACE,YAAY,IAAI6D,SAAU;YACzCC,QAAQ,EAAG7C,KAAK,IAAKF,kBAAkB,CAAC,cAAc,EAAEE,KAAK,CAAE;YAC/D8C,UAAU;YACVC,KAAK,EAAE;cAAEnB,KAAK,EAAE;YAAI,CAAE;YAAAI,QAAA,gBAEtB9D,OAAA,CAACC,MAAM;cAAC6B,KAAK,EAAC,QAAQ;cAAAgC,QAAA,EAAC;YAAM;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtCnD,OAAA,CAACC,MAAM;cAAC6B,KAAK,EAAC,aAAa;cAAAgC,QAAA,EAAC;YAAW;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChDnD,OAAA,CAACC,MAAM;cAAC6B,KAAK,EAAC,aAAa;cAAAgC,QAAA,EAAC;YAAW;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChDnD,OAAA,CAACC,MAAM;cAAC6B,KAAK,EAAC,OAAO;cAAAgC,QAAA,EAAC;YAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENnD,OAAA;UAAKe,SAAS,EAAC,cAAc;UAAA+C,QAAA,gBAC3B9D,OAAA;YAAA8D,QAAA,EAAO;UAAM;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrBnD,OAAA,CAAC5B,MAAM;YACLqG,WAAW,EAAC,YAAY;YACxB3C,KAAK,EAAEnB,OAAO,CAACG,KAAK,IAAI4D,SAAU;YAClCC,QAAQ,EAAG7C,KAAK,IAAKF,kBAAkB,CAAC,OAAO,EAAEE,KAAK,CAAE;YACxD8C,UAAU;YACVC,KAAK,EAAE;cAAEnB,KAAK,EAAE;YAAI,CAAE;YAAAI,QAAA,gBAEtB9D,OAAA,CAACC,MAAM;cAAC6B,KAAK,EAAC,SAAS;cAAAgC,QAAA,EAAC;YAAO;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACxCnD,OAAA,CAACC,MAAM;cAAC6B,KAAK,EAAC,WAAW;cAAAgC,QAAA,EAAC;YAAS;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC5CnD,OAAA,CAACC,MAAM;cAAC6B,KAAK,EAAC,SAAS;cAAAgC,QAAA,EAAC;YAAO;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELxC,OAAO,CAACG,KAAK,iBACZd,OAAA;UAAKe,SAAS,EAAC,cAAc;UAAA+C,QAAA,gBAC3B9D,OAAA;YAAA8D,QAAA,EAAO;UAAM;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrBnD,OAAA,CAAC5B,MAAM;YACLqG,WAAW,EAAC,aAAa;YACzB3C,KAAK,EAAEnB,OAAO,CAACI,SAAS,IAAI2D,SAAU;YACtCC,QAAQ,EAAG7C,KAAK,IAAKF,kBAAkB,CAAC,WAAW,EAAEE,KAAK,CAAE;YAC5D8C,UAAU;YACVC,KAAK,EAAE;cAAEnB,KAAK,EAAE;YAAI,CAAE;YAAAI,QAAA,EAErB1C,kBAAkB,CAACT,OAAO,CAACG,KAAK,CAAC,CAACgE,GAAG,CAACC,GAAG,iBACxC/E,OAAA,CAACC,MAAM;cAAW6B,KAAK,EAAEiD,GAAI;cAAAjB,QAAA,GAAC,QAAM,EAACiB,GAAG;YAAA,GAA3BA,GAAG;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAiC,CAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,EAEAxC,OAAO,CAACG,KAAK,iBACZd,OAAA;UAAKe,SAAS,EAAC,cAAc;UAAA+C,QAAA,gBAC3B9D,OAAA;YAAA8D,QAAA,EAAO;UAAQ;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACvBnD,OAAA,CAAC5B,MAAM;YACLqG,WAAW,EAAC,cAAc;YAC1B3C,KAAK,EAAEnB,OAAO,CAACK,OAAO,IAAI0D,SAAU;YACpCC,QAAQ,EAAG7C,KAAK,IAAKF,kBAAkB,CAAC,SAAS,EAAEE,KAAK,CAAE;YAC1D8C,UAAU;YACVC,KAAK,EAAE;cAAEnB,KAAK,EAAE;YAAI,CAAE;YAAAI,QAAA,EAErB3C,mBAAmB,CAACR,OAAO,CAACG,KAAK,CAAC,CAACgE,GAAG,CAAC9D,OAAO,iBAC7ChB,OAAA,CAACC,MAAM;cAAe6B,KAAK,EAAEd,OAAQ;cAAA8C,QAAA,EAAE9C;YAAO,GAAjCA,OAAO;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACxD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN,eAEDnD,OAAA;UAAKe,SAAS,EAAC,cAAc;UAAA+C,QAAA,gBAC3B9D,OAAA;YAAA8D,QAAA,EAAO;UAAO;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtBnD,OAAA,CAACE,MAAM;YACLuE,WAAW,EAAC,qBAAqB;YACjC3C,KAAK,EAAEb,UAAW;YAClB0D,QAAQ,EAAGK,CAAC,IAAK9D,aAAa,CAAC8D,CAAC,CAACC,MAAM,CAACnD,KAAK,CAAE;YAC/C+C,KAAK,EAAE;cAAEnB,KAAK,EAAE;YAAI,CAAE;YACtBkB,UAAU;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNnD,OAAA;MAAKe,SAAS,EAAC,iBAAiB;MAAA+C,QAAA,eAC9B9D,OAAA,CAAC/B,KAAK;QACJwF,OAAO,EAAEA,OAAQ;QACjByB,UAAU,EAAE7B,iBAAkB;QAC9B8B,MAAM,EAAC,KAAK;QACZ1E,OAAO,EAAEA,OAAQ;QACjB2E,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAM,SAAQA,KAAM;QACvC,CAAE;QACFC,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK;MAAE;QAAA3C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC9C,EAAA,CAzWQF,oBAAoB;EAAA,QACVzB,WAAW;AAAA;AAAAkH,EAAA,GADrBzF,oBAAoB;AA2W7B,eAAeA,oBAAoB;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}