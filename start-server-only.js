// Start only the server
const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting BrainWave Server...');

// Start server
const serverPath = path.join(__dirname, 'server');
console.log('📍 Server path:', serverPath);

const server = spawn('npm', ['start'], {
  cwd: serverPath,
  stdio: 'inherit',
  shell: true
});

server.on('error', (error) => {
  console.error('❌ Server error:', error);
});

server.on('close', (code) => {
  console.log(`🔴 Server process exited with code ${code}`);
});

console.log('✅ Server starting...');
console.log('🌐 Server will be available at: http://localhost:5000');
console.log('📊 Health check: http://localhost:5000/api/health');

// Keep the process alive
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  server.kill();
  process.exit(0);
});
