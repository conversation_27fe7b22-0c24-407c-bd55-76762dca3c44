const mongoose = require("mongoose");
const dns = require('dns');

// Set DNS servers to Google's public DNS to resolve MongoDB Atlas hostnames
dns.setServers(['*******', '*******', '*******', '*******']);

// MongoDB connection options - OPTIMIZED FOR SLOW OPERATIONS
const connectionOptions = {
  serverSelectionTimeoutMS: 15000, // Increased for slow networks
  socketTimeoutMS: 30000, // Increased for slow operations
  connectTimeoutMS: 15000, // Increased for slow connections
  family: 4, // Use IPv4, skip trying IPv6
  maxPoolSize: 2, // Minimal pool size for stability
  minPoolSize: 1, // Minimum connections
  maxIdleTimeMS: 30000, // Longer idle time
  retryWrites: true, // Retry failed writes
  retryReads: true, // Retry failed reads
  directConnection: false, // Allow multiple hosts
  heartbeatFrequencyMS: 10000, // Less frequent heartbeats
  serverSelectionRetryDelayMS: 2000, // Slower retry for stability
  bufferCommands: false, // Disable buffering for immediate failures
  bufferMaxEntries: 0, // No buffering
};

// Test DNS resolution for MongoDB Atlas
const testDNSResolution = () => {
  return new Promise((resolve) => {
    dns.lookup('cluster0.cdg8fdn.mongodb.net', (err, address) => {
      if (err) {
        console.log('🔍 DNS resolution failed for MongoDB Atlas hostname');
        console.log('   Error:', err.message);
        resolve(false);
      } else {
        console.log('✅ DNS resolution successful for MongoDB Atlas');
        console.log('   Resolved to:', address);
        resolve(true);
      }
    });
  });
};

// Connect to MongoDB with proper error handling and fallback
const connectDB = async (retryCount = 0) => {
  const maxRetries = 3;

  try {
    console.log(`Attempting to connect to MongoDB (attempt ${retryCount + 1}/${maxRetries + 1})...`);

    // Test DNS resolution first
    const dnsWorking = await testDNSResolution();
    if (!dnsWorking && retryCount === 0) {
      console.log('⚠️ DNS resolution failed, but continuing with connection attempt...');
    }

    // Try primary connection first
    let mongoUrl = process.env.MONGO_URL;

    // If primary connection failed before, try fallback
    if (retryCount > 0 && process.env.MONGO_URL_FALLBACK) {
      console.log("🔄 Trying fallback connection string...");
      mongoUrl = process.env.MONGO_URL_FALLBACK;
    }

    await mongoose.connect(mongoUrl, connectionOptions);
    console.log("✅ Mongo Db Connection Successful");
  } catch (error) {
    console.error("❌ Mongo Db Connection Failed:", error.message);

    // Specific error handling for common issues
    if (error.message.includes('ETIMEOUT') || error.message.includes('queryTxt')) {
      console.error("🔍 DNS/Network timeout error detected. Possible solutions:");
      console.error("   1. Change DNS servers to Google (*******, *******) or Cloudflare (*******)");
      console.error("   2. Add 0.0.0.0/0 to MongoDB Atlas IP whitelist for testing");
      console.error("   3. Check firewall/antivirus blocking MongoDB connections");
      console.error("   4. Verify internet connectivity");
      console.error("   5. Try running: ipconfig /flushdns (Windows) to clear DNS cache");
    }

    // Retry with exponential backoff
    if (retryCount < maxRetries) {
      const delay = Math.pow(2, retryCount) * 3000; // 3s, 6s, 12s
      console.log(`⏳ Retrying connection in ${delay/1000} seconds...`);
      setTimeout(() => connectDB(retryCount + 1), delay);
    } else {
      console.error("❌ Max retry attempts reached. Please check your MongoDB configuration.");
      console.error("💡 The server will continue running, but database features won't work.");
      console.error("💡 You can manually fix DNS by changing to Google DNS (*******) in network settings.");
    }
  }
};

// Set mongoose global options for timeout handling
mongoose.set('bufferCommands', false);
mongoose.set('bufferMaxEntries', 0);

// Initialize connection
connectDB();

const connection = mongoose.connection;

connection.on("connected", () => {
  console.log("✅ MongoDB Connected Successfully");
});

connection.on("error", (err) => {
  console.error("❌ MongoDB Connection Error:", err.message);
});

connection.on("disconnected", () => {
  console.log("⚠️ MongoDB Disconnected. Attempting to reconnect...");
});

// Handle process termination
process.on('SIGINT', async () => {
  await mongoose.connection.close();
  console.log('MongoDB connection closed through app termination');
  process.exit(0);
});

module.exports = connection;