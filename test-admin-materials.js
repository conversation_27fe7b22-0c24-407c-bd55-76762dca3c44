// Test admin study materials functionality
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

// Test admin credentials (you may need to adjust these)
const adminCredentials = {
  email: '<EMAIL>',
  password: 'admin123'
};

async function testAdminMaterials() {
  try {
    console.log('🧪 Testing Admin Study Materials System...');
    console.log('📍 Server URL:', BASE_URL);
    
    // Test 1: Check server health
    console.log('\n1️⃣ Testing server health...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Server health:', healthResponse.data.status);
    
    // Test 2: Try to login as admin (this might fail if admin doesn't exist)
    console.log('\n2️⃣ Testing admin authentication...');
    try {
      const loginResponse = await axios.post(`${BASE_URL}/users/login`, adminCredentials);
      
      if (loginResponse.data.success) {
        console.log('✅ Admin login successful');
        const token = loginResponse.data.data;
        
        // Test 3: Get all study materials
        console.log('\n3️⃣ Testing study materials retrieval...');
        const materialsResponse = await axios.get(`${BASE_URL}/study/admin/all-materials`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (materialsResponse.data.success) {
          console.log('✅ Study materials retrieved successfully');
          console.log('📊 Total materials:', materialsResponse.data.data.length);
          
          // Show breakdown by type
          const materials = materialsResponse.data.data;
          const breakdown = materials.reduce((acc, material) => {
            acc[material.type] = (acc[material.type] || 0) + 1;
            return acc;
          }, {});
          
          console.log('📋 Materials breakdown:', breakdown);
        } else {
          console.log('⚠️ Study materials retrieval failed:', materialsResponse.data.message);
        }
        
        // Test 4: Test study content endpoint
        console.log('\n4️⃣ Testing study content endpoint...');
        const contentResponse = await axios.post(`${BASE_URL}/study/get-study-content`, {
          content: 'videos',
          className: 'all',
          subject: 'all'
        }, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (contentResponse.data.success) {
          console.log('✅ Study content retrieved successfully');
          console.log('📊 Videos found:', contentResponse.data.data.length);
        } else {
          console.log('⚠️ Study content retrieval failed:', contentResponse.data.message);
        }
        
      } else {
        console.log('❌ Admin login failed:', loginResponse.data.message);
      }
      
    } catch (loginError) {
      console.log('⚠️ Admin login failed (admin might not exist):', loginError.response?.data?.message || loginError.message);
      console.log('💡 You may need to create an admin user first');
    }
    
    // Test 5: Test public endpoints
    console.log('\n5️⃣ Testing public endpoints...');
    try {
      const publicResponse = await axios.get(`${BASE_URL}/users/get-current-user`);
      console.log('📋 Public endpoint response status:', publicResponse.status);
    } catch (error) {
      console.log('⚠️ Public endpoint test (expected to fail without auth):', error.response?.status);
    }
    
    console.log('\n✅ Admin materials system test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('📋 Response status:', error.response.status);
      console.error('📋 Response data:', error.response.data);
    }
  }
}

testAdminMaterials();
