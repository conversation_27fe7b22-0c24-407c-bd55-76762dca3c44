// Start server with database connection fix
const { spawn } = require('child_process');
const path = require('path');
const mongoose = require('mongoose');

console.log('🚀 Starting BrainWave with Database Fix...');

// Load environment variables
require('dotenv').config({ path: './server/.env' });

async function testDatabaseFirst() {
  try {
    console.log('🔍 Testing database connection first...');
    
    // Optimized connection options for slow networks
    const options = {
      serverSelectionTimeoutMS: 15000,
      socketTimeoutMS: 30000,
      connectTimeoutMS: 15000,
      family: 4,
      maxPoolSize: 2,
      minPoolSize: 1,
      retryWrites: true,
      retryReads: true,
      bufferCommands: false,
      bufferMaxEntries: 0
    };
    
    await mongoose.connect(process.env.MONGO_URL, options);
    console.log('✅ Database connection successful!');
    
    // Test basic operation with longer timeout
    console.log('🧪 Testing database operations...');
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log(`✅ Database operations working - ${collections.length} collections found`);
    
    await mongoose.disconnect();
    console.log('🔌 Database test completed, starting server...');
    
    return true;
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    
    if (error.message.includes('timeout')) {
      console.log('⚠️ Database is slow but may work with server startup');
      console.log('💡 Starting server anyway - it may work once fully loaded');
      return true; // Continue anyway
    }
    
    return false;
  }
}

async function startServices() {
  // Test database first
  const dbOk = await testDatabaseFirst();
  
  if (!dbOk) {
    console.log('❌ Database connection failed completely');
    console.log('💡 Please check your internet connection and MongoDB Atlas settings');
    return;
  }
  
  console.log('\n🚀 Starting server...');
  
  // Start server
  const server = spawn('node', ['server.js'], {
    cwd: path.join(__dirname, 'server'),
    stdio: 'inherit',
    shell: true
  });
  
  server.on('error', (error) => {
    console.error('❌ Server error:', error);
  });
  
  server.on('close', (code) => {
    console.log(`🔴 Server process exited with code ${code}`);
  });
  
  // Wait a moment for server to start
  setTimeout(() => {
    console.log('\n🚀 Starting client...');
    
    // Start client
    const client = spawn('npm', ['start'], {
      cwd: path.join(__dirname, 'client'),
      stdio: 'inherit',
      shell: true
    });
    
    client.on('error', (error) => {
      console.error('❌ Client error:', error);
    });
    
    client.on('close', (code) => {
      console.log(`🔴 Client process exited with code ${code}`);
    });
    
    // Show status after startup
    setTimeout(() => {
      console.log('\n🎉 BrainWave Services Started!');
      console.log('🌐 Server: http://localhost:5000');
      console.log('🌐 Client: http://localhost:3000');
      console.log('🔧 Admin: http://localhost:3000/admin/login');
      console.log('📊 Health: http://localhost:5000/api/health');
      console.log('\n💡 If login fails, the database may still be slow');
      console.log('💡 Wait a few minutes for MongoDB Atlas to fully respond');
    }, 10000);
    
  }, 5000);
  
  // Handle shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down services...');
    server.kill();
    client.kill();
    process.exit(0);
  });
}

startServices();
