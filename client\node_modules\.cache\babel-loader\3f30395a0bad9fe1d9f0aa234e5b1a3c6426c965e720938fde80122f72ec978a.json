{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Forum\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport './index.css';\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message, Button, Input, Form, Avatar, Badge, Tag } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\nimport image from '../../../assets/person.png';\nimport { PlusOutlined, MessageOutlined, UserOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Forum = () => {\n  _s();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [userData, setUserData] = useState('');\n  const [questions, setQuestions] = useState([]);\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n\n  // Pagination states\n  const [currentPage, setCurrentPage] = useState(1);\n  const [questionsPerPage] = useState(5);\n  const [totalQuestions, setTotalQuestions] = useState(0);\n  const fetchQuestions = async (page = currentPage) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllQuestions({\n        page,\n        limit: questionsPerPage\n      });\n      if (response.success) {\n        // Sort by creation date (newest first) instead of reversing\n        const sortedQuestions = response.data.sort((a, b) => new Date(b.createdAt || b._id) - new Date(a.createdAt || a._id));\n        setQuestions(sortedQuestions);\n        setTotalQuestions(response.totalQuestions || response.data.length);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const getUserData = async () => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          // fetchQuestions will be called by useEffect when userData changes\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      getUserData();\n    }\n  }, []);\n\n  // Fetch questions when component mounts or page changes\n  useEffect(() => {\n    if (!isAdmin && userData) {\n      fetchQuestions(currentPage);\n    }\n  }, [currentPage, isAdmin, userData]);\n  const handleAskQuestion = async values => {\n    try {\n      dispatch(ShowLoading());\n      const response = await addQuestion(values);\n      if (response.success) {\n        message.success(response.message);\n        setAskQuestionVisible(false);\n        form.resetFields();\n        // Reset to first page to see the new question\n        setCurrentPage(1);\n        await fetchQuestions(1);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleReply = questionId => {\n    // Toggle reply form - if already open for this question, close it\n    if (replyQuestionId === questionId) {\n      setReplyQuestionId(null);\n      form.resetFields();\n    } else {\n      setReplyQuestionId(questionId);\n      form.resetFields();\n      // Scroll to the reply form after it's rendered\n      setTimeout(() => {\n        const replyElement = document.getElementById(`reply-form-${questionId}`);\n        if (replyElement) {\n          replyElement.scrollIntoView({\n            behavior: 'smooth',\n            block: 'center'\n          });\n        }\n      }, 100);\n    }\n  };\n  const handleReplySubmit = async values => {\n    try {\n      dispatch(ShowLoading());\n      const payload = {\n        questionId: replyQuestionId,\n        text: values.text\n      };\n      const response = await addReply(payload);\n      if (response.success) {\n        message.success(response.message);\n        setReplyQuestionId(null);\n        form.resetFields();\n        // Refresh current page to show the new reply\n        await fetchQuestions(currentPage);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n\n  // Pagination calculations - use all questions since we're fetching by page\n  const totalPages = Math.ceil(totalQuestions / questionsPerPage);\n  const startItem = (currentPage - 1) * questionsPerPage + 1;\n  const endItem = Math.min(currentPage * questionsPerPage, totalQuestions);\n  const handlePageChange = page => {\n    setCurrentPage(page);\n    fetchQuestions(page);\n  };\n\n  // Format date and time\n  const formatDateTime = dateString => {\n    if (!dateString) return 'Just now';\n    const date = new Date(dateString);\n\n    // Check if date is valid\n    if (isNaN(date.getTime())) {\n      return 'Just now';\n    }\n    const now = new Date();\n    const diffInMinutes = (now - date) / (1000 * 60);\n    const diffInHours = diffInMinutes / 60;\n    const diffInDays = diffInHours / 24;\n\n    // Less than 1 minute\n    if (diffInMinutes < 1) {\n      return 'Just now';\n    }\n    // Less than 1 hour\n    else if (diffInMinutes < 60) {\n      return `${Math.floor(diffInMinutes)}m ago`;\n    }\n    // Less than 24 hours\n    else if (diffInHours < 24) {\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    }\n    // Less than 7 days\n    else if (diffInDays < 7) {\n      return `${Math.floor(diffInDays)}d ago`;\n    }\n    // More than 7 days\n    else {\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined\n      });\n    }\n  };\n\n  // Format user class based on level\n  const formatUserClass = (userClass, userLevel) => {\n    if (!userClass) return 'N/A';\n    switch (userLevel) {\n      case 'primary':\n        return `Class ${userClass}`;\n      case 'primary_kiswahili':\n        return `Darasa la ${userClass}`;\n      case 'secondary':\n        return userClass.toString().startsWith('Form') ? userClass : `Form ${userClass}`;\n      case 'advance':\n        return userClass.toString().startsWith('Form') ? userClass : `Form ${userClass}`;\n      default:\n        return userClass.toString();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modern-forum\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"forum-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"forum-header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"forum-title\",\n            children: \"Community Forum\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"forum-description\",\n            children: \"Connect with fellow learners, ask questions, and share knowledge. Join our vibrant community discussion!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-ask-btn\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 43\n              }, this),\n              onClick: () => setAskQuestionVisible(true),\n              className: \"ask-question-header\",\n              size: \"large\",\n              children: \"Ask Question\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 21\n      }, this), askQuestionVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ask-question-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ask-question-form-inline\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"form-title-inline\",\n            children: \"Ask a Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            onFinish: handleAskQuestion,\n            layout: \"vertical\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"Question Title\",\n              rules: [{\n                required: true,\n                message: 'Please enter the title'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"What's your question about?\",\n                className: \"modern-input\",\n                size: \"large\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"body\",\n              label: \"Question Details\",\n              rules: [{\n                required: true,\n                message: 'Please enter the question details'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                rows: 4,\n                placeholder: \"Provide more details about your question...\",\n                className: \"modern-textarea\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              className: \"form-actions-inline\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                className: \"submit-btn-inline\",\n                children: \"Post Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => setAskQuestionVisible(false),\n                className: \"cancel-btn-inline\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 25\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"questions-container\",\n        children: questions.map(question => {\n          var _question$user, _question$user2, _question$user3, _question$user4, _question$user5, _question$replies, _question$replies2, _question$replies3;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modern-question-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"question-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-avatar-container\",\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    src: (_question$user = question.user) !== null && _question$user !== void 0 && _question$user.profileImage ? question.user.profileImage : image,\n                    alt: \"profile\",\n                    size: 48,\n                    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 305,\n                      columnNumber: 55\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 45\n                  }, this), ((_question$user2 = question.user) === null || _question$user2 === void 0 ? void 0 : _question$user2._id) && /*#__PURE__*/_jsxDEV(OnlineStatusIndicator, {\n                    userId: question.user._id,\n                    size: \"sm\",\n                    className: \"online-status-forum\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 300,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"username\",\n                    children: ((_question$user3 = question.user) === null || _question$user3 === void 0 ? void 0 : _question$user3.name) || 'Anonymous'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"question-meta\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"question-datetime\",\n                      children: formatDateTime(question.createdAt)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 319,\n                      columnNumber: 49\n                    }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                      color: \"blue\",\n                      className: \"subject-tag\",\n                      children: formatUserClass((_question$user4 = question.user) === null || _question$user4 === void 0 ? void 0 : _question$user4.class, (_question$user5 = question.user) === null || _question$user5 === void 0 ? void 0 : _question$user5.level)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 49\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                count: ((_question$replies = question.replies) === null || _question$replies === void 0 ? void 0 : _question$replies.length) || 0,\n                className: \"reply-badge\",\n                showZero: true,\n                style: {\n                  backgroundColor: '#f3f4f6',\n                  color: '#6b7280',\n                  borderRadius: '20px',\n                  padding: '0.25rem 0.75rem',\n                  fontSize: '0.8rem',\n                  fontWeight: '600'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"question-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"question-title\",\n                children: question.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"question-body\",\n                children: question.body\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"replies-section\",\n              children: ((_question$replies2 = question.replies) === null || _question$replies2 === void 0 ? void 0 : _question$replies2.length) > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"replies-header\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"replies-count\",\n                    children: [question.replies.length, \" \", question.replies.length === 1 ? 'Reply' : 'Replies']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 354,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 45\n                }, this), question.replies.map(reply => {\n                  var _reply$user, _reply$user2, _reply$user3, _reply$user4, _reply$user5;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"modern-reply\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"reply-header\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"reply-avatar-container\",\n                        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                          src: (_reply$user = reply.user) !== null && _reply$user !== void 0 && _reply$user.profileImage ? reply.user.profileImage : image,\n                          alt: \"profile\",\n                          size: 32,\n                          icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 366,\n                            columnNumber: 71\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 362,\n                          columnNumber: 61\n                        }, this), ((_reply$user2 = reply.user) === null || _reply$user2 === void 0 ? void 0 : _reply$user2._id) && /*#__PURE__*/_jsxDEV(OnlineStatusIndicator, {\n                          userId: reply.user._id,\n                          size: \"xs\",\n                          className: \"online-status-reply\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 370,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 361,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"reply-user-info\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"reply-user-details\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"reply-username\",\n                            children: ((_reply$user3 = reply.user) === null || _reply$user3 === void 0 ? void 0 : _reply$user3.name) || 'Anonymous'\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 379,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                            color: \"green\",\n                            className: \"reply-class-tag\",\n                            size: \"small\",\n                            children: formatUserClass((_reply$user4 = reply.user) === null || _reply$user4 === void 0 ? void 0 : _reply$user4.class, (_reply$user5 = reply.user) === null || _reply$user5 === void 0 ? void 0 : _reply$user5.level)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 380,\n                            columnNumber: 65\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 378,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"reply-datetime\",\n                          children: formatDateTime(reply.createdAt || reply.timestamp || reply._id)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 384,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 377,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"reply-content\",\n                      children: reply.text\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 389,\n                      columnNumber: 53\n                    }, this)]\n                  }, reply._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 49\n                  }, this);\n                })]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"no-replies\",\n                children: \"No replies yet. Be the first to reply!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 33\n            }, this), replyQuestionId === question._id && /*#__PURE__*/_jsxDEV(\"div\", {\n              id: `reply-form-${question._id}`,\n              className: \"reply-form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"reply-form-title\",\n                children: \"Reply to this question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                form: form,\n                onFinish: handleReplySubmit,\n                layout: \"vertical\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"text\",\n                  label: \"Your Reply\",\n                  rules: [{\n                    required: true,\n                    message: 'Please enter your reply'\n                  }],\n                  children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                    rows: 3,\n                    placeholder: \"Write your reply...\",\n                    className: \"modern-textarea\",\n                    autoFocus: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 408,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  className: \"reply-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    type: \"primary\",\n                    htmlType: \"submit\",\n                    className: \"submit-reply-btn\",\n                    children: \"Submit Reply\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    onClick: () => setReplyQuestionId(null),\n                    className: \"cancel-reply-btn\",\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 419,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"question-actions\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                icon: /*#__PURE__*/_jsxDEV(MessageOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 47\n                }, this),\n                onClick: () => handleReply(question._id),\n                className: \"action-btn reply-btn\",\n                type: replyQuestionId === question._id ? \"default\" : \"primary\",\n                children: replyQuestionId === question._id ? \"Cancel Reply\" : \"Reply\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                icon: /*#__PURE__*/_jsxDEV(MessageOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 438,\n                  columnNumber: 47\n                }, this),\n                className: \"action-btn view-btn\",\n                disabled: true,\n                style: {\n                  opacity: 0.7\n                },\n                children: [((_question$replies3 = question.replies) === null || _question$replies3 === void 0 ? void 0 : _question$replies3.length) || 0, \" Replies\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 33\n            }, this)]\n          }, question._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 29\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 21\n      }, this), totalQuestions > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"forum-pagination\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"pagination-text\",\n            children: [\"Showing \", startItem, \"-\", endItem, \" of \", totalQuestions, \" questions\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 33\n          }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"pagination-pages\",\n            children: [\"Page \", currentPage, \" of \", totalPages]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 29\n        }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-controls\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handlePageChange(currentPage - 1),\n            disabled: currentPage === 1,\n            className: \"pagination-btn\",\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 37\n          }, this), Array.from({\n            length: Math.min(5, totalPages)\n          }, (_, i) => {\n            let pageNum;\n            if (totalPages <= 5) {\n              pageNum = i + 1;\n            } else if (currentPage <= 3) {\n              pageNum = i + 1;\n            } else if (currentPage >= totalPages - 2) {\n              pageNum = totalPages - 4 + i;\n            } else {\n              pageNum = currentPage - 2 + i;\n            }\n            return /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => handlePageChange(pageNum),\n              className: `pagination-btn ${currentPage === pageNum ? 'active' : ''}`,\n              children: pageNum\n            }, pageNum, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 45\n            }, this);\n          }), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handlePageChange(currentPage + 1),\n            disabled: currentPage === totalPages,\n            className: \"pagination-btn\",\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 33\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 224,\n    columnNumber: 9\n  }, this);\n};\n_s(Forum, \"rm80873S12yqUdtzCDT4+qCOOnM=\", false, function () {\n  return [Form.useForm, useDispatch];\n});\n_c = Forum;\nexport default Forum;\nvar _c;\n$RefreshReg$(_c, \"Forum\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getUserInfo", "message", "<PERSON><PERSON>", "Input", "Form", "Avatar", "Badge", "Tag", "useDispatch", "HideLoading", "ShowLoading", "addQuestion", "addReply", "getAllQuestions", "image", "PlusOutlined", "MessageOutlined", "UserOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Forum", "_s", "isAdmin", "setIsAdmin", "userData", "setUserData", "questions", "setQuestions", "askQuestionVisible", "setAskQuestionVisible", "replyQuestionId", "setReplyQuestionId", "form", "useForm", "dispatch", "currentPage", "setCurrentPage", "questionsPerPage", "totalQuestions", "setTotalQuestions", "fetchQuestions", "page", "response", "limit", "success", "sortedQuestions", "data", "sort", "a", "b", "Date", "createdAt", "_id", "length", "error", "getUserData", "localStorage", "getItem", "handleAskQuestion", "values", "resetFields", "handleReply", "questionId", "setTimeout", "replyElement", "document", "getElementById", "scrollIntoView", "behavior", "block", "handleReplySubmit", "payload", "text", "totalPages", "Math", "ceil", "startItem", "endItem", "min", "handlePageChange", "formatDateTime", "dateString", "date", "isNaN", "getTime", "now", "diffInMinutes", "diffInHours", "diffInDays", "floor", "toLocaleTimeString", "hour", "minute", "hour12", "toLocaleDateString", "month", "day", "year", "getFullYear", "undefined", "formatUserClass", "userClass", "userLevel", "toString", "startsWith", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "icon", "onClick", "size", "onFinish", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "TextArea", "rows", "htmlType", "map", "question", "_question$user", "_question$user2", "_question$user3", "_question$user4", "_question$user5", "_question$replies", "_question$replies2", "_question$replies3", "src", "user", "profileImage", "alt", "OnlineStatusIndicator", "userId", "color", "class", "level", "count", "replies", "showZero", "style", "backgroundColor", "borderRadius", "padding", "fontSize", "fontWeight", "title", "body", "reply", "_reply$user", "_reply$user2", "_reply$user3", "_reply$user4", "_reply$user5", "timestamp", "id", "autoFocus", "disabled", "opacity", "Array", "from", "_", "i", "pageNum", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport './index.css';\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Badge, Tag } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\r\nimport image from '../../../assets/person.png';\r\nimport { PlusOutlined, MessageOutlined, UserOutlined } from '@ant-design/icons';\r\n\r\nconst Forum = () => {\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [userData, setUserData] = useState('');\r\n    const [questions, setQuestions] = useState([]);\r\n    const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n    const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n    const [form] = Form.useForm();\r\n    const dispatch = useDispatch();\r\n\r\n\r\n    // Pagination states\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [questionsPerPage] = useState(5);\r\n    const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n    const fetchQuestions = async (page = currentPage) => {\r\n        try {\r\n            dispatch(ShowLoading());\r\n            const response = await getAllQuestions({ page, limit: questionsPerPage });\r\n            if (response.success) {\r\n                // Sort by creation date (newest first) instead of reversing\r\n                const sortedQuestions = response.data.sort((a, b) =>\r\n                    new Date(b.createdAt || b._id) - new Date(a.createdAt || a._id)\r\n                );\r\n                setQuestions(sortedQuestions);\r\n                setTotalQuestions(response.totalQuestions || response.data.length);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        } finally {\r\n            dispatch(HideLoading());\r\n        }\r\n    };\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            dispatch(ShowLoading());\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    // fetchQuestions will be called by useEffect when userData changes\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        } finally {\r\n            dispatch(HideLoading());\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    // Fetch questions when component mounts or page changes\r\n    useEffect(() => {\r\n        if (!isAdmin && userData) {\r\n            fetchQuestions(currentPage);\r\n        }\r\n    }, [currentPage, isAdmin, userData]);\r\n\r\n\r\n\r\n    const handleAskQuestion = async (values) => {\r\n        try {\r\n            dispatch(ShowLoading());\r\n            const response = await addQuestion(values);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setAskQuestionVisible(false);\r\n                form.resetFields();\r\n                // Reset to first page to see the new question\r\n                setCurrentPage(1);\r\n                await fetchQuestions(1);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        } finally {\r\n            dispatch(HideLoading());\r\n        }\r\n    };\r\n\r\n    const handleReply = (questionId) => {\r\n        // Toggle reply form - if already open for this question, close it\r\n        if (replyQuestionId === questionId) {\r\n            setReplyQuestionId(null);\r\n            form.resetFields();\r\n        } else {\r\n            setReplyQuestionId(questionId);\r\n            form.resetFields();\r\n            // Scroll to the reply form after it's rendered\r\n            setTimeout(() => {\r\n                const replyElement = document.getElementById(`reply-form-${questionId}`);\r\n                if (replyElement) {\r\n                    replyElement.scrollIntoView({ behavior: 'smooth', block: 'center' });\r\n                }\r\n            }, 100);\r\n        }\r\n    };\r\n\r\n    const handleReplySubmit = async (values) => {\r\n        try {\r\n            dispatch(ShowLoading());\r\n            const payload = {\r\n                questionId: replyQuestionId,\r\n                text: values.text\r\n            };\r\n            const response = await addReply(payload);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setReplyQuestionId(null);\r\n                form.resetFields();\r\n                // Refresh current page to show the new reply\r\n                await fetchQuestions(currentPage);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        } finally {\r\n            dispatch(HideLoading());\r\n        }\r\n    };\r\n\r\n\r\n\r\n    // Pagination calculations - use all questions since we're fetching by page\r\n    const totalPages = Math.ceil(totalQuestions / questionsPerPage);\r\n    const startItem = (currentPage - 1) * questionsPerPage + 1;\r\n    const endItem = Math.min(currentPage * questionsPerPage, totalQuestions);\r\n\r\n    const handlePageChange = (page) => {\r\n        setCurrentPage(page);\r\n        fetchQuestions(page);\r\n    };\r\n\r\n    // Format date and time\r\n    const formatDateTime = (dateString) => {\r\n        if (!dateString) return 'Just now';\r\n\r\n        const date = new Date(dateString);\r\n\r\n        // Check if date is valid\r\n        if (isNaN(date.getTime())) {\r\n            return 'Just now';\r\n        }\r\n\r\n        const now = new Date();\r\n        const diffInMinutes = (now - date) / (1000 * 60);\r\n        const diffInHours = diffInMinutes / 60;\r\n        const diffInDays = diffInHours / 24;\r\n\r\n        // Less than 1 minute\r\n        if (diffInMinutes < 1) {\r\n            return 'Just now';\r\n        }\r\n        // Less than 1 hour\r\n        else if (diffInMinutes < 60) {\r\n            return `${Math.floor(diffInMinutes)}m ago`;\r\n        }\r\n        // Less than 24 hours\r\n        else if (diffInHours < 24) {\r\n            return date.toLocaleTimeString('en-US', {\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n                hour12: true\r\n            });\r\n        }\r\n        // Less than 7 days\r\n        else if (diffInDays < 7) {\r\n            return `${Math.floor(diffInDays)}d ago`;\r\n        }\r\n        // More than 7 days\r\n        else {\r\n            return date.toLocaleDateString('en-US', {\r\n                month: 'short',\r\n                day: 'numeric',\r\n                year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined\r\n            });\r\n        }\r\n    };\r\n\r\n    // Format user class based on level\r\n    const formatUserClass = (userClass, userLevel) => {\r\n        if (!userClass) return 'N/A';\r\n\r\n        switch (userLevel) {\r\n            case 'primary':\r\n                return `Class ${userClass}`;\r\n            case 'primary_kiswahili':\r\n                return `Darasa la ${userClass}`;\r\n            case 'secondary':\r\n                return userClass.toString().startsWith('Form') ? userClass : `Form ${userClass}`;\r\n            case 'advance':\r\n                return userClass.toString().startsWith('Form') ? userClass : `Form ${userClass}`;\r\n            default:\r\n                return userClass.toString();\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            {!isAdmin && (\r\n                <div className=\"modern-forum\">\r\n                    {/* Header Section */}\r\n                    <div className=\"forum-header\">\r\n                        <div className=\"forum-header-content\">\r\n                            <h1 className=\"forum-title\">Community Forum</h1>\r\n                            <p className=\"forum-description\">\r\n                                Connect with fellow learners, ask questions, and share knowledge.\r\n                                Join our vibrant community discussion!\r\n                            </p>\r\n                            {/* Ask Question Button in Header */}\r\n                            <div className=\"header-ask-btn\">\r\n                                <Button\r\n                                    type=\"primary\"\r\n                                    icon={<PlusOutlined />}\r\n                                    onClick={() => setAskQuestionVisible(true)}\r\n                                    className=\"ask-question-header\"\r\n                                    size=\"large\"\r\n                                >\r\n                                    Ask Question\r\n                                </Button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n\r\n\r\n                    {/* Ask Question Form - Below Header */}\r\n                    {askQuestionVisible && (\r\n                        <div className=\"ask-question-section\">\r\n                            <div className=\"ask-question-form-inline\">\r\n                                <h3 className=\"form-title-inline\">Ask a Question</h3>\r\n                                <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\">\r\n                                    <Form.Item\r\n                                        name=\"title\"\r\n                                        label=\"Question Title\"\r\n                                        rules={[{ required: true, message: 'Please enter the title' }]}\r\n                                    >\r\n                                        <Input\r\n                                            placeholder=\"What's your question about?\"\r\n                                            className=\"modern-input\"\r\n                                            size=\"large\"\r\n                                        />\r\n                                    </Form.Item>\r\n                                    <Form.Item\r\n                                        name=\"body\"\r\n                                        label=\"Question Details\"\r\n                                        rules={[{ required: true, message: 'Please enter the question details' }]}\r\n                                    >\r\n                                        <Input.TextArea\r\n                                            rows={4}\r\n                                            placeholder=\"Provide more details about your question...\"\r\n                                            className=\"modern-textarea\"\r\n                                        />\r\n                                    </Form.Item>\r\n                                    <Form.Item className=\"form-actions-inline\">\r\n                                        <Button type=\"primary\" htmlType=\"submit\" className=\"submit-btn-inline\">\r\n                                            Post Question\r\n                                        </Button>\r\n                                        <Button onClick={() => setAskQuestionVisible(false)} className=\"cancel-btn-inline\">\r\n                                            Cancel\r\n                                        </Button>\r\n                                    </Form.Item>\r\n                                </Form>\r\n                            </div>\r\n                        </div>\r\n                    )}\r\n\r\n                    {/* Questions List */}\r\n                    <div className=\"questions-container\">\r\n                        {questions.map((question) => (\r\n                            <div key={question._id} className=\"modern-question-card\">\r\n                                {/* Question Header */}\r\n                                <div className=\"question-header\">\r\n                                    <div className=\"user-info\">\r\n                                        <div className=\"user-avatar-container\">\r\n                                            <Avatar\r\n                                                src={question.user?.profileImage ? question.user.profileImage : image}\r\n                                                alt=\"profile\"\r\n                                                size={48}\r\n                                                icon={<UserOutlined />}\r\n                                            />\r\n                                            {/* Online Status Indicator */}\r\n                                            {question.user?._id && (\r\n                                                <OnlineStatusIndicator\r\n                                                    userId={question.user._id}\r\n                                                    size=\"sm\"\r\n                                                    className=\"online-status-forum\"\r\n                                                />\r\n                                            )}\r\n                                        </div>\r\n                                        <div className=\"user-details\">\r\n                                            <span className=\"username\">{question.user?.name || 'Anonymous'}</span>\r\n                                            <div className=\"question-meta\">\r\n                                                <span className=\"question-datetime\">\r\n                                                    {formatDateTime(question.createdAt)}\r\n                                                </span>\r\n                                                <Tag color=\"blue\" className=\"subject-tag\">\r\n                                                    {formatUserClass(question.user?.class, question.user?.level)}\r\n                                                </Tag>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                    <Badge\r\n                                        count={question.replies?.length || 0}\r\n                                        className=\"reply-badge\"\r\n                                        showZero\r\n                                        style={{\r\n                                            backgroundColor: '#f3f4f6',\r\n                                            color: '#6b7280',\r\n                                            borderRadius: '20px',\r\n                                            padding: '0.25rem 0.75rem',\r\n                                            fontSize: '0.8rem',\r\n                                            fontWeight: '600'\r\n                                        }}\r\n                                    />\r\n                                </div>\r\n\r\n                                {/* Question Content */}\r\n                                <div className=\"question-content\">\r\n                                    <h3 className=\"question-title\">{question.title}</h3>\r\n                                    <p className=\"question-body\">{question.body}</p>\r\n                                </div>\r\n\r\n                                {/* Replies Section - Always Visible */}\r\n                                <div className=\"replies-section\">\r\n                                    {question.replies?.length > 0 ? (\r\n                                        <>\r\n                                            <div className=\"replies-header\">\r\n                                                <span className=\"replies-count\">\r\n                                                    {question.replies.length} {question.replies.length === 1 ? 'Reply' : 'Replies'}\r\n                                                </span>\r\n                                            </div>\r\n                                            {question.replies.map((reply) => (\r\n                                                <div key={reply._id} className=\"modern-reply\">\r\n                                                    <div className=\"reply-header\">\r\n                                                        <div className=\"reply-avatar-container\">\r\n                                                            <Avatar\r\n                                                                src={reply.user?.profileImage ? reply.user.profileImage : image}\r\n                                                                alt=\"profile\"\r\n                                                                size={32}\r\n                                                                icon={<UserOutlined />}\r\n                                                            />\r\n                                                            {/* Online Status Indicator for Reply */}\r\n                                                            {reply.user?._id && (\r\n                                                                <OnlineStatusIndicator\r\n                                                                    userId={reply.user._id}\r\n                                                                    size=\"xs\"\r\n                                                                    className=\"online-status-reply\"\r\n                                                                />\r\n                                                            )}\r\n                                                        </div>\r\n                                                        <div className=\"reply-user-info\">\r\n                                                            <div className=\"reply-user-details\">\r\n                                                                <span className=\"reply-username\">{reply.user?.name || 'Anonymous'}</span>\r\n                                                                <Tag color=\"green\" className=\"reply-class-tag\" size=\"small\">\r\n                                                                    {formatUserClass(reply.user?.class, reply.user?.level)}\r\n                                                                </Tag>\r\n                                                            </div>\r\n                                                            <span className=\"reply-datetime\">\r\n                                                                {formatDateTime(reply.createdAt || reply.timestamp || reply._id)}\r\n                                                            </span>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div className=\"reply-content\">{reply.text}</div>\r\n                                                </div>\r\n                                            ))}\r\n                                        </>\r\n                                    ) : (\r\n                                        <p className=\"no-replies\">No replies yet. Be the first to reply!</p>\r\n                                    )}\r\n                                </div>\r\n\r\n                                {/* Reply Form - Appears when Reply button is clicked */}\r\n                                {replyQuestionId === question._id && (\r\n                                    <div id={`reply-form-${question._id}`} className=\"reply-form-section\">\r\n                                        <h4 className=\"reply-form-title\">Reply to this question</h4>\r\n                                        <Form form={form} onFinish={handleReplySubmit} layout=\"vertical\">\r\n                                            <Form.Item\r\n                                                name=\"text\"\r\n                                                label=\"Your Reply\"\r\n                                                rules={[{ required: true, message: 'Please enter your reply' }]}\r\n                                            >\r\n                                                <Input.TextArea\r\n                                                    rows={3}\r\n                                                    placeholder=\"Write your reply...\"\r\n                                                    className=\"modern-textarea\"\r\n                                                    autoFocus\r\n                                                />\r\n                                            </Form.Item>\r\n                                            <Form.Item className=\"reply-actions\">\r\n                                                <Button type=\"primary\" htmlType=\"submit\" className=\"submit-reply-btn\">\r\n                                                    Submit Reply\r\n                                                </Button>\r\n                                                <Button onClick={() => setReplyQuestionId(null)} className=\"cancel-reply-btn\">\r\n                                                    Cancel\r\n                                                </Button>\r\n                                            </Form.Item>\r\n                                        </Form>\r\n                                    </div>\r\n                                )}\r\n\r\n                                {/* Action Buttons */}\r\n                                <div className=\"question-actions\">\r\n                                    <Button\r\n                                        icon={<MessageOutlined />}\r\n                                        onClick={() => handleReply(question._id)}\r\n                                        className=\"action-btn reply-btn\"\r\n                                        type={replyQuestionId === question._id ? \"default\" : \"primary\"}\r\n                                    >\r\n                                        {replyQuestionId === question._id ? \"Cancel Reply\" : \"Reply\"}\r\n                                    </Button>\r\n                                    <Button\r\n                                        icon={<MessageOutlined />}\r\n                                        className=\"action-btn view-btn\"\r\n                                        disabled\r\n                                        style={{ opacity: 0.7 }}\r\n                                    >\r\n                                        {question.replies?.length || 0} Replies\r\n                                    </Button>\r\n                                </div>\r\n\r\n\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n\r\n                    {/* Pagination */}\r\n                    {totalQuestions > 0 && (\r\n                        <div className=\"forum-pagination\">\r\n                            <div className=\"pagination-info\">\r\n                                <span className=\"pagination-text\">\r\n                                    Showing {startItem}-{endItem} of {totalQuestions} questions\r\n                                </span>\r\n                                {totalPages > 1 && (\r\n                                    <span className=\"pagination-pages\">\r\n                                        Page {currentPage} of {totalPages}\r\n                                    </span>\r\n                                )}\r\n                            </div>\r\n                            {totalPages > 1 && (\r\n                                <div className=\"pagination-controls\">\r\n                                    <Button\r\n                                        onClick={() => handlePageChange(currentPage - 1)}\r\n                                        disabled={currentPage === 1}\r\n                                        className=\"pagination-btn\"\r\n                                    >\r\n                                        Previous\r\n                                    </Button>\r\n\r\n                                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {\r\n                                        let pageNum;\r\n                                        if (totalPages <= 5) {\r\n                                            pageNum = i + 1;\r\n                                        } else if (currentPage <= 3) {\r\n                                            pageNum = i + 1;\r\n                                        } else if (currentPage >= totalPages - 2) {\r\n                                            pageNum = totalPages - 4 + i;\r\n                                        } else {\r\n                                            pageNum = currentPage - 2 + i;\r\n                                        }\r\n\r\n                                        return (\r\n                                            <Button\r\n                                                key={pageNum}\r\n                                                onClick={() => handlePageChange(pageNum)}\r\n                                                className={`pagination-btn ${currentPage === pageNum ? 'active' : ''}`}\r\n                                            >\r\n                                                {pageNum}\r\n                                            </Button>\r\n                                        );\r\n                                    })}\r\n\r\n                                    <Button\r\n                                        onClick={() => handlePageChange(currentPage + 1)}\r\n                                        disabled={currentPage === totalPages}\r\n                                        className=\"pagination-btn\"\r\n                                    >\r\n                                        Next\r\n                                    </Button>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n                    )}\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Forum;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,GAAG,QAAQ,MAAM;AACvE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,WAAW,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,yBAAyB;AAChF,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,SAASC,YAAY,EAAEC,eAAe,EAAEC,YAAY,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEhF,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+B,SAAS,EAAEC,YAAY,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACiC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACmC,eAAe,EAAEC,kBAAkB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACqC,IAAI,CAAC,GAAG9B,IAAI,CAAC+B,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;;EAG9B;EACA,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC0C,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EACtC,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAM6C,cAAc,GAAG,MAAAA,CAAOC,IAAI,GAAGN,WAAW,KAAK;IACjD,IAAI;MACAD,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMkC,QAAQ,GAAG,MAAM/B,eAAe,CAAC;QAAE8B,IAAI;QAAEE,KAAK,EAAEN;MAAiB,CAAC,CAAC;MACzE,IAAIK,QAAQ,CAACE,OAAO,EAAE;QAClB;QACA,MAAMC,eAAe,GAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAC5C,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,IAAIF,CAAC,CAACG,GAAG,CAAC,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,SAAS,IAAIH,CAAC,CAACI,GAAG,CAClE,CAAC;QACDzB,YAAY,CAACkB,eAAe,CAAC;QAC7BN,iBAAiB,CAACG,QAAQ,CAACJ,cAAc,IAAII,QAAQ,CAACI,IAAI,CAACO,MAAM,CAAC;MACtE,CAAC,MAAM;QACHtD,OAAO,CAACuD,KAAK,CAACZ,QAAQ,CAAC3C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOuD,KAAK,EAAE;MACZvD,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,CAAC;IAChC,CAAC,SAAS;MACNmC,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMgD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACArB,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMkC,QAAQ,GAAG,MAAM5C,WAAW,CAAC,CAAC;MACpC,IAAI4C,QAAQ,CAACE,OAAO,EAAE;QAClB,IAAIF,QAAQ,CAACI,IAAI,CAACxB,OAAO,EAAE;UACvBC,UAAU,CAAC,IAAI,CAAC;QACpB,CAAC,MAAM;UACHA,UAAU,CAAC,KAAK,CAAC;UACjBE,WAAW,CAACiB,QAAQ,CAACI,IAAI,CAAC;UAC1B;QACJ;MACJ,CAAC,MAAM;QACH/C,OAAO,CAACuD,KAAK,CAACZ,QAAQ,CAAC3C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOuD,KAAK,EAAE;MACZvD,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,CAAC;IAChC,CAAC,SAAS;MACNmC,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;IAC3B;EACJ,CAAC;EAEDX,SAAS,CAAC,MAAM;IACZ,IAAI4D,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MAC/BF,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA3D,SAAS,CAAC,MAAM;IACZ,IAAI,CAAC0B,OAAO,IAAIE,QAAQ,EAAE;MACtBgB,cAAc,CAACL,WAAW,CAAC;IAC/B;EACJ,CAAC,EAAE,CAACA,WAAW,EAAEb,OAAO,EAAEE,QAAQ,CAAC,CAAC;EAIpC,MAAMkC,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IACxC,IAAI;MACAzB,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMkC,QAAQ,GAAG,MAAMjC,WAAW,CAACkD,MAAM,CAAC;MAC1C,IAAIjB,QAAQ,CAACE,OAAO,EAAE;QAClB7C,OAAO,CAAC6C,OAAO,CAACF,QAAQ,CAAC3C,OAAO,CAAC;QACjC8B,qBAAqB,CAAC,KAAK,CAAC;QAC5BG,IAAI,CAAC4B,WAAW,CAAC,CAAC;QAClB;QACAxB,cAAc,CAAC,CAAC,CAAC;QACjB,MAAMI,cAAc,CAAC,CAAC,CAAC;MAC3B,CAAC,MAAM;QACHzC,OAAO,CAACuD,KAAK,CAACZ,QAAQ,CAAC3C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOuD,KAAK,EAAE;MACZvD,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,CAAC;IAChC,CAAC,SAAS;MACNmC,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMsD,WAAW,GAAIC,UAAU,IAAK;IAChC;IACA,IAAIhC,eAAe,KAAKgC,UAAU,EAAE;MAChC/B,kBAAkB,CAAC,IAAI,CAAC;MACxBC,IAAI,CAAC4B,WAAW,CAAC,CAAC;IACtB,CAAC,MAAM;MACH7B,kBAAkB,CAAC+B,UAAU,CAAC;MAC9B9B,IAAI,CAAC4B,WAAW,CAAC,CAAC;MAClB;MACAG,UAAU,CAAC,MAAM;QACb,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAE,cAAaJ,UAAW,EAAC,CAAC;QACxE,IAAIE,YAAY,EAAE;UACdA,YAAY,CAACG,cAAc,CAAC;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,KAAK,EAAE;UAAS,CAAC,CAAC;QACxE;MACJ,CAAC,EAAE,GAAG,CAAC;IACX;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAOX,MAAM,IAAK;IACxC,IAAI;MACAzB,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAM+D,OAAO,GAAG;QACZT,UAAU,EAAEhC,eAAe;QAC3B0C,IAAI,EAAEb,MAAM,CAACa;MACjB,CAAC;MACD,MAAM9B,QAAQ,GAAG,MAAMhC,QAAQ,CAAC6D,OAAO,CAAC;MACxC,IAAI7B,QAAQ,CAACE,OAAO,EAAE;QAClB7C,OAAO,CAAC6C,OAAO,CAACF,QAAQ,CAAC3C,OAAO,CAAC;QACjCgC,kBAAkB,CAAC,IAAI,CAAC;QACxBC,IAAI,CAAC4B,WAAW,CAAC,CAAC;QAClB;QACA,MAAMpB,cAAc,CAACL,WAAW,CAAC;MACrC,CAAC,MAAM;QACHpC,OAAO,CAACuD,KAAK,CAACZ,QAAQ,CAAC3C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOuD,KAAK,EAAE;MACZvD,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,CAAC;IAChC,CAAC,SAAS;MACNmC,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;IAC3B;EACJ,CAAC;;EAID;EACA,MAAMkE,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACrC,cAAc,GAAGD,gBAAgB,CAAC;EAC/D,MAAMuC,SAAS,GAAG,CAACzC,WAAW,GAAG,CAAC,IAAIE,gBAAgB,GAAG,CAAC;EAC1D,MAAMwC,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC3C,WAAW,GAAGE,gBAAgB,EAAEC,cAAc,CAAC;EAExE,MAAMyC,gBAAgB,GAAItC,IAAI,IAAK;IAC/BL,cAAc,CAACK,IAAI,CAAC;IACpBD,cAAc,CAACC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMuC,cAAc,GAAIC,UAAU,IAAK;IACnC,IAAI,CAACA,UAAU,EAAE,OAAO,UAAU;IAElC,MAAMC,IAAI,GAAG,IAAIhC,IAAI,CAAC+B,UAAU,CAAC;;IAEjC;IACA,IAAIE,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;MACvB,OAAO,UAAU;IACrB;IAEA,MAAMC,GAAG,GAAG,IAAInC,IAAI,CAAC,CAAC;IACtB,MAAMoC,aAAa,GAAG,CAACD,GAAG,GAAGH,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC;IAChD,MAAMK,WAAW,GAAGD,aAAa,GAAG,EAAE;IACtC,MAAME,UAAU,GAAGD,WAAW,GAAG,EAAE;;IAEnC;IACA,IAAID,aAAa,GAAG,CAAC,EAAE;MACnB,OAAO,UAAU;IACrB;IACA;IAAA,KACK,IAAIA,aAAa,GAAG,EAAE,EAAE;MACzB,OAAQ,GAAEZ,IAAI,CAACe,KAAK,CAACH,aAAa,CAAE,OAAM;IAC9C;IACA;IAAA,KACK,IAAIC,WAAW,GAAG,EAAE,EAAE;MACvB,OAAOL,IAAI,CAACQ,kBAAkB,CAAC,OAAO,EAAE;QACpCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACZ,CAAC,CAAC;IACN;IACA;IAAA,KACK,IAAIL,UAAU,GAAG,CAAC,EAAE;MACrB,OAAQ,GAAEd,IAAI,CAACe,KAAK,CAACD,UAAU,CAAE,OAAM;IAC3C;IACA;IAAA,KACK;MACD,OAAON,IAAI,CAACY,kBAAkB,CAAC,OAAO,EAAE;QACpCC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAEf,IAAI,CAACgB,WAAW,CAAC,CAAC,KAAKb,GAAG,CAACa,WAAW,CAAC,CAAC,GAAG,SAAS,GAAGC;MACjE,CAAC,CAAC;IACN;EACJ,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAACC,SAAS,EAAEC,SAAS,KAAK;IAC9C,IAAI,CAACD,SAAS,EAAE,OAAO,KAAK;IAE5B,QAAQC,SAAS;MACb,KAAK,SAAS;QACV,OAAQ,SAAQD,SAAU,EAAC;MAC/B,KAAK,mBAAmB;QACpB,OAAQ,aAAYA,SAAU,EAAC;MACnC,KAAK,WAAW;QACZ,OAAOA,SAAS,CAACE,QAAQ,CAAC,CAAC,CAACC,UAAU,CAAC,MAAM,CAAC,GAAGH,SAAS,GAAI,QAAOA,SAAU,EAAC;MACpF,KAAK,SAAS;QACV,OAAOA,SAAS,CAACE,QAAQ,CAAC,CAAC,CAACC,UAAU,CAAC,MAAM,CAAC,GAAGH,SAAS,GAAI,QAAOA,SAAU,EAAC;MACpF;QACI,OAAOA,SAAS,CAACE,QAAQ,CAAC,CAAC;IACnC;EACJ,CAAC;EAED,oBACItF,OAAA;IAAAwF,QAAA,EACK,CAACnF,OAAO,iBACLL,OAAA;MAAKyF,SAAS,EAAC,cAAc;MAAAD,QAAA,gBAEzBxF,OAAA;QAAKyF,SAAS,EAAC,cAAc;QAAAD,QAAA,eACzBxF,OAAA;UAAKyF,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjCxF,OAAA;YAAIyF,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChD7F,OAAA;YAAGyF,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAGjC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJ7F,OAAA;YAAKyF,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC3BxF,OAAA,CAACjB,MAAM;cACH+G,IAAI,EAAC,SAAS;cACdC,IAAI,eAAE/F,OAAA,CAACJ,YAAY;gBAAA8F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBG,OAAO,EAAEA,CAAA,KAAMpF,qBAAqB,CAAC,IAAI,CAAE;cAC3C6E,SAAS,EAAC,qBAAqB;cAC/BQ,IAAI,EAAC,OAAO;cAAAT,QAAA,EACf;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAKLlF,kBAAkB,iBACfX,OAAA;QAAKyF,SAAS,EAAC,sBAAsB;QAAAD,QAAA,eACjCxF,OAAA;UAAKyF,SAAS,EAAC,0BAA0B;UAAAD,QAAA,gBACrCxF,OAAA;YAAIyF,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrD7F,OAAA,CAACf,IAAI;YAAC8B,IAAI,EAAEA,IAAK;YAACmF,QAAQ,EAAEzD,iBAAkB;YAAC0D,MAAM,EAAC,UAAU;YAAAX,QAAA,gBAC5DxF,OAAA,CAACf,IAAI,CAACmH,IAAI;cACNC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,gBAAgB;cACtBC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1H,OAAO,EAAE;cAAyB,CAAC,CAAE;cAAA0G,QAAA,eAE/DxF,OAAA,CAAChB,KAAK;gBACFyH,WAAW,EAAC,6BAA6B;gBACzChB,SAAS,EAAC,cAAc;gBACxBQ,IAAI,EAAC;cAAO;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACZ7F,OAAA,CAACf,IAAI,CAACmH,IAAI;cACNC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,kBAAkB;cACxBC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE1H,OAAO,EAAE;cAAoC,CAAC,CAAE;cAAA0G,QAAA,eAE1ExF,OAAA,CAAChB,KAAK,CAAC0H,QAAQ;gBACXC,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC,6CAA6C;gBACzDhB,SAAS,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACZ7F,OAAA,CAACf,IAAI,CAACmH,IAAI;cAACX,SAAS,EAAC,qBAAqB;cAAAD,QAAA,gBACtCxF,OAAA,CAACjB,MAAM;gBAAC+G,IAAI,EAAC,SAAS;gBAACc,QAAQ,EAAC,QAAQ;gBAACnB,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAAC;cAEvE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT7F,OAAA,CAACjB,MAAM;gBAACiH,OAAO,EAAEA,CAAA,KAAMpF,qBAAqB,CAAC,KAAK,CAAE;gBAAC6E,SAAS,EAAC,mBAAmB;gBAAAD,QAAA,EAAC;cAEnF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,eAGD7F,OAAA;QAAKyF,SAAS,EAAC,qBAAqB;QAAAD,QAAA,EAC/B/E,SAAS,CAACoG,GAAG,CAAEC,QAAQ;UAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,iBAAA,EAAAC,kBAAA,EAAAC,kBAAA;UAAA,oBACpBtH,OAAA;YAAwByF,SAAS,EAAC,sBAAsB;YAAAD,QAAA,gBAEpDxF,OAAA;cAAKyF,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC5BxF,OAAA;gBAAKyF,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACtBxF,OAAA;kBAAKyF,SAAS,EAAC,uBAAuB;kBAAAD,QAAA,gBAClCxF,OAAA,CAACd,MAAM;oBACHqI,GAAG,EAAE,CAAAR,cAAA,GAAAD,QAAQ,CAACU,IAAI,cAAAT,cAAA,eAAbA,cAAA,CAAeU,YAAY,GAAGX,QAAQ,CAACU,IAAI,CAACC,YAAY,GAAG9H,KAAM;oBACtE+H,GAAG,EAAC,SAAS;oBACbzB,IAAI,EAAE,EAAG;oBACTF,IAAI,eAAE/F,OAAA,CAACF,YAAY;sBAAA4F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1B,CAAC,EAED,EAAAmB,eAAA,GAAAF,QAAQ,CAACU,IAAI,cAAAR,eAAA,uBAAbA,eAAA,CAAe7E,GAAG,kBACfnC,OAAA,CAAC2H,qBAAqB;oBAClBC,MAAM,EAAEd,QAAQ,CAACU,IAAI,CAACrF,GAAI;oBAC1B8D,IAAI,EAAC,IAAI;oBACTR,SAAS,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CACJ;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACN7F,OAAA;kBAAKyF,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBACzBxF,OAAA;oBAAMyF,SAAS,EAAC,UAAU;oBAAAD,QAAA,EAAE,EAAAyB,eAAA,GAAAH,QAAQ,CAACU,IAAI,cAAAP,eAAA,uBAAbA,eAAA,CAAeZ,IAAI,KAAI;kBAAW;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtE7F,OAAA;oBAAKyF,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAC1BxF,OAAA;sBAAMyF,SAAS,EAAC,mBAAmB;sBAAAD,QAAA,EAC9BzB,cAAc,CAAC+C,QAAQ,CAAC5E,SAAS;oBAAC;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC,eACP7F,OAAA,CAACZ,GAAG;sBAACyI,KAAK,EAAC,MAAM;sBAACpC,SAAS,EAAC,aAAa;sBAAAD,QAAA,EACpCL,eAAe,EAAA+B,eAAA,GAACJ,QAAQ,CAACU,IAAI,cAAAN,eAAA,uBAAbA,eAAA,CAAeY,KAAK,GAAAX,eAAA,GAAEL,QAAQ,CAACU,IAAI,cAAAL,eAAA,uBAAbA,eAAA,CAAeY,KAAK;oBAAC;sBAAArC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN7F,OAAA,CAACb,KAAK;gBACF6I,KAAK,EAAE,EAAAZ,iBAAA,GAAAN,QAAQ,CAACmB,OAAO,cAAAb,iBAAA,uBAAhBA,iBAAA,CAAkBhF,MAAM,KAAI,CAAE;gBACrCqD,SAAS,EAAC,aAAa;gBACvByC,QAAQ;gBACRC,KAAK,EAAE;kBACHC,eAAe,EAAE,SAAS;kBAC1BP,KAAK,EAAE,SAAS;kBAChBQ,YAAY,EAAE,MAAM;kBACpBC,OAAO,EAAE,iBAAiB;kBAC1BC,QAAQ,EAAE,QAAQ;kBAClBC,UAAU,EAAE;gBAChB;cAAE;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGN7F,OAAA;cAAKyF,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC7BxF,OAAA;gBAAIyF,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,EAAEsB,QAAQ,CAAC2B;cAAK;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpD7F,OAAA;gBAAGyF,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAEsB,QAAQ,CAAC4B;cAAI;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eAGN7F,OAAA;cAAKyF,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAC3B,EAAA6B,kBAAA,GAAAP,QAAQ,CAACmB,OAAO,cAAAZ,kBAAA,uBAAhBA,kBAAA,CAAkBjF,MAAM,IAAG,CAAC,gBACzBpC,OAAA,CAAAE,SAAA;gBAAAsF,QAAA,gBACIxF,OAAA;kBAAKyF,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,eAC3BxF,OAAA;oBAAMyF,SAAS,EAAC,eAAe;oBAAAD,QAAA,GAC1BsB,QAAQ,CAACmB,OAAO,CAAC7F,MAAM,EAAC,GAAC,EAAC0E,QAAQ,CAACmB,OAAO,CAAC7F,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS;kBAAA;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACLiB,QAAQ,CAACmB,OAAO,CAACpB,GAAG,CAAE8B,KAAK;kBAAA,IAAAC,WAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA,EAAAC,YAAA;kBAAA,oBACxBhJ,OAAA;oBAAqByF,SAAS,EAAC,cAAc;oBAAAD,QAAA,gBACzCxF,OAAA;sBAAKyF,SAAS,EAAC,cAAc;sBAAAD,QAAA,gBACzBxF,OAAA;wBAAKyF,SAAS,EAAC,wBAAwB;wBAAAD,QAAA,gBACnCxF,OAAA,CAACd,MAAM;0BACHqI,GAAG,EAAE,CAAAqB,WAAA,GAAAD,KAAK,CAACnB,IAAI,cAAAoB,WAAA,eAAVA,WAAA,CAAYnB,YAAY,GAAGkB,KAAK,CAACnB,IAAI,CAACC,YAAY,GAAG9H,KAAM;0BAChE+H,GAAG,EAAC,SAAS;0BACbzB,IAAI,EAAE,EAAG;0BACTF,IAAI,eAAE/F,OAAA,CAACF,YAAY;4BAAA4F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAE;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1B,CAAC,EAED,EAAAgD,YAAA,GAAAF,KAAK,CAACnB,IAAI,cAAAqB,YAAA,uBAAVA,YAAA,CAAY1G,GAAG,kBACZnC,OAAA,CAAC2H,qBAAqB;0BAClBC,MAAM,EAAEe,KAAK,CAACnB,IAAI,CAACrF,GAAI;0BACvB8D,IAAI,EAAC,IAAI;0BACTR,SAAS,EAAC;wBAAqB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClC,CACJ;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACN7F,OAAA;wBAAKyF,SAAS,EAAC,iBAAiB;wBAAAD,QAAA,gBAC5BxF,OAAA;0BAAKyF,SAAS,EAAC,oBAAoB;0BAAAD,QAAA,gBAC/BxF,OAAA;4BAAMyF,SAAS,EAAC,gBAAgB;4BAAAD,QAAA,EAAE,EAAAsD,YAAA,GAAAH,KAAK,CAACnB,IAAI,cAAAsB,YAAA,uBAAVA,YAAA,CAAYzC,IAAI,KAAI;0BAAW;4BAAAX,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC,eACzE7F,OAAA,CAACZ,GAAG;4BAACyI,KAAK,EAAC,OAAO;4BAACpC,SAAS,EAAC,iBAAiB;4BAACQ,IAAI,EAAC,OAAO;4BAAAT,QAAA,EACtDL,eAAe,EAAA4D,YAAA,GAACJ,KAAK,CAACnB,IAAI,cAAAuB,YAAA,uBAAVA,YAAA,CAAYjB,KAAK,GAAAkB,YAAA,GAAEL,KAAK,CAACnB,IAAI,cAAAwB,YAAA,uBAAVA,YAAA,CAAYjB,KAAK;0BAAC;4BAAArC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACrD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACL,CAAC,eACN7F,OAAA;0BAAMyF,SAAS,EAAC,gBAAgB;0BAAAD,QAAA,EAC3BzB,cAAc,CAAC4E,KAAK,CAACzG,SAAS,IAAIyG,KAAK,CAACM,SAAS,IAAIN,KAAK,CAACxG,GAAG;wBAAC;0BAAAuD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9D,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACN7F,OAAA;sBAAKyF,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAEmD,KAAK,CAACpF;oBAAI;sBAAAmC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,GA9B3C8C,KAAK,CAACxG,GAAG;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OA+Bd,CAAC;gBAAA,CACT,CAAC;cAAA,eACJ,CAAC,gBAEH7F,OAAA;gBAAGyF,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YACtE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,EAGLhF,eAAe,KAAKiG,QAAQ,CAAC3E,GAAG,iBAC7BnC,OAAA;cAAKkJ,EAAE,EAAG,cAAapC,QAAQ,CAAC3E,GAAI,EAAE;cAACsD,SAAS,EAAC,oBAAoB;cAAAD,QAAA,gBACjExF,OAAA;gBAAIyF,SAAS,EAAC,kBAAkB;gBAAAD,QAAA,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5D7F,OAAA,CAACf,IAAI;gBAAC8B,IAAI,EAAEA,IAAK;gBAACmF,QAAQ,EAAE7C,iBAAkB;gBAAC8C,MAAM,EAAC,UAAU;gBAAAX,QAAA,gBAC5DxF,OAAA,CAACf,IAAI,CAACmH,IAAI;kBACNC,IAAI,EAAC,MAAM;kBACXC,KAAK,EAAC,YAAY;kBAClBC,KAAK,EAAE,CAAC;oBAAEC,QAAQ,EAAE,IAAI;oBAAE1H,OAAO,EAAE;kBAA0B,CAAC,CAAE;kBAAA0G,QAAA,eAEhExF,OAAA,CAAChB,KAAK,CAAC0H,QAAQ;oBACXC,IAAI,EAAE,CAAE;oBACRF,WAAW,EAAC,qBAAqB;oBACjChB,SAAS,EAAC,iBAAiB;oBAC3B0D,SAAS;kBAAA;oBAAAzD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACZ7F,OAAA,CAACf,IAAI,CAACmH,IAAI;kBAACX,SAAS,EAAC,eAAe;kBAAAD,QAAA,gBAChCxF,OAAA,CAACjB,MAAM;oBAAC+G,IAAI,EAAC,SAAS;oBAACc,QAAQ,EAAC,QAAQ;oBAACnB,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,EAAC;kBAEtE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT7F,OAAA,CAACjB,MAAM;oBAACiH,OAAO,EAAEA,CAAA,KAAMlF,kBAAkB,CAAC,IAAI,CAAE;oBAAC2E,SAAS,EAAC,kBAAkB;oBAAAD,QAAA,EAAC;kBAE9E;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACR,eAGD7F,OAAA;cAAKyF,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC7BxF,OAAA,CAACjB,MAAM;gBACHgH,IAAI,eAAE/F,OAAA,CAACH,eAAe;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BG,OAAO,EAAEA,CAAA,KAAMpD,WAAW,CAACkE,QAAQ,CAAC3E,GAAG,CAAE;gBACzCsD,SAAS,EAAC,sBAAsB;gBAChCK,IAAI,EAAEjF,eAAe,KAAKiG,QAAQ,CAAC3E,GAAG,GAAG,SAAS,GAAG,SAAU;gBAAAqD,QAAA,EAE9D3E,eAAe,KAAKiG,QAAQ,CAAC3E,GAAG,GAAG,cAAc,GAAG;cAAO;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC,eACT7F,OAAA,CAACjB,MAAM;gBACHgH,IAAI,eAAE/F,OAAA,CAACH,eAAe;kBAAA6F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BJ,SAAS,EAAC,qBAAqB;gBAC/B2D,QAAQ;gBACRjB,KAAK,EAAE;kBAAEkB,OAAO,EAAE;gBAAI,CAAE;gBAAA7D,QAAA,GAEvB,EAAA8B,kBAAA,GAAAR,QAAQ,CAACmB,OAAO,cAAAX,kBAAA,uBAAhBA,kBAAA,CAAkBlF,MAAM,KAAI,CAAC,EAAC,UACnC;cAAA;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA,GArJAiB,QAAQ,CAAC3E,GAAG;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwJjB,CAAC;QAAA,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGLxE,cAAc,GAAG,CAAC,iBACfrB,OAAA;QAAKyF,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC7BxF,OAAA;UAAKyF,SAAS,EAAC,iBAAiB;UAAAD,QAAA,gBAC5BxF,OAAA;YAAMyF,SAAS,EAAC,iBAAiB;YAAAD,QAAA,GAAC,UACtB,EAAC7B,SAAS,EAAC,GAAC,EAACC,OAAO,EAAC,MAAI,EAACvC,cAAc,EAAC,YACrD;UAAA;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACNrC,UAAU,GAAG,CAAC,iBACXxD,OAAA;YAAMyF,SAAS,EAAC,kBAAkB;YAAAD,QAAA,GAAC,OAC1B,EAACtE,WAAW,EAAC,MAAI,EAACsC,UAAU;UAAA;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EACLrC,UAAU,GAAG,CAAC,iBACXxD,OAAA;UAAKyF,SAAS,EAAC,qBAAqB;UAAAD,QAAA,gBAChCxF,OAAA,CAACjB,MAAM;YACHiH,OAAO,EAAEA,CAAA,KAAMlC,gBAAgB,CAAC5C,WAAW,GAAG,CAAC,CAAE;YACjDkI,QAAQ,EAAElI,WAAW,KAAK,CAAE;YAC5BuE,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAC7B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERyD,KAAK,CAACC,IAAI,CAAC;YAAEnH,MAAM,EAAEqB,IAAI,CAACI,GAAG,CAAC,CAAC,EAAEL,UAAU;UAAE,CAAC,EAAE,CAACgG,CAAC,EAAEC,CAAC,KAAK;YACvD,IAAIC,OAAO;YACX,IAAIlG,UAAU,IAAI,CAAC,EAAE;cACjBkG,OAAO,GAAGD,CAAC,GAAG,CAAC;YACnB,CAAC,MAAM,IAAIvI,WAAW,IAAI,CAAC,EAAE;cACzBwI,OAAO,GAAGD,CAAC,GAAG,CAAC;YACnB,CAAC,MAAM,IAAIvI,WAAW,IAAIsC,UAAU,GAAG,CAAC,EAAE;cACtCkG,OAAO,GAAGlG,UAAU,GAAG,CAAC,GAAGiG,CAAC;YAChC,CAAC,MAAM;cACHC,OAAO,GAAGxI,WAAW,GAAG,CAAC,GAAGuI,CAAC;YACjC;YAEA,oBACIzJ,OAAA,CAACjB,MAAM;cAEHiH,OAAO,EAAEA,CAAA,KAAMlC,gBAAgB,CAAC4F,OAAO,CAAE;cACzCjE,SAAS,EAAG,kBAAiBvE,WAAW,KAAKwI,OAAO,GAAG,QAAQ,GAAG,EAAG,EAAE;cAAAlE,QAAA,EAEtEkE;YAAO,GAJHA,OAAO;cAAAhE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKR,CAAC;UAEjB,CAAC,CAAC,eAEF7F,OAAA,CAACjB,MAAM;YACHiH,OAAO,EAAEA,CAAA,KAAMlC,gBAAgB,CAAC5C,WAAW,GAAG,CAAC,CAAE;YACjDkI,QAAQ,EAAElI,WAAW,KAAKsC,UAAW;YACrCiC,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAC7B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAzF,EAAA,CAtfKD,KAAK;EAAA,QAMQlB,IAAI,CAAC+B,OAAO,EACV3B,WAAW;AAAA;AAAAsK,EAAA,GAP1BxJ,KAAK;AAwfX,eAAeA,KAAK;AAAC,IAAAwJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}