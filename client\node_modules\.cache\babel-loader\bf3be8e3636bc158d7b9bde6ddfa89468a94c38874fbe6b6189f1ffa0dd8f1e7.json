{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Forum\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport './index.css';\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message, Button, Input, Form, Avatar } from \"antd\";\nimport PageTitle from \"../../../components/PageTitle\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\nimport image from '../../../assets/person.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Forum = () => {\n  _s();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [userData, setUserData] = useState('');\n  const [questions, setQuestions] = useState([]);\n  const [expandedReplies, setExpandedReplies] = useState({});\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const [replyRefs, setReplyRefs] = useState({});\n  const fetchQuestions = async () => {\n    try {\n      const response = await getAllQuestions();\n      if (response.success) {\n        setQuestions(response.data.reverse());\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchQuestions();\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n    dispatch(HideLoading());\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      dispatch(ShowLoading());\n      getUserData();\n    }\n  }, []);\n  const toggleReplies = questionId => {\n    setExpandedReplies(prevExpandedReplies => ({\n      ...prevExpandedReplies,\n      [questionId]: !prevExpandedReplies[questionId]\n    }));\n  };\n  const handleAskQuestion = async values => {\n    try {\n      const response = await addQuestion(values);\n      if (response.success) {\n        message.success(response.message);\n        setAskQuestionVisible(false);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  const handleReply = questionId => {\n    setReplyQuestionId(questionId);\n  };\n  const handleReplySubmit = async values => {\n    try {\n      const payload = {\n        questionId: replyQuestionId,\n        text: values.text\n      };\n      const response = await addReply(payload);\n      if (response.success) {\n        message.success(response.message);\n        setReplyQuestionId(null);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\n      setReplyRefs(prevRefs => ({\n        ...prevRefs,\n        [replyQuestionId]: /*#__PURE__*/React.createRef()\n      }));\n    }\n  }, [replyQuestionId, replyRefs]);\n  useEffect(() => {\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\n      replyRefs[replyQuestionId].current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [replyQuestionId, replyRefs]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"Forum\",\n      children: [/*#__PURE__*/_jsxDEV(PageTitle, {\n        title: \"Forum\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"divider\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Welcome to the forum! Feel free to ask questions, share your thoughts, and engage with the community.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setAskQuestionVisible(true),\n          style: {\n            marginBottom: 20\n          },\n          children: \"Ask a Question\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 21\n      }, this), askQuestionVisible && /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        onFinish: handleAskQuestion,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"title\",\n          label: \"Title\",\n          rules: [{\n            required: true,\n            message: 'Please enter the title'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            style: {\n              padding: '18px 12px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"body\",\n          label: \"Body\",\n          rules: [{\n            required: true,\n            message: 'Please enter the body'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            children: \"Ask Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setAskQuestionVisible(false),\n            style: {\n              marginLeft: 10\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 25\n      }, this), questions.map(question => {\n        var _question$user, _question$user2, _question$replies;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"forum-question-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"question\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"profile-row\",\n              children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                src: (_question$user = question.user) !== null && _question$user !== void 0 && _question$user.profileImage ? question.user.profileImage : image,\n                alt: \"profile\",\n                size: 50\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: ((_question$user2 = question.user) === null || _question$user2 === void 0 ? void 0 : _question$user2.name) || 'Anonymous'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"title\",\n              children: question.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"body\",\n              children: question.body\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => toggleReplies(question._id),\n              children: expandedReplies[question._id] ? \"Collapse Replies\" : \"Expand Replies\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => handleReply(question._id),\n              children: \"Reply\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 29\n          }, this), expandedReplies[question._id] && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"replies\",\n            children: ((_question$replies = question.replies) === null || _question$replies === void 0 ? void 0 : _question$replies.map(reply => {\n              var _reply$user, _reply$user2;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reply\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"profile-row\",\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    src: (_reply$user = reply.user) !== null && _reply$user !== void 0 && _reply$user.profileImage ? reply.user.profileImage : image,\n                    alt: \"profile\",\n                    size: 50\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 176,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: ((_reply$user2 = reply.user) === null || _reply$user2 === void 0 ? void 0 : _reply$user2.name) || 'Anonymous'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 177,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 175,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text\",\n                  children: reply.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 179,\n                  columnNumber: 45\n                }, this)]\n              }, reply._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 41\n              }, this);\n            })) || /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"No replies yet.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: replyRefs[question._id],\n            children: replyQuestionId === question._id && /*#__PURE__*/_jsxDEV(Form, {\n              form: form,\n              onFinish: handleReplySubmit,\n              layout: \"vertical\",\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"text\",\n                label: \"Your Reply\",\n                rules: [{\n                  required: true,\n                  message: 'Please enter your reply'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                  rows: 4\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  children: \"Submit Reply\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: () => setReplyQuestionId(null),\n                  style: {\n                    marginLeft: 10\n                  },\n                  children: \"Cancel\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 29\n          }, this)]\n        }, question._id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 25\n        }, this);\n      })]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 125,\n    columnNumber: 9\n  }, this);\n};\n_s(Forum, \"Y+McI4Dtk0yB6DNcWORyqfnj2sA=\", false, function () {\n  return [Form.useForm, useDispatch];\n});\n_c = Forum;\nexport default Forum;\nvar _c;\n$RefreshReg$(_c, \"Forum\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getUserInfo", "message", "<PERSON><PERSON>", "Input", "Form", "Avatar", "Page<PERSON><PERSON>le", "useDispatch", "HideLoading", "ShowLoading", "addQuestion", "addReply", "getAllQuestions", "image", "jsxDEV", "_jsxDEV", "Forum", "_s", "isAdmin", "setIsAdmin", "userData", "setUserData", "questions", "setQuestions", "expandedReplies", "setExpandedReplies", "askQuestionVisible", "setAskQuestionVisible", "replyQuestionId", "setReplyQuestionId", "form", "useForm", "dispatch", "replyRefs", "setReplyRefs", "fetchQuestions", "response", "success", "data", "reverse", "error", "getUserData", "localStorage", "getItem", "toggleReplies", "questionId", "prevExpandedReplies", "handleAskQuestion", "values", "resetFields", "handleReply", "handleReplySubmit", "payload", "text", "prevRefs", "createRef", "current", "scrollIntoView", "behavior", "children", "className", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "style", "marginBottom", "onFinish", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "padding", "TextArea", "type", "htmlType", "marginLeft", "map", "question", "_question$user", "_question$user2", "_question$replies", "src", "user", "profileImage", "alt", "size", "body", "_id", "replies", "reply", "_reply$user", "_reply$user2", "ref", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport './index.css';\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar } from \"antd\";\r\nimport PageTitle from \"../../../components/PageTitle\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\r\nimport image from '../../../assets/person.png';\r\n\r\nconst Forum = () => {\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [userData, setUserData] = useState('');\r\n    const [questions, setQuestions] = useState([]);\r\n    const [expandedReplies, setExpandedReplies] = useState({});\r\n    const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n    const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n    const [form] = Form.useForm();\r\n    const dispatch = useDispatch();\r\n    const [replyRefs, setReplyRefs] = useState({});\r\n\r\n    const fetchQuestions = async () => {\r\n        try {\r\n            const response = await getAllQuestions();\r\n            if (response.success) {\r\n                setQuestions(response.data.reverse());\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchQuestions();\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n        dispatch(HideLoading());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n    const toggleReplies = (questionId) => {\r\n        setExpandedReplies((prevExpandedReplies) => ({\r\n            ...prevExpandedReplies,\r\n            [questionId]: !prevExpandedReplies[questionId],\r\n        }));\r\n    };\r\n\r\n    const handleAskQuestion = async (values) => {\r\n        try {\r\n            const response = await addQuestion(values);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setAskQuestionVisible(false);\r\n                form.resetFields();\r\n                await fetchQuestions();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    const handleReply = (questionId) => {\r\n        setReplyQuestionId(questionId);\r\n    };\r\n\r\n    const handleReplySubmit = async (values) => {\r\n        try {\r\n            const payload = {\r\n                questionId: replyQuestionId,\r\n                text: values.text\r\n            };\r\n            const response = await addReply(payload);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setReplyQuestionId(null);\r\n                form.resetFields();\r\n                await fetchQuestions();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n            setReplyRefs((prevRefs) => ({\r\n                ...prevRefs,\r\n                [replyQuestionId]: React.createRef(),\r\n            }));\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n            replyRefs[replyQuestionId].current.scrollIntoView({ behavior: 'smooth' });\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    return (\r\n        <div>\r\n            {!isAdmin && (\r\n                <div className=\"Forum\">\r\n                    <PageTitle title=\"Forum\" />\r\n                    <div className=\"divider\"></div>\r\n\r\n                    <div>\r\n                        <p>Welcome to the forum! Feel free to ask questions, share your thoughts, and engage with the community.</p>\r\n                        <Button onClick={() => setAskQuestionVisible(true)} style={{ marginBottom: 20 }}>\r\n                            Ask a Question\r\n                        </Button>\r\n                    </div>\r\n\r\n                    {askQuestionVisible && (\r\n                        <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\">\r\n                            <Form.Item name=\"title\" label=\"Title\" rules={[{ required: true, message: 'Please enter the title' }]}>\r\n                                <Input style={{ padding: '18px 12px' }} />\r\n                            </Form.Item>\r\n                            <Form.Item name=\"body\" label=\"Body\" rules={[{ required: true, message: 'Please enter the body' }]}>\r\n                                <Input.TextArea />\r\n                            </Form.Item>\r\n                            <Form.Item>\r\n                                <Button type=\"primary\" htmlType=\"submit\">\r\n                                    Ask Question\r\n                                </Button>\r\n                                <Button onClick={() => setAskQuestionVisible(false)} style={{ marginLeft: 10 }}>\r\n                                    Cancel\r\n                                </Button>\r\n                            </Form.Item>\r\n                        </Form>\r\n                    )}\r\n\r\n                    {questions.map((question) => (\r\n                        <div key={question._id} className=\"forum-question-container\">\r\n                            <div className=\"question\">\r\n                                <div className=\"profile-row\">\r\n                                    <Avatar src={question.user?.profileImage ? question.user.profileImage : image} alt=\"profile\" size={50} />\r\n                                    <p>{question.user?.name || 'Anonymous'}</p>\r\n                                </div>\r\n                                <div className=\"title\">{question.title}</div>\r\n                                <div className=\"body\">{question.body}</div>\r\n                                <Button onClick={() => toggleReplies(question._id)}>\r\n                                    {expandedReplies[question._id] ? \"Collapse Replies\" : \"Expand Replies\"}\r\n                                </Button>\r\n                                <Button onClick={() => handleReply(question._id)}>Reply</Button>\r\n                            </div>\r\n                            {expandedReplies[question._id] && (\r\n                                <div className=\"replies\">\r\n                                    {question.replies?.map((reply) => (\r\n                                        <div key={reply._id} className=\"reply\">\r\n                                            <div className=\"profile-row\">\r\n                                                <Avatar src={reply.user?.profileImage ? reply.user.profileImage : image} alt=\"profile\" size={50} />\r\n                                                <p>{reply.user?.name || 'Anonymous'}</p>\r\n                                            </div>\r\n                                            <div className=\"text\">{reply.text}</div>\r\n                                        </div>\r\n                                    )) || <p>No replies yet.</p>}\r\n                                </div>\r\n                            )}\r\n                            <div ref={replyRefs[question._id]}>\r\n                                {replyQuestionId === question._id && (\r\n                                    <Form form={form} onFinish={handleReplySubmit} layout=\"vertical\">\r\n                                        <Form.Item name=\"text\" label=\"Your Reply\" rules={[{ required: true, message: 'Please enter your reply' }]}>\r\n                                            <Input.TextArea rows={4} />\r\n                                        </Form.Item>\r\n                                        <Form.Item>\r\n                                            <Button type=\"primary\" htmlType=\"submit\">\r\n                                                Submit Reply\r\n                                            </Button>\r\n                                            <Button onClick={() => setReplyQuestionId(null)} style={{ marginLeft: 10 }}>\r\n                                                Cancel\r\n                                            </Button>\r\n                                        </Form.Item>\r\n                                    </Form>\r\n                                )}\r\n                            </div>\r\n                        </div>\r\n                    ))}\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Forum;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,QAAQ,MAAM;AAC3D,OAAOC,SAAS,MAAM,+BAA+B;AACrD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,WAAW,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,yBAAyB;AAChF,OAAOC,KAAK,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyB,SAAS,EAAEC,YAAY,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC2B,eAAe,EAAEC,kBAAkB,CAAC,GAAG5B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC6B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiC,IAAI,CAAC,GAAG1B,IAAI,CAAC2B,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGzB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAE9C,MAAMsC,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMxB,eAAe,CAAC,CAAC;MACxC,IAAIwB,QAAQ,CAACC,OAAO,EAAE;QAClBd,YAAY,CAACa,QAAQ,CAACE,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC;MACzC,CAAC,MAAM;QACHtC,OAAO,CAACuC,KAAK,CAACJ,QAAQ,CAACnC,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOuC,KAAK,EAAE;MACZvC,OAAO,CAACuC,KAAK,CAACA,KAAK,CAACvC,OAAO,CAAC;IAChC;EACJ,CAAC;EAED,MAAMwC,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAML,QAAQ,GAAG,MAAMpC,WAAW,CAAC,CAAC;MACpC,IAAIoC,QAAQ,CAACC,OAAO,EAAE;QAClB,IAAID,QAAQ,CAACE,IAAI,CAACpB,OAAO,EAAE;UACvBC,UAAU,CAAC,IAAI,CAAC;QACpB,CAAC,MAAM;UACHA,UAAU,CAAC,KAAK,CAAC;UACjBE,WAAW,CAACe,QAAQ,CAACE,IAAI,CAAC;UAC1B,MAAMH,cAAc,CAAC,CAAC;QAC1B;MACJ,CAAC,MAAM;QACHlC,OAAO,CAACuC,KAAK,CAACJ,QAAQ,CAACnC,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOuC,KAAK,EAAE;MACZvC,OAAO,CAACuC,KAAK,CAACA,KAAK,CAACvC,OAAO,CAAC;IAChC;IACA+B,QAAQ,CAACxB,WAAW,CAAC,CAAC,CAAC;EAC3B,CAAC;EAEDV,SAAS,CAAC,MAAM;IACZ,IAAI4C,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MAC/BX,QAAQ,CAACvB,WAAW,CAAC,CAAC,CAAC;MACvBgC,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAIC,UAAU,IAAK;IAClCpB,kBAAkB,CAAEqB,mBAAmB,KAAM;MACzC,GAAGA,mBAAmB;MACtB,CAACD,UAAU,GAAG,CAACC,mBAAmB,CAACD,UAAU;IACjD,CAAC,CAAC,CAAC;EACP,CAAC;EAED,MAAME,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IACxC,IAAI;MACA,MAAMZ,QAAQ,GAAG,MAAM1B,WAAW,CAACsC,MAAM,CAAC;MAC1C,IAAIZ,QAAQ,CAACC,OAAO,EAAE;QAClBpC,OAAO,CAACoC,OAAO,CAACD,QAAQ,CAACnC,OAAO,CAAC;QACjC0B,qBAAqB,CAAC,KAAK,CAAC;QAC5BG,IAAI,CAACmB,WAAW,CAAC,CAAC;QAClB,MAAMd,cAAc,CAAC,CAAC;MAC1B,CAAC,MAAM;QACHlC,OAAO,CAACuC,KAAK,CAACJ,QAAQ,CAACnC,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOuC,KAAK,EAAE;MACZvC,OAAO,CAACuC,KAAK,CAACA,KAAK,CAACvC,OAAO,CAAC;IAChC;EACJ,CAAC;EAED,MAAMiD,WAAW,GAAIL,UAAU,IAAK;IAChChB,kBAAkB,CAACgB,UAAU,CAAC;EAClC,CAAC;EAED,MAAMM,iBAAiB,GAAG,MAAOH,MAAM,IAAK;IACxC,IAAI;MACA,MAAMI,OAAO,GAAG;QACZP,UAAU,EAAEjB,eAAe;QAC3ByB,IAAI,EAAEL,MAAM,CAACK;MACjB,CAAC;MACD,MAAMjB,QAAQ,GAAG,MAAMzB,QAAQ,CAACyC,OAAO,CAAC;MACxC,IAAIhB,QAAQ,CAACC,OAAO,EAAE;QAClBpC,OAAO,CAACoC,OAAO,CAACD,QAAQ,CAACnC,OAAO,CAAC;QACjC4B,kBAAkB,CAAC,IAAI,CAAC;QACxBC,IAAI,CAACmB,WAAW,CAAC,CAAC;QAClB,MAAMd,cAAc,CAAC,CAAC;MAC1B,CAAC,MAAM;QACHlC,OAAO,CAACuC,KAAK,CAACJ,QAAQ,CAACnC,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOuC,KAAK,EAAE;MACZvC,OAAO,CAACuC,KAAK,CAACA,KAAK,CAACvC,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDH,SAAS,CAAC,MAAM;IACZ,IAAI8B,eAAe,IAAI,CAACK,SAAS,CAACL,eAAe,CAAC,EAAE;MAChDM,YAAY,CAAEoB,QAAQ,KAAM;QACxB,GAAGA,QAAQ;QACX,CAAC1B,eAAe,gBAAGhC,KAAK,CAAC2D,SAAS,CAAC;MACvC,CAAC,CAAC,CAAC;IACP;EACJ,CAAC,EAAE,CAAC3B,eAAe,EAAEK,SAAS,CAAC,CAAC;EAEhCnC,SAAS,CAAC,MAAM;IACZ,IAAI8B,eAAe,IAAIK,SAAS,CAACL,eAAe,CAAC,EAAE;MAC/CK,SAAS,CAACL,eAAe,CAAC,CAAC4B,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC7E;EACJ,CAAC,EAAE,CAAC9B,eAAe,EAAEK,SAAS,CAAC,CAAC;EAEhC,oBACIlB,OAAA;IAAA4C,QAAA,EACK,CAACzC,OAAO,iBACLH,OAAA;MAAK6C,SAAS,EAAC,OAAO;MAAAD,QAAA,gBAClB5C,OAAA,CAACT,SAAS;QAACuD,KAAK,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3BlD,OAAA;QAAK6C,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE/BlD,OAAA;QAAA4C,QAAA,gBACI5C,OAAA;UAAA4C,QAAA,EAAG;QAAqG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5GlD,OAAA,CAACb,MAAM;UAACgE,OAAO,EAAEA,CAAA,KAAMvC,qBAAqB,CAAC,IAAI,CAAE;UAACwC,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAT,QAAA,EAAC;QAEjF;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,EAELvC,kBAAkB,iBACfX,OAAA,CAACX,IAAI;QAAC0B,IAAI,EAAEA,IAAK;QAACuC,QAAQ,EAAEtB,iBAAkB;QAACuB,MAAM,EAAC,UAAU;QAAAX,QAAA,gBAC5D5C,OAAA,CAACX,IAAI,CAACmE,IAAI;UAACC,IAAI,EAAC,OAAO;UAACC,KAAK,EAAC,OAAO;UAACC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1E,OAAO,EAAE;UAAyB,CAAC,CAAE;UAAA0D,QAAA,eACjG5C,OAAA,CAACZ,KAAK;YAACgE,KAAK,EAAE;cAAES,OAAO,EAAE;YAAY;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACZlD,OAAA,CAACX,IAAI,CAACmE,IAAI;UAACC,IAAI,EAAC,MAAM;UAACC,KAAK,EAAC,MAAM;UAACC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAE1E,OAAO,EAAE;UAAwB,CAAC,CAAE;UAAA0D,QAAA,eAC9F5C,OAAA,CAACZ,KAAK,CAAC0E,QAAQ;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eACZlD,OAAA,CAACX,IAAI,CAACmE,IAAI;UAAAZ,QAAA,gBACN5C,OAAA,CAACb,MAAM;YAAC4E,IAAI,EAAC,SAAS;YAACC,QAAQ,EAAC,QAAQ;YAAApB,QAAA,EAAC;UAEzC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlD,OAAA,CAACb,MAAM;YAACgE,OAAO,EAAEA,CAAA,KAAMvC,qBAAqB,CAAC,KAAK,CAAE;YAACwC,KAAK,EAAE;cAAEa,UAAU,EAAE;YAAG,CAAE;YAAArB,QAAA,EAAC;UAEhF;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACT,EAEA3C,SAAS,CAAC2D,GAAG,CAAEC,QAAQ;QAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,iBAAA;QAAA,oBACpBtE,OAAA;UAAwB6C,SAAS,EAAC,0BAA0B;UAAAD,QAAA,gBACxD5C,OAAA;YAAK6C,SAAS,EAAC,UAAU;YAAAD,QAAA,gBACrB5C,OAAA;cAAK6C,SAAS,EAAC,aAAa;cAAAD,QAAA,gBACxB5C,OAAA,CAACV,MAAM;gBAACiF,GAAG,EAAE,CAAAH,cAAA,GAAAD,QAAQ,CAACK,IAAI,cAAAJ,cAAA,eAAbA,cAAA,CAAeK,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACC,YAAY,GAAG3E,KAAM;gBAAC4E,GAAG,EAAC,SAAS;gBAACC,IAAI,EAAE;cAAG;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACzGlD,OAAA;gBAAA4C,QAAA,EAAI,EAAAyB,eAAA,GAAAF,QAAQ,CAACK,IAAI,cAAAH,eAAA,uBAAbA,eAAA,CAAeZ,IAAI,KAAI;cAAW;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNlD,OAAA;cAAK6C,SAAS,EAAC,OAAO;cAAAD,QAAA,EAAEuB,QAAQ,CAACrB;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7ClD,OAAA;cAAK6C,SAAS,EAAC,MAAM;cAAAD,QAAA,EAAEuB,QAAQ,CAACS;YAAI;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3ClD,OAAA,CAACb,MAAM;cAACgE,OAAO,EAAEA,CAAA,KAAMtB,aAAa,CAACsC,QAAQ,CAACU,GAAG,CAAE;cAAAjC,QAAA,EAC9CnC,eAAe,CAAC0D,QAAQ,CAACU,GAAG,CAAC,GAAG,kBAAkB,GAAG;YAAgB;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACTlD,OAAA,CAACb,MAAM;cAACgE,OAAO,EAAEA,CAAA,KAAMhB,WAAW,CAACgC,QAAQ,CAACU,GAAG,CAAE;cAAAjC,QAAA,EAAC;YAAK;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC,EACLzC,eAAe,CAAC0D,QAAQ,CAACU,GAAG,CAAC,iBAC1B7E,OAAA;YAAK6C,SAAS,EAAC,SAAS;YAAAD,QAAA,EACnB,EAAA0B,iBAAA,GAAAH,QAAQ,CAACW,OAAO,cAAAR,iBAAA,uBAAhBA,iBAAA,CAAkBJ,GAAG,CAAEa,KAAK;cAAA,IAAAC,WAAA,EAAAC,YAAA;cAAA,oBACzBjF,OAAA;gBAAqB6C,SAAS,EAAC,OAAO;gBAAAD,QAAA,gBAClC5C,OAAA;kBAAK6C,SAAS,EAAC,aAAa;kBAAAD,QAAA,gBACxB5C,OAAA,CAACV,MAAM;oBAACiF,GAAG,EAAE,CAAAS,WAAA,GAAAD,KAAK,CAACP,IAAI,cAAAQ,WAAA,eAAVA,WAAA,CAAYP,YAAY,GAAGM,KAAK,CAACP,IAAI,CAACC,YAAY,GAAG3E,KAAM;oBAAC4E,GAAG,EAAC,SAAS;oBAACC,IAAI,EAAE;kBAAG;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnGlD,OAAA;oBAAA4C,QAAA,EAAI,EAAAqC,YAAA,GAAAF,KAAK,CAACP,IAAI,cAAAS,YAAA,uBAAVA,YAAA,CAAYxB,IAAI,KAAI;kBAAW;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACNlD,OAAA;kBAAK6C,SAAS,EAAC,MAAM;kBAAAD,QAAA,EAAEmC,KAAK,CAACzC;gBAAI;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,GALlC6B,KAAK,CAACF,GAAG;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMd,CAAC;YAAA,CACT,CAAC,kBAAIlD,OAAA;cAAA4C,QAAA,EAAG;YAAe;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CACR,eACDlD,OAAA;YAAKkF,GAAG,EAAEhE,SAAS,CAACiD,QAAQ,CAACU,GAAG,CAAE;YAAAjC,QAAA,EAC7B/B,eAAe,KAAKsD,QAAQ,CAACU,GAAG,iBAC7B7E,OAAA,CAACX,IAAI;cAAC0B,IAAI,EAAEA,IAAK;cAACuC,QAAQ,EAAElB,iBAAkB;cAACmB,MAAM,EAAC,UAAU;cAAAX,QAAA,gBAC5D5C,OAAA,CAACX,IAAI,CAACmE,IAAI;gBAACC,IAAI,EAAC,MAAM;gBAACC,KAAK,EAAC,YAAY;gBAACC,KAAK,EAAE,CAAC;kBAAEC,QAAQ,EAAE,IAAI;kBAAE1E,OAAO,EAAE;gBAA0B,CAAC,CAAE;gBAAA0D,QAAA,eACtG5C,OAAA,CAACZ,KAAK,CAAC0E,QAAQ;kBAACqB,IAAI,EAAE;gBAAE;kBAAApC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACZlD,OAAA,CAACX,IAAI,CAACmE,IAAI;gBAAAZ,QAAA,gBACN5C,OAAA,CAACb,MAAM;kBAAC4E,IAAI,EAAC,SAAS;kBAACC,QAAQ,EAAC,QAAQ;kBAAApB,QAAA,EAAC;gBAEzC;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlD,OAAA,CAACb,MAAM;kBAACgE,OAAO,EAAEA,CAAA,KAAMrC,kBAAkB,CAAC,IAAI,CAAE;kBAACsC,KAAK,EAAE;oBAAEa,UAAU,EAAE;kBAAG,CAAE;kBAAArB,QAAA,EAAC;gBAE5E;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UACT;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA,GA1CAiB,QAAQ,CAACU,GAAG;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA2CjB,CAAC;MAAA,CACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAhD,EAAA,CApMKD,KAAK;EAAA,QAOQZ,IAAI,CAAC2B,OAAO,EACVxB,WAAW;AAAA;AAAA4F,EAAA,GAR1BnF,KAAK;AAsMX,eAAeA,KAAK;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}