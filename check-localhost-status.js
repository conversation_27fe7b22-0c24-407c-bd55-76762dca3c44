const http = require('http');

console.log('🔍 Checking localhost services...\n');

// Check server (port 5000)
const checkServer = () => {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:5000/api/health', (res) => {
      console.log('✅ Server (port 5000): RUNNING');
      console.log(`   Status: ${res.statusCode}`);
      resolve(true);
    });
    
    req.on('error', () => {
      console.log('❌ Server (port 5000): NOT RUNNING');
      resolve(false);
    });
    
    req.setTimeout(3000, () => {
      console.log('⏰ Server (port 5000): TIMEOUT');
      req.destroy();
      resolve(false);
    });
  });
};

// Check client (port 3000)
const checkClient = () => {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:3000', (res) => {
      console.log('✅ Client (port 3000): RUNNING');
      console.log(`   Status: ${res.statusCode}`);
      resolve(true);
    });
    
    req.on('error', () => {
      console.log('❌ Client (port 3000): NOT RUNNING');
      resolve(false);
    });
    
    req.setTimeout(3000, () => {
      console.log('⏰ Client (port 3000): TIMEOUT');
      req.destroy();
      resolve(false);
    });
  });
};

// Run checks
(async () => {
  const serverRunning = await checkServer();
  const clientRunning = await checkClient();
  
  console.log('\n📊 Summary:');
  console.log(`Server: ${serverRunning ? '✅ RUNNING' : '❌ NOT RUNNING'}`);
  console.log(`Client: ${clientRunning ? '✅ RUNNING' : '❌ NOT RUNNING'}`);
  
  if (serverRunning && clientRunning) {
    console.log('\n🎉 Both services are running!');
    console.log('🔗 Open: http://localhost:3000');
  } else {
    console.log('\n⚠️ Some services are not running. Please check the startup logs.');
  }
})();
