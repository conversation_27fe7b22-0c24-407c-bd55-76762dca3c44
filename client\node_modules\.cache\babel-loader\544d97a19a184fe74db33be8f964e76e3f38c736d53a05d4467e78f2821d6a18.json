{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\admin\\\\StudyMaterials\\\\AddStudyMaterialForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Form, message, Select, Upload, Button } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addVideo, addNote, addPastPaper, addBook } from \"../../../apicalls/study\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport { FaUpload, FaVideo, FaFileAlt, FaBook, FaGraduationCap, FaCloudUploadAlt } from \"react-icons/fa\";\nimport \"./AddStudyMaterialForm.css\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nfunction AddStudyMaterialForm({\n  materialType,\n  onSuccess,\n  onCancel\n}) {\n  _s();\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const [loading, setLoading] = useState(false);\n  const [fileList, setFileList] = useState([]);\n  const [thumbnailList, setThumbnailList] = useState([]);\n  const [videoFileList, setVideoFileList] = useState([]);\n  const [uploadMethod, setUploadMethod] = useState(\"youtube\"); // \"youtube\", \"upload\", or \"s3url\"\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [uploadStatus, setUploadStatus] = useState(\"\"); // \"uploading\", \"processing\", \"complete\"\n  const [uploadSpeed, setUploadSpeed] = useState(0);\n  const [estimatedTime, setEstimatedTime] = useState(0);\n  const [uploadStartTime, setUploadStartTime] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n\n  // Get subjects based on level\n  const getSubjectsForLevel = level => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"primary_kiswahili\":\n        return primaryKiswahiliSubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = level => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"primary_kiswahili\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"secondary\":\n        return [\"1\", \"2\", \"3\", \"4\"];\n      case \"advance\":\n        return [\"5\", \"6\"];\n      default:\n        return [];\n    }\n  };\n  const [availableSubjects, setAvailableSubjects] = useState(primarySubjects);\n  const [availableClasses, setAvailableClasses] = useState([\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"]);\n  const [selectedAdditionalClasses, setSelectedAdditionalClasses] = useState([]);\n  const handleLevelChange = level => {\n    setAvailableSubjects(getSubjectsForLevel(level));\n    setAvailableClasses(getClassesForLevel(level));\n    setSelectedAdditionalClasses([]); // Reset additional classes when level changes\n    form.setFieldsValue({\n      subject: undefined,\n      className: undefined\n    });\n  };\n  const handleAdditionalClassesChange = classes => {\n    setSelectedAdditionalClasses(classes);\n  };\n  const handleCoreClassChange = value => {\n    // Remove the newly selected core class from additional classes if it's there\n    const filteredAdditionalClasses = selectedAdditionalClasses.filter(cls => cls !== value);\n    setSelectedAdditionalClasses(filteredAdditionalClasses);\n  };\n  const handleSubmit = async values => {\n    let timeoutId;\n    try {\n      setLoading(true);\n      setUploadProgress(0);\n      setUploadStatus(\"\");\n      dispatch(ShowLoading());\n\n      // Set a shorter timeout for faster UX\n      timeoutId = setTimeout(() => {\n        if (uploadProgress === 100) {\n          setLoading(false);\n          setUploadProgress(0);\n          setUploadStatus(\"\");\n          dispatch(HideLoading());\n          message.success(\"Material uploaded successfully!\");\n        }\n      }, 3000); // Reduced to 3 seconds for faster response\n\n      let response;\n      if (materialType === \"videos\") {\n        // Add additional classes to values for videos\n        const videoValues = {\n          ...values,\n          additionalClasses: selectedAdditionalClasses\n        };\n\n        // For videos, handle YouTube, S3 URL, and file upload methods\n        if (uploadMethod === \"youtube\") {\n          // Send JSON data for YouTube videos\n          setUploadStatus(\"Adding YouTube video...\");\n          response = await addVideo(videoValues);\n        } else if (uploadMethod === \"s3url\") {\n          // Handle S3 URL method with optional thumbnail upload\n          if (thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n            // If thumbnail is provided, create FormData to upload thumbnail\n            const formData = new FormData();\n\n            // Add form fields\n            Object.keys(videoValues).forEach(key => {\n              if (videoValues[key] !== undefined && videoValues[key] !== null) {\n                if (Array.isArray(videoValues[key])) {\n                  // Handle arrays (like additionalClasses)\n                  videoValues[key].forEach(item => formData.append(key, item));\n                } else {\n                  formData.append(key, videoValues[key]);\n                }\n              }\n            });\n\n            // Add thumbnail file\n            formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n            setUploadStatus(\"Uploading thumbnail and adding video...\");\n            response = await addVideo(formData);\n          } else {\n            // No thumbnail, send JSON data\n            setUploadStatus(\"Adding S3 video...\");\n            response = await addVideo(videoValues);\n          }\n        } else {\n          // Create FormData for video file upload\n          const formData = new FormData();\n\n          // Add form fields\n          Object.keys(videoValues).forEach(key => {\n            if (videoValues[key] !== undefined && videoValues[key] !== null) {\n              if (Array.isArray(videoValues[key])) {\n                // Handle arrays (like additionalClasses)\n                videoValues[key].forEach(item => formData.append(key, item));\n              } else {\n                formData.append(key, videoValues[key]);\n              }\n            }\n          });\n\n          // Add video file\n          if (videoFileList.length > 0 && videoFileList[0].originFileObj) {\n            formData.append(\"video\", videoFileList[0].originFileObj);\n            setUploadStatus(\"Uploading video file...\");\n          }\n\n          // Add thumbnail file if provided\n          if (thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n            console.log('📎 Adding thumbnail to upload:', thumbnailList[0].name);\n            formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n          }\n\n          // Upload with enhanced progress tracking\n          setUploadStartTime(Date.now());\n          response = await addVideo(formData, (progress, loaded, total) => {\n            setUploadProgress(progress);\n\n            // Calculate upload speed and estimated time\n            if (uploadStartTime) {\n              const elapsedTime = (Date.now() - uploadStartTime) / 1000; // seconds\n              const uploadedBytes = loaded || total * progress / 100;\n              const speed = uploadedBytes / elapsedTime; // bytes per second\n              const remainingBytes = total - uploadedBytes;\n              const estimatedSeconds = remainingBytes / speed;\n              setUploadSpeed(speed);\n              setEstimatedTime(estimatedSeconds);\n            }\n            if (progress === 100) {\n              setUploadStatus(\"Finalizing upload...\");\n            } else if (progress > 0) {\n              setUploadStatus(`Uploading... ${progress}%`);\n            }\n          });\n        }\n      } else {\n        // For other materials, create FormData\n        const formData = new FormData();\n\n        // Add form fields\n        Object.keys(values).forEach(key => {\n          if (values[key] !== undefined && values[key] !== null) {\n            formData.append(key, values[key]);\n          }\n        });\n\n        // Add files\n        if (fileList.length > 0 && fileList[0].originFileObj) {\n          formData.append(\"document\", fileList[0].originFileObj);\n        }\n        if (materialType === \"books\" && thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n          formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n        }\n\n        // Call appropriate API\n        switch (materialType) {\n          case \"study-notes\":\n            response = await addNote(formData);\n            break;\n          case \"past-papers\":\n            response = await addPastPaper(formData);\n            break;\n          case \"books\":\n            response = await addBook(formData);\n            break;\n          default:\n            throw new Error(\"Invalid material type\");\n        }\n      }\n      if (response && response.status === 201 && response.data.success) {\n        message.success(response.data.message);\n        form.resetFields();\n        setFileList([]);\n        setThumbnailList([]);\n        setVideoFileList([]);\n        setSelectedAdditionalClasses([]);\n        setUploadMethod(\"youtube\");\n        setUploadProgress(0);\n        setUploadStatus(\"\");\n        onSuccess(materialType);\n      } else if (response) {\n        var _response$data;\n        const errorMessage = ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.message) || \"Failed to add material\";\n        message.error(errorMessage);\n      } else {\n        message.error(\"Failed to add material - no response received\");\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3;\n      console.error(\"Error adding material:\", error);\n\n      // Handle authentication errors specifically\n      if (error.response && error.response.status === 401) {\n        message.error(\"Authentication failed. Please login again.\");\n        // Redirect to login\n        setTimeout(() => {\n          window.location.href = '/login';\n        }, 1000);\n        return;\n      }\n\n      // Provide specific error messages based on error type\n      if (error.code === 'ECONNABORTED') {\n        message.error(\"Upload timeout. Please try with a smaller file or check your internet connection.\");\n      } else if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 413) {\n        message.error(\"File too large. Please use a file smaller than 500MB.\");\n      } else if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 400) {\n        var _error$response$data;\n        message.error(((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"Invalid file or form data.\");\n      } else if (((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status) === 500) {\n        message.error(\"Server error. Please try again later.\");\n      } else {\n        message.error(\"Upload failed. Please check your internet connection and try again.\");\n      }\n    } finally {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n      setLoading(false);\n      setUploadProgress(0);\n      setUploadStatus(\"\");\n      dispatch(HideLoading());\n    }\n  };\n  const uploadProps = {\n    beforeUpload: () => false,\n    // Prevent auto upload\n    maxCount: 1,\n    accept: materialType === \"videos\" ? undefined : \".pdf,.doc,.docx,.ppt,.pptx\"\n  };\n  const videoUploadProps = {\n    beforeUpload: () => false,\n    maxCount: 1,\n    accept: \"video/*\"\n  };\n  const thumbnailUploadProps = {\n    beforeUpload: () => false,\n    maxCount: 1,\n    accept: \"image/*\"\n  };\n  const getMaterialIcon = () => {\n    switch (materialType) {\n      case \"videos\":\n        return /*#__PURE__*/_jsxDEV(FaVideo, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 16\n        }, this);\n      case \"study-notes\":\n        return /*#__PURE__*/_jsxDEV(FaFileAlt, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 16\n        }, this);\n      case \"past-papers\":\n        return /*#__PURE__*/_jsxDEV(FaGraduationCap, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 16\n        }, this);\n      case \"books\":\n        return /*#__PURE__*/_jsxDEV(FaBook, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(FaFileAlt, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  const getMaterialTitle = () => {\n    switch (materialType) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return \"Material\";\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"add-material-form\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"form-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-header-icon\",\n        children: [getMaterialIcon(), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: [\"Add New \", getMaterialTitle()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        initialValues: {\n          level: \"primary\",\n          className: \"\",\n          subject: \"\",\n          title: \"\",\n          year: \"\",\n          videoID: \"\",\n          videoUrl: \"\",\n          thumbnailUrl: \"\"\n        },\n        className: \"material-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"Level\",\n            name: \"level\",\n            rules: [{\n              required: true,\n              message: \"Please select a level\"\n            }],\n            className: \"form-item-half\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select level\",\n              onChange: handleLevelChange,\n              size: \"large\",\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"primary\",\n                children: \"Primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"secondary\",\n                children: \"Secondary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 373,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"advance\",\n                children: \"Advance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 374,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"Class\",\n            name: \"className\",\n            rules: [{\n              required: true,\n              message: \"Please select a class\"\n            }],\n            className: \"form-item-half\",\n            children: /*#__PURE__*/_jsxDEV(Select, {\n              placeholder: \"Select class\",\n              size: \"large\",\n              onChange: handleCoreClassChange,\n              children: availableClasses.map(cls => /*#__PURE__*/_jsxDEV(Option, {\n                value: cls,\n                children: cls\n              }, cls, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"Subject\",\n          name: \"subject\",\n          rules: [{\n            required: true,\n            message: \"Please select a subject\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"Select subject\",\n            size: \"large\",\n            children: availableSubjects.map(subject => /*#__PURE__*/_jsxDEV(Option, {\n              value: subject,\n              children: subject\n            }, subject, false, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this), materialType === \"videos\" && /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"Additional Classes (Optional)\",\n          className: \"additional-classes-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"additional-classes-info\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Select additional classes that can access this video (besides the core class selected above)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            mode: \"multiple\",\n            placeholder: \"Select additional classes (optional)\",\n            size: \"large\",\n            value: selectedAdditionalClasses,\n            onChange: handleAdditionalClassesChange,\n            style: {\n              width: '100%'\n            },\n            children: availableClasses.filter(cls => cls !== form.getFieldValue('className')) // Exclude the core class\n            .map(cls => /*#__PURE__*/_jsxDEV(Option, {\n              value: cls,\n              children: cls\n            }, cls, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"additional-classes-note\",\n            children: /*#__PURE__*/_jsxDEV(\"small\", {\n              children: \"Note: The video will be available to the core class and all selected additional classes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"Title\",\n          name: \"title\",\n          rules: [{\n            required: true,\n            message: \"Please enter a title\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: `Enter ${getMaterialTitle().toLowerCase()} title`,\n            className: \"form-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this), (materialType === \"past-papers\" || materialType === \"books\") && /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"Year\",\n          name: \"year\",\n          rules: [{\n            required: true,\n            message: \"Please enter the year\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Enter year (e.g., 2023)\",\n            className: \"form-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this), materialType === \"videos\" && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n            label: \"Upload Method\",\n            className: \"upload-method-section\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-method-selector\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `method-option ${uploadMethod === \"youtube\" ? \"active\" : \"\"}`,\n                onClick: () => setUploadMethod(\"youtube\"),\n                children: [/*#__PURE__*/_jsxDEV(FaVideo, {\n                  className: \"method-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"YouTube Video\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Add video using YouTube ID\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 469,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `method-option ${uploadMethod === \"s3url\" ? \"active\" : \"\"}`,\n                onClick: () => setUploadMethod(\"s3url\"),\n                children: [/*#__PURE__*/_jsxDEV(FaCloudUploadAlt, {\n                  className: \"method-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"S3 Object URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 482,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Add video using S3 bucket URL\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `method-option ${uploadMethod === \"upload\" ? \"active\" : \"\"}`,\n                onClick: () => setUploadMethod(\"upload\"),\n                children: [/*#__PURE__*/_jsxDEV(FaCloudUploadAlt, {\n                  className: \"method-icon\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 489,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Upload Video File\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"Upload video file to server\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 491,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 15\n          }, this), uploadMethod === \"youtube\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Video ID (YouTube)\",\n              name: \"videoID\",\n              rules: [{\n                required: uploadMethod === \"youtube\",\n                message: \"Please enter YouTube video ID\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Enter YouTube video ID (e.g., dQw4w9WgXcQ)\",\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Video URL (Optional)\",\n              name: \"videoUrl\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                placeholder: \"Enter video URL (optional)\",\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Thumbnail URL (Optional)\",\n              name: \"thumbnailUrl\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                placeholder: \"Enter thumbnail URL (optional)\",\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : uploadMethod === \"s3url\" ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"S3 Object URL\",\n              name: \"videoUrl\",\n              rules: [{\n                required: uploadMethod === \"s3url\",\n                message: \"Please enter S3 object URL\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"url\",\n                placeholder: \"Enter S3 object URL (e.g., https://your-bucket.s3.amazonaws.com/video.mp4)\",\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Drag & Drop Thumbnail (Optional)\",\n              className: \"upload-section\",\n              children: /*#__PURE__*/_jsxDEV(Upload, {\n                ...thumbnailUploadProps,\n                fileList: thumbnailList,\n                onChange: ({\n                  fileList\n                }) => setThumbnailList(fileList),\n                className: \"thumbnail-upload\",\n                onDrop: e => {\n                  e.preventDefault();\n                  setIsDragOver(false);\n                  const files = Array.from(e.dataTransfer.files);\n                  const imageFiles = files.filter(file => file.type.startsWith('image/'));\n                  if (imageFiles.length > 0) {\n                    const file = imageFiles[0];\n                    // Validate file size (5MB limit)\n                    if (file.size > 5 * 1024 * 1024) {\n                      message.error('Thumbnail file size must be less than 5MB');\n                      return;\n                    }\n                    setThumbnailList([{\n                      uid: '-1',\n                      name: file.name,\n                      status: 'done',\n                      originFileObj: file,\n                      url: URL.createObjectURL(file)\n                    }]);\n                    message.success('Thumbnail uploaded successfully!');\n                  } else {\n                    message.error('Please drop an image file (JPG, PNG, GIF)');\n                  }\n                },\n                onDragOver: e => {\n                  e.preventDefault();\n                  setIsDragOver(true);\n                },\n                onDragEnter: e => {\n                  e.preventDefault();\n                  setIsDragOver(true);\n                },\n                onDragLeave: e => {\n                  e.preventDefault();\n                  setIsDragOver(false);\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `upload-area small thumbnail-drop-zone ${isDragOver ? 'drag-over' : ''}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaUpload, {\n                    className: \"upload-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 593,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: isDragOver ? 'Drop thumbnail here!' : 'Drag & drop thumbnail or click to upload'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 594,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"upload-hint\",\n                    children: \"Auto-generated if not provided\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 595,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"upload-hint\",\n                    children: \"Supports JPG, PNG, GIF (Max: 5MB)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 596,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 592,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 546,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Upload Video File\",\n              className: \"upload-section\",\n              children: /*#__PURE__*/_jsxDEV(Upload, {\n                ...videoUploadProps,\n                fileList: videoFileList,\n                onChange: ({\n                  fileList\n                }) => setVideoFileList(fileList),\n                className: \"video-upload\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"upload-area\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCloudUploadAlt, {\n                    className: \"upload-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Click or drag video file to upload\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 615,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"upload-hint\",\n                    children: \"Supports MP4, AVI, MOV, WMV (Max: 500MB)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 616,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"upload-hint\",\n                    children: \"Large files may take several minutes to upload\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 617,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"Upload Custom Thumbnail (Optional)\",\n              className: \"upload-section\",\n              children: /*#__PURE__*/_jsxDEV(Upload, {\n                ...thumbnailUploadProps,\n                fileList: thumbnailList,\n                onChange: ({\n                  fileList\n                }) => setThumbnailList(fileList),\n                className: \"thumbnail-upload\",\n                onDrop: e => {\n                  e.preventDefault();\n                  setIsDragOver(false);\n                  const files = Array.from(e.dataTransfer.files);\n                  const imageFiles = files.filter(file => file.type.startsWith('image/'));\n                  if (imageFiles.length > 0) {\n                    const file = imageFiles[0];\n                    // Validate file size (5MB limit)\n                    if (file.size > 5 * 1024 * 1024) {\n                      message.error('Thumbnail file size must be less than 5MB');\n                      return;\n                    }\n                    setThumbnailList([{\n                      uid: '-1',\n                      name: file.name,\n                      status: 'done',\n                      originFileObj: file,\n                      url: URL.createObjectURL(file)\n                    }]);\n                    message.success('Thumbnail uploaded successfully!');\n                  } else {\n                    message.error('Please drop an image file (JPG, PNG, GIF)');\n                  }\n                },\n                onDragOver: e => {\n                  e.preventDefault();\n                  setIsDragOver(true);\n                },\n                onDragEnter: e => {\n                  e.preventDefault();\n                  setIsDragOver(true);\n                },\n                onDragLeave: e => {\n                  e.preventDefault();\n                  setIsDragOver(false);\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `upload-area small thumbnail-drop-zone ${isDragOver ? 'drag-over' : ''}`,\n                  children: [/*#__PURE__*/_jsxDEV(FaUpload, {\n                    className: \"upload-icon\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 669,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: isDragOver ? 'Drop thumbnail here!' : 'Drag & drop thumbnail or click to upload'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 670,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"upload-hint\",\n                    children: \"Auto-generated if not provided\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 671,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"upload-hint\",\n                    children: \"Supports JPG, PNG, GIF (Max: 5MB)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 672,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 668,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 626,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true), materialType !== \"videos\" && /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: `Upload ${getMaterialTitle()} Document`,\n          className: \"upload-section\",\n          children: /*#__PURE__*/_jsxDEV(Upload, {\n            ...uploadProps,\n            fileList: fileList,\n            onChange: ({\n              fileList\n            }) => setFileList(fileList),\n            className: \"document-upload\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-area\",\n              children: [/*#__PURE__*/_jsxDEV(FaCloudUploadAlt, {\n                className: \"upload-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 693,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Click or drag file to upload\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"upload-hint\",\n                children: \"Supports PDF, DOC, DOCX, PPT, PPTX\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 692,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 682,\n          columnNumber: 13\n        }, this), materialType === \"books\" && /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"Upload Thumbnail (Optional)\",\n          className: \"upload-section\",\n          children: /*#__PURE__*/_jsxDEV(Upload, {\n            ...thumbnailUploadProps,\n            fileList: thumbnailList,\n            onChange: ({\n              fileList\n            }) => setThumbnailList(fileList),\n            className: \"thumbnail-upload\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-area small\",\n              children: [/*#__PURE__*/_jsxDEV(FaUpload, {\n                className: \"upload-icon\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"Upload book cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 706,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 702,\n          columnNumber: 13\n        }, this), loading && uploadMethod === \"upload\" && materialType === \"videos\" && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"upload-progress-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"progress-text\",\n                children: uploadStatus\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 725,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"progress-percentage\",\n                children: [uploadProgress, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 724,\n              columnNumber: 17\n            }, this), uploadSpeed > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"upload-stats\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"upload-speed\",\n                children: [\"\\uD83D\\uDCCA \", (uploadSpeed / (1024 * 1024)).toFixed(2), \" MB/s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 21\n              }, this), estimatedTime > 0 && estimatedTime < 3600 && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"estimated-time\",\n                children: [\"\\u23F1\\uFE0F \", Math.ceil(estimatedTime), \"s remaining\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 734,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 729,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-bar\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"progress-fill\",\n              style: {\n                width: `${uploadProgress}%`,\n                transition: 'width 0.3s ease'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"progress-details\",\n            children: uploadProgress < 100 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"uploading-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83D\\uDCE4 Uploading video file to server...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 755,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                children: \"Please keep this tab open until upload completes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 754,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"processing-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"\\uD83C\\uDFAC Upload complete! Processing video and generating thumbnail...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 760,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                children: \"This may take a few moments for large files\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 761,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 722,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-actions\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"default\",\n            onClick: onCancel,\n            size: \"large\",\n            className: \"cancel-btn\",\n            disabled: loading,\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 769,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            loading: loading,\n            size: \"large\",\n            className: \"submit-btn\",\n            children: loading ? uploadMethod === \"upload\" && materialType === \"videos\" ? \"Uploading...\" : \"Adding...\" : `Add ${getMaterialTitle()}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 768,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 337,\n    columnNumber: 5\n  }, this);\n}\n_s(AddStudyMaterialForm, \"7XbTZLh/J6BxbgycpDOggpxju3g=\", false, function () {\n  return [Form.useForm, useDispatch];\n});\n_c = AddStudyMaterialForm;\nexport default AddStudyMaterialForm;\nvar _c;\n$RefreshReg$(_c, \"AddStudyMaterialForm\");", "map": {"version": 3, "names": ["React", "useState", "Form", "message", "Select", "Upload", "<PERSON><PERSON>", "useDispatch", "HideLoading", "ShowLoading", "addVideo", "addNote", "addPastPaper", "addBook", "primarySubjects", "primaryKiswahiliSubjects", "secondarySubjects", "advanceSubjects", "FaUpload", "FaVideo", "FaFileAlt", "FaBook", "FaGraduationCap", "FaCloudUploadAlt", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "AddStudyMaterialForm", "materialType", "onSuccess", "onCancel", "_s", "form", "useForm", "dispatch", "loading", "setLoading", "fileList", "setFileList", "thumbnailList", "setThumbnailList", "videoFileList", "setVideoFileList", "uploadMethod", "setUploadMethod", "uploadProgress", "setUploadProgress", "uploadStatus", "setUploadStatus", "uploadSpeed", "setUploadSpeed", "estimatedTime", "setEstimatedTime", "uploadStartTime", "setUploadStartTime", "isDragOver", "setIsDragOver", "getSubjectsForLevel", "level", "getClassesForLevel", "availableSubjects", "setAvailableSubjects", "availableClasses", "setAvailableClasses", "selectedAdditionalClasses", "setSelectedAdditionalClasses", "handleLevelChange", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subject", "undefined", "className", "handleAdditionalClassesChange", "classes", "handleCoreClassChange", "value", "filteredAdditionalClasses", "filter", "cls", "handleSubmit", "values", "timeoutId", "setTimeout", "success", "response", "videoValues", "additionalClasses", "length", "originFileObj", "formData", "FormData", "Object", "keys", "for<PERSON>ach", "key", "Array", "isArray", "item", "append", "console", "log", "name", "Date", "now", "progress", "loaded", "total", "elapsedTime", "uploadedBytes", "speed", "remainingBytes", "estimatedSeconds", "Error", "status", "data", "resetFields", "_response$data", "errorMessage", "error", "_error$response", "_error$response2", "_error$response3", "window", "location", "href", "code", "_error$response$data", "clearTimeout", "uploadProps", "beforeUpload", "maxCount", "accept", "videoUploadProps", "thumbnailUploadProps", "getMaterialIcon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getMaterialTitle", "children", "layout", "onFinish", "initialValues", "title", "year", "videoID", "videoUrl", "thumbnailUrl", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "onChange", "size", "map", "mode", "style", "width", "getFieldValue", "type", "toLowerCase", "onClick", "onDrop", "e", "preventDefault", "files", "from", "dataTransfer", "imageFiles", "file", "startsWith", "uid", "url", "URL", "createObjectURL", "onDragOver", "onDragEnter", "onDragLeave", "toFixed", "Math", "ceil", "transition", "disabled", "htmlType", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/admin/StudyMaterials/AddStudyMaterialForm.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { Form, message, Select, Upload, Button } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addVideo, addNote, addPastPaper, addBook } from \"../../../apicalls/study\";\nimport { primarySubjects, primaryKiswahiliSubjects, secondarySubjects, advanceSubjects } from \"../../../data/Subjects\";\nimport {\n  FaUpload,\n  FaVideo,\n  FaFileAlt,\n  FaBook,\n  FaGraduationCap,\n  FaCloudUploadAlt\n} from \"react-icons/fa\";\nimport \"./AddStudyMaterialForm.css\";\n\nconst { Option } = Select;\n\nfunction AddStudyMaterialForm({ materialType, onSuccess, onCancel }) {\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const [loading, setLoading] = useState(false);\n  const [fileList, setFileList] = useState([]);\n  const [thumbnailList, setThumbnailList] = useState([]);\n  const [videoFileList, setVideoFileList] = useState([]);\n  const [uploadMethod, setUploadMethod] = useState(\"youtube\"); // \"youtube\", \"upload\", or \"s3url\"\n  const [uploadProgress, setUploadProgress] = useState(0);\n  const [uploadStatus, setUploadStatus] = useState(\"\"); // \"uploading\", \"processing\", \"complete\"\n  const [uploadSpeed, setUploadSpeed] = useState(0);\n  const [estimatedTime, setEstimatedTime] = useState(0);\n  const [uploadStartTime, setUploadStartTime] = useState(null);\n  const [isDragOver, setIsDragOver] = useState(false);\n\n  // Get subjects based on level\n  const getSubjectsForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return primarySubjects;\n      case \"primary_kiswahili\":\n        return primaryKiswahiliSubjects;\n      case \"secondary\":\n        return secondarySubjects;\n      case \"advance\":\n        return advanceSubjects;\n      default:\n        return [];\n    }\n  };\n\n  // Get classes based on level\n  const getClassesForLevel = (level) => {\n    switch (level) {\n      case \"primary\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"primary_kiswahili\":\n        return [\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"];\n      case \"secondary\":\n        return [\"1\", \"2\", \"3\", \"4\"];\n      case \"advance\":\n        return [\"5\", \"6\"];\n      default:\n        return [];\n    }\n  };\n\n  const [availableSubjects, setAvailableSubjects] = useState(primarySubjects);\n  const [availableClasses, setAvailableClasses] = useState([\"1\", \"2\", \"3\", \"4\", \"5\", \"6\", \"7\"]);\n  const [selectedAdditionalClasses, setSelectedAdditionalClasses] = useState([]);\n\n  const handleLevelChange = (level) => {\n    setAvailableSubjects(getSubjectsForLevel(level));\n    setAvailableClasses(getClassesForLevel(level));\n    setSelectedAdditionalClasses([]); // Reset additional classes when level changes\n    form.setFieldsValue({ subject: undefined, className: undefined });\n  };\n\n  const handleAdditionalClassesChange = (classes) => {\n    setSelectedAdditionalClasses(classes);\n  };\n\n  const handleCoreClassChange = (value) => {\n    // Remove the newly selected core class from additional classes if it's there\n    const filteredAdditionalClasses = selectedAdditionalClasses.filter(cls => cls !== value);\n    setSelectedAdditionalClasses(filteredAdditionalClasses);\n  };\n\n  const handleSubmit = async (values) => {\n    let timeoutId;\n    try {\n      setLoading(true);\n      setUploadProgress(0);\n      setUploadStatus(\"\");\n      dispatch(ShowLoading());\n\n      // Set a shorter timeout for faster UX\n      timeoutId = setTimeout(() => {\n        if (uploadProgress === 100) {\n          setLoading(false);\n          setUploadProgress(0);\n          setUploadStatus(\"\");\n          dispatch(HideLoading());\n          message.success(\"Material uploaded successfully!\");\n        }\n      }, 3000); // Reduced to 3 seconds for faster response\n\n      let response;\n\n      if (materialType === \"videos\") {\n        // Add additional classes to values for videos\n        const videoValues = {\n          ...values,\n          additionalClasses: selectedAdditionalClasses\n        };\n\n        // For videos, handle YouTube, S3 URL, and file upload methods\n        if (uploadMethod === \"youtube\") {\n          // Send JSON data for YouTube videos\n          setUploadStatus(\"Adding YouTube video...\");\n          response = await addVideo(videoValues);\n        } else if (uploadMethod === \"s3url\") {\n          // Handle S3 URL method with optional thumbnail upload\n          if (thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n            // If thumbnail is provided, create FormData to upload thumbnail\n            const formData = new FormData();\n\n            // Add form fields\n            Object.keys(videoValues).forEach(key => {\n              if (videoValues[key] !== undefined && videoValues[key] !== null) {\n                if (Array.isArray(videoValues[key])) {\n                  // Handle arrays (like additionalClasses)\n                  videoValues[key].forEach(item => formData.append(key, item));\n                } else {\n                  formData.append(key, videoValues[key]);\n                }\n              }\n            });\n\n            // Add thumbnail file\n            formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n            setUploadStatus(\"Uploading thumbnail and adding video...\");\n            response = await addVideo(formData);\n          } else {\n            // No thumbnail, send JSON data\n            setUploadStatus(\"Adding S3 video...\");\n            response = await addVideo(videoValues);\n          }\n        } else {\n          // Create FormData for video file upload\n          const formData = new FormData();\n\n          // Add form fields\n          Object.keys(videoValues).forEach(key => {\n            if (videoValues[key] !== undefined && videoValues[key] !== null) {\n              if (Array.isArray(videoValues[key])) {\n                // Handle arrays (like additionalClasses)\n                videoValues[key].forEach(item => formData.append(key, item));\n              } else {\n                formData.append(key, videoValues[key]);\n              }\n            }\n          });\n\n          // Add video file\n          if (videoFileList.length > 0 && videoFileList[0].originFileObj) {\n            formData.append(\"video\", videoFileList[0].originFileObj);\n            setUploadStatus(\"Uploading video file...\");\n          }\n\n          // Add thumbnail file if provided\n          if (thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n            console.log('📎 Adding thumbnail to upload:', thumbnailList[0].name);\n            formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n          }\n\n          // Upload with enhanced progress tracking\n          setUploadStartTime(Date.now());\n          response = await addVideo(formData, (progress, loaded, total) => {\n            setUploadProgress(progress);\n\n            // Calculate upload speed and estimated time\n            if (uploadStartTime) {\n              const elapsedTime = (Date.now() - uploadStartTime) / 1000; // seconds\n              const uploadedBytes = loaded || (total * progress / 100);\n              const speed = uploadedBytes / elapsedTime; // bytes per second\n              const remainingBytes = total - uploadedBytes;\n              const estimatedSeconds = remainingBytes / speed;\n\n              setUploadSpeed(speed);\n              setEstimatedTime(estimatedSeconds);\n            }\n\n            if (progress === 100) {\n              setUploadStatus(\"Finalizing upload...\");\n            } else if (progress > 0) {\n              setUploadStatus(`Uploading... ${progress}%`);\n            }\n          });\n        }\n      } else {\n        // For other materials, create FormData\n        const formData = new FormData();\n        \n        // Add form fields\n        Object.keys(values).forEach(key => {\n          if (values[key] !== undefined && values[key] !== null) {\n            formData.append(key, values[key]);\n          }\n        });\n\n        // Add files\n        if (fileList.length > 0 && fileList[0].originFileObj) {\n          formData.append(\"document\", fileList[0].originFileObj);\n        }\n\n        if (materialType === \"books\" && thumbnailList.length > 0 && thumbnailList[0].originFileObj) {\n          formData.append(\"thumbnail\", thumbnailList[0].originFileObj);\n        }\n\n        // Call appropriate API\n        switch (materialType) {\n          case \"study-notes\":\n            response = await addNote(formData);\n            break;\n          case \"past-papers\":\n            response = await addPastPaper(formData);\n            break;\n          case \"books\":\n            response = await addBook(formData);\n            break;\n          default:\n            throw new Error(\"Invalid material type\");\n        }\n      }\n\n      if (response && response.status === 201 && response.data.success) {\n        message.success(response.data.message);\n        form.resetFields();\n        setFileList([]);\n        setThumbnailList([]);\n        setVideoFileList([]);\n        setSelectedAdditionalClasses([]);\n        setUploadMethod(\"youtube\");\n        setUploadProgress(0);\n        setUploadStatus(\"\");\n        onSuccess(materialType);\n      } else if (response) {\n        const errorMessage = response.data?.message || \"Failed to add material\";\n        message.error(errorMessage);\n      } else {\n        message.error(\"Failed to add material - no response received\");\n      }\n    } catch (error) {\n      console.error(\"Error adding material:\", error);\n\n      // Handle authentication errors specifically\n      if (error.response && error.response.status === 401) {\n        message.error(\"Authentication failed. Please login again.\");\n        // Redirect to login\n        setTimeout(() => {\n          window.location.href = '/login';\n        }, 1000);\n        return;\n      }\n\n      // Provide specific error messages based on error type\n      if (error.code === 'ECONNABORTED') {\n        message.error(\"Upload timeout. Please try with a smaller file or check your internet connection.\");\n      } else if (error.response?.status === 413) {\n        message.error(\"File too large. Please use a file smaller than 500MB.\");\n      } else if (error.response?.status === 400) {\n        message.error(error.response.data?.message || \"Invalid file or form data.\");\n      } else if (error.response?.status === 500) {\n        message.error(\"Server error. Please try again later.\");\n      } else {\n        message.error(\"Upload failed. Please check your internet connection and try again.\");\n      }\n    } finally {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n      setLoading(false);\n      setUploadProgress(0);\n      setUploadStatus(\"\");\n      dispatch(HideLoading());\n    }\n  };\n\n  const uploadProps = {\n    beforeUpload: () => false, // Prevent auto upload\n    maxCount: 1,\n    accept: materialType === \"videos\" ? undefined : \".pdf,.doc,.docx,.ppt,.pptx\",\n  };\n\n  const videoUploadProps = {\n    beforeUpload: () => false,\n    maxCount: 1,\n    accept: \"video/*\",\n  };\n\n  const thumbnailUploadProps = {\n    beforeUpload: () => false,\n    maxCount: 1,\n    accept: \"image/*\",\n  };\n\n  const getMaterialIcon = () => {\n    switch (materialType) {\n      case \"videos\":\n        return <FaVideo />;\n      case \"study-notes\":\n        return <FaFileAlt />;\n      case \"past-papers\":\n        return <FaGraduationCap />;\n      case \"books\":\n        return <FaBook />;\n      default:\n        return <FaFileAlt />;\n    }\n  };\n\n  const getMaterialTitle = () => {\n    switch (materialType) {\n      case \"videos\":\n        return \"Video\";\n      case \"study-notes\":\n        return \"Study Note\";\n      case \"past-papers\":\n        return \"Past Paper\";\n      case \"books\":\n        return \"Book\";\n      default:\n        return \"Material\";\n    }\n  };\n\n  return (\n    <div className=\"add-material-form\">\n      <div className=\"form-card\">\n        <div className=\"form-header-icon\">\n          {getMaterialIcon()}\n          <h3>Add New {getMaterialTitle()}</h3>\n        </div>\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          initialValues={{\n            level: \"primary\",\n            className: \"\",\n            subject: \"\",\n            title: \"\",\n            year: \"\",\n            videoID: \"\",\n            videoUrl: \"\",\n            thumbnailUrl: \"\"\n          }}\n          className=\"material-form\"\n        >\n          <div className=\"form-row\">\n            <Form.Item\n              label=\"Level\"\n              name=\"level\"\n              rules={[{ required: true, message: \"Please select a level\" }]}\n              className=\"form-item-half\"\n            >\n              <Select\n                placeholder=\"Select level\"\n                onChange={handleLevelChange}\n                size=\"large\"\n              >\n                <Option value=\"primary\">Primary</Option>\n                <Option value=\"secondary\">Secondary</Option>\n                <Option value=\"advance\">Advance</Option>\n              </Select>\n            </Form.Item>\n\n            <Form.Item\n              label=\"Class\"\n              name=\"className\"\n              rules={[{ required: true, message: \"Please select a class\" }]}\n              className=\"form-item-half\"\n            >\n              <Select\n                placeholder=\"Select class\"\n                size=\"large\"\n                onChange={handleCoreClassChange}\n              >\n                {availableClasses.map(cls => (\n                  <Option key={cls} value={cls}>{cls}</Option>\n                ))}\n              </Select>\n            </Form.Item>\n          </div>\n\n          <Form.Item\n            label=\"Subject\"\n            name=\"subject\"\n            rules={[{ required: true, message: \"Please select a subject\" }]}\n          >\n            <Select placeholder=\"Select subject\" size=\"large\">\n              {availableSubjects.map(subject => (\n                <Option key={subject} value={subject}>{subject}</Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          {materialType === \"videos\" && (\n            <Form.Item\n              label=\"Additional Classes (Optional)\"\n              className=\"additional-classes-section\"\n            >\n              <div className=\"additional-classes-info\">\n                <p>Select additional classes that can access this video (besides the core class selected above)</p>\n              </div>\n              <Select\n                mode=\"multiple\"\n                placeholder=\"Select additional classes (optional)\"\n                size=\"large\"\n                value={selectedAdditionalClasses}\n                onChange={handleAdditionalClassesChange}\n                style={{ width: '100%' }}\n              >\n                {availableClasses\n                  .filter(cls => cls !== form.getFieldValue('className')) // Exclude the core class\n                  .map(cls => (\n                    <Option key={cls} value={cls}>{cls}</Option>\n                  ))}\n              </Select>\n              <div className=\"additional-classes-note\">\n                <small>Note: The video will be available to the core class and all selected additional classes</small>\n              </div>\n            </Form.Item>\n          )}\n\n          <Form.Item\n            label=\"Title\"\n            name=\"title\"\n            rules={[{ required: true, message: \"Please enter a title\" }]}\n          >\n            <input\n              type=\"text\"\n              placeholder={`Enter ${getMaterialTitle().toLowerCase()} title`}\n              className=\"form-input\"\n            />\n          </Form.Item>\n\n          {(materialType === \"past-papers\" || materialType === \"books\") && (\n            <Form.Item\n              label=\"Year\"\n              name=\"year\"\n              rules={[{ required: true, message: \"Please enter the year\" }]}\n            >\n              <input\n                type=\"text\"\n                placeholder=\"Enter year (e.g., 2023)\"\n                className=\"form-input\"\n              />\n            </Form.Item>\n          )}\n\n          {materialType === \"videos\" && (\n            <>\n              <Form.Item\n                label=\"Upload Method\"\n                className=\"upload-method-section\"\n              >\n                <div className=\"upload-method-selector\">\n                  <div\n                    className={`method-option ${uploadMethod === \"youtube\" ? \"active\" : \"\"}`}\n                    onClick={() => setUploadMethod(\"youtube\")}\n                  >\n                    <FaVideo className=\"method-icon\" />\n                    <span>YouTube Video</span>\n                    <p>Add video using YouTube ID</p>\n                  </div>\n                  <div\n                    className={`method-option ${uploadMethod === \"s3url\" ? \"active\" : \"\"}`}\n                    onClick={() => setUploadMethod(\"s3url\")}\n                  >\n                    <FaCloudUploadAlt className=\"method-icon\" />\n                    <span>S3 Object URL</span>\n                    <p>Add video using S3 bucket URL</p>\n                  </div>\n                  <div\n                    className={`method-option ${uploadMethod === \"upload\" ? \"active\" : \"\"}`}\n                    onClick={() => setUploadMethod(\"upload\")}\n                  >\n                    <FaCloudUploadAlt className=\"method-icon\" />\n                    <span>Upload Video File</span>\n                    <p>Upload video file to server</p>\n                  </div>\n                </div>\n              </Form.Item>\n\n              {uploadMethod === \"youtube\" ? (\n                <>\n                  <Form.Item\n                    label=\"Video ID (YouTube)\"\n                    name=\"videoID\"\n                    rules={[{ required: uploadMethod === \"youtube\", message: \"Please enter YouTube video ID\" }]}\n                  >\n                    <input\n                      type=\"text\"\n                      placeholder=\"Enter YouTube video ID (e.g., dQw4w9WgXcQ)\"\n                      className=\"form-input\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"Video URL (Optional)\"\n                    name=\"videoUrl\"\n                  >\n                    <input\n                      type=\"url\"\n                      placeholder=\"Enter video URL (optional)\"\n                      className=\"form-input\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"Thumbnail URL (Optional)\"\n                    name=\"thumbnailUrl\"\n                  >\n                    <input\n                      type=\"url\"\n                      placeholder=\"Enter thumbnail URL (optional)\"\n                      className=\"form-input\"\n                    />\n                  </Form.Item>\n                </>\n              ) : uploadMethod === \"s3url\" ? (\n                <>\n                  <Form.Item\n                    label=\"S3 Object URL\"\n                    name=\"videoUrl\"\n                    rules={[{ required: uploadMethod === \"s3url\", message: \"Please enter S3 object URL\" }]}\n                  >\n                    <input\n                      type=\"url\"\n                      placeholder=\"Enter S3 object URL (e.g., https://your-bucket.s3.amazonaws.com/video.mp4)\"\n                      className=\"form-input\"\n                    />\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"Drag & Drop Thumbnail (Optional)\"\n                    className=\"upload-section\"\n                  >\n                    <Upload\n                      {...thumbnailUploadProps}\n                      fileList={thumbnailList}\n                      onChange={({ fileList }) => setThumbnailList(fileList)}\n                      className=\"thumbnail-upload\"\n                      onDrop={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(false);\n                        const files = Array.from(e.dataTransfer.files);\n                        const imageFiles = files.filter(file => file.type.startsWith('image/'));\n                        if (imageFiles.length > 0) {\n                          const file = imageFiles[0];\n                          // Validate file size (5MB limit)\n                          if (file.size > 5 * 1024 * 1024) {\n                            message.error('Thumbnail file size must be less than 5MB');\n                            return;\n                          }\n                          setThumbnailList([{\n                            uid: '-1',\n                            name: file.name,\n                            status: 'done',\n                            originFileObj: file,\n                            url: URL.createObjectURL(file)\n                          }]);\n                          message.success('Thumbnail uploaded successfully!');\n                        } else {\n                          message.error('Please drop an image file (JPG, PNG, GIF)');\n                        }\n                      }}\n                      onDragOver={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(true);\n                      }}\n                      onDragEnter={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(true);\n                      }}\n                      onDragLeave={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(false);\n                      }}\n                    >\n                      <div className={`upload-area small thumbnail-drop-zone ${isDragOver ? 'drag-over' : ''}`}>\n                        <FaUpload className=\"upload-icon\" />\n                        <p>{isDragOver ? 'Drop thumbnail here!' : 'Drag & drop thumbnail or click to upload'}</p>\n                        <p className=\"upload-hint\">Auto-generated if not provided</p>\n                        <p className=\"upload-hint\">Supports JPG, PNG, GIF (Max: 5MB)</p>\n                      </div>\n                    </Upload>\n                  </Form.Item>\n                </>\n              ) : (\n                <>\n                  <Form.Item\n                    label=\"Upload Video File\"\n                    className=\"upload-section\"\n                  >\n                    <Upload\n                      {...videoUploadProps}\n                      fileList={videoFileList}\n                      onChange={({ fileList }) => setVideoFileList(fileList)}\n                      className=\"video-upload\"\n                    >\n                      <div className=\"upload-area\">\n                        <FaCloudUploadAlt className=\"upload-icon\" />\n                        <p>Click or drag video file to upload</p>\n                        <p className=\"upload-hint\">Supports MP4, AVI, MOV, WMV (Max: 500MB)</p>\n                        <p className=\"upload-hint\">Large files may take several minutes to upload</p>\n                      </div>\n                    </Upload>\n                  </Form.Item>\n\n                  <Form.Item\n                    label=\"Upload Custom Thumbnail (Optional)\"\n                    className=\"upload-section\"\n                  >\n                    <Upload\n                      {...thumbnailUploadProps}\n                      fileList={thumbnailList}\n                      onChange={({ fileList }) => setThumbnailList(fileList)}\n                      className=\"thumbnail-upload\"\n                      onDrop={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(false);\n                        const files = Array.from(e.dataTransfer.files);\n                        const imageFiles = files.filter(file => file.type.startsWith('image/'));\n                        if (imageFiles.length > 0) {\n                          const file = imageFiles[0];\n                          // Validate file size (5MB limit)\n                          if (file.size > 5 * 1024 * 1024) {\n                            message.error('Thumbnail file size must be less than 5MB');\n                            return;\n                          }\n                          setThumbnailList([{\n                            uid: '-1',\n                            name: file.name,\n                            status: 'done',\n                            originFileObj: file,\n                            url: URL.createObjectURL(file)\n                          }]);\n                          message.success('Thumbnail uploaded successfully!');\n                        } else {\n                          message.error('Please drop an image file (JPG, PNG, GIF)');\n                        }\n                      }}\n                      onDragOver={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(true);\n                      }}\n                      onDragEnter={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(true);\n                      }}\n                      onDragLeave={(e) => {\n                        e.preventDefault();\n                        setIsDragOver(false);\n                      }}\n                    >\n                      <div className={`upload-area small thumbnail-drop-zone ${isDragOver ? 'drag-over' : ''}`}>\n                        <FaUpload className=\"upload-icon\" />\n                        <p>{isDragOver ? 'Drop thumbnail here!' : 'Drag & drop thumbnail or click to upload'}</p>\n                        <p className=\"upload-hint\">Auto-generated if not provided</p>\n                        <p className=\"upload-hint\">Supports JPG, PNG, GIF (Max: 5MB)</p>\n                      </div>\n                    </Upload>\n                  </Form.Item>\n                </>\n              )}\n            </>\n          )}\n\n          {materialType !== \"videos\" && (\n            <Form.Item\n              label={`Upload ${getMaterialTitle()} Document`}\n              className=\"upload-section\"\n            >\n              <Upload\n                {...uploadProps}\n                fileList={fileList}\n                onChange={({ fileList }) => setFileList(fileList)}\n                className=\"document-upload\"\n              >\n                <div className=\"upload-area\">\n                  <FaCloudUploadAlt className=\"upload-icon\" />\n                  <p>Click or drag file to upload</p>\n                  <p className=\"upload-hint\">Supports PDF, DOC, DOCX, PPT, PPTX</p>\n                </div>\n              </Upload>\n            </Form.Item>\n          )}\n\n          {materialType === \"books\" && (\n            <Form.Item\n              label=\"Upload Thumbnail (Optional)\"\n              className=\"upload-section\"\n            >\n              <Upload\n                {...thumbnailUploadProps}\n                fileList={thumbnailList}\n                onChange={({ fileList }) => setThumbnailList(fileList)}\n                className=\"thumbnail-upload\"\n              >\n                <div className=\"upload-area small\">\n                  <FaUpload className=\"upload-icon\" />\n                  <p>Upload book cover</p>\n                </div>\n              </Upload>\n            </Form.Item>\n          )}\n\n          {/* Enhanced Upload Progress Indicator */}\n          {loading && uploadMethod === \"upload\" && materialType === \"videos\" && (\n            <div className=\"upload-progress-section\">\n              <div className=\"progress-header\">\n                <div className=\"progress-info\">\n                  <span className=\"progress-text\">{uploadStatus}</span>\n                  <span className=\"progress-percentage\">{uploadProgress}%</span>\n                </div>\n                {uploadSpeed > 0 && (\n                  <div className=\"upload-stats\">\n                    <span className=\"upload-speed\">\n                      📊 {(uploadSpeed / (1024 * 1024)).toFixed(2)} MB/s\n                    </span>\n                    {estimatedTime > 0 && estimatedTime < 3600 && (\n                      <span className=\"estimated-time\">\n                        ⏱️ {Math.ceil(estimatedTime)}s remaining\n                      </span>\n                    )}\n                  </div>\n                )}\n              </div>\n\n              <div className=\"progress-bar\">\n                <div\n                  className=\"progress-fill\"\n                  style={{\n                    width: `${uploadProgress}%`,\n                    transition: 'width 0.3s ease'\n                  }}\n                ></div>\n              </div>\n\n              <div className=\"progress-details\">\n                {uploadProgress < 100 ? (\n                  <div className=\"uploading-info\">\n                    <span>📤 Uploading video file to server...</span>\n                    <small>Please keep this tab open until upload completes</small>\n                  </div>\n                ) : (\n                  <div className=\"processing-info\">\n                    <span>🎬 Upload complete! Processing video and generating thumbnail...</span>\n                    <small>This may take a few moments for large files</small>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          <div className=\"form-actions\">\n            <Button\n              type=\"default\"\n              onClick={onCancel}\n              size=\"large\"\n              className=\"cancel-btn\"\n              disabled={loading}\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              size=\"large\"\n              className=\"submit-btn\"\n            >\n              {loading ? (\n                uploadMethod === \"upload\" && materialType === \"videos\" ?\n                \"Uploading...\" : \"Adding...\"\n              ) : (\n                `Add ${getMaterialTitle()}`\n              )}\n            </Button>\n          </div>\n        </Form>\n      </div>\n    </div>\n  );\n}\n\nexport default AddStudyMaterialForm;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,OAAO,EAAEC,MAAM,EAAEC,MAAM,EAAEC,MAAM,QAAQ,MAAM;AAC5D,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,QAAQ,EAAEC,OAAO,EAAEC,YAAY,EAAEC,OAAO,QAAQ,yBAAyB;AAClF,SAASC,eAAe,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,eAAe,QAAQ,wBAAwB;AACtH,SACEC,QAAQ,EACRC,OAAO,EACPC,SAAS,EACTC,MAAM,EACNC,eAAe,EACfC,gBAAgB,QACX,gBAAgB;AACvB,OAAO,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAM;EAAEC;AAAO,CAAC,GAAGxB,MAAM;AAEzB,SAASyB,oBAAoBA,CAAC;EAAEC,YAAY;EAAEC,SAAS;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACnE,MAAM,CAACC,IAAI,CAAC,GAAGhC,IAAI,CAACiC,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0C,aAAa,EAAEC,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EAC7D,MAAM,CAAC8C,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC;EACvD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACoD,aAAa,EAAEC,gBAAgB,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACwD,UAAU,EAAEC,aAAa,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAM0D,mBAAmB,GAAIC,KAAK,IAAK;IACrC,QAAQA,KAAK;MACX,KAAK,SAAS;QACZ,OAAO9C,eAAe;MACxB,KAAK,mBAAmB;QACtB,OAAOC,wBAAwB;MACjC,KAAK,WAAW;QACd,OAAOC,iBAAiB;MAC1B,KAAK,SAAS;QACZ,OAAOC,eAAe;MACxB;QACE,OAAO,EAAE;IACb;EACF,CAAC;;EAED;EACA,MAAM4C,kBAAkB,GAAID,KAAK,IAAK;IACpC,QAAQA,KAAK;MACX,KAAK,SAAS;QACZ,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC5C,KAAK,mBAAmB;QACtB,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC5C,KAAK,WAAW;QACd,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC7B,KAAK,SAAS;QACZ,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;MACnB;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,MAAM,CAACE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9D,QAAQ,CAACa,eAAe,CAAC;EAC3E,MAAM,CAACkD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhE,QAAQ,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;EAC7F,MAAM,CAACiE,yBAAyB,EAAEC,4BAA4B,CAAC,GAAGlE,QAAQ,CAAC,EAAE,CAAC;EAE9E,MAAMmE,iBAAiB,GAAIR,KAAK,IAAK;IACnCG,oBAAoB,CAACJ,mBAAmB,CAACC,KAAK,CAAC,CAAC;IAChDK,mBAAmB,CAACJ,kBAAkB,CAACD,KAAK,CAAC,CAAC;IAC9CO,4BAA4B,CAAC,EAAE,CAAC,CAAC,CAAC;IAClCjC,IAAI,CAACmC,cAAc,CAAC;MAAEC,OAAO,EAAEC,SAAS;MAAEC,SAAS,EAAED;IAAU,CAAC,CAAC;EACnE,CAAC;EAED,MAAME,6BAA6B,GAAIC,OAAO,IAAK;IACjDP,4BAA4B,CAACO,OAAO,CAAC;EACvC,CAAC;EAED,MAAMC,qBAAqB,GAAIC,KAAK,IAAK;IACvC;IACA,MAAMC,yBAAyB,GAAGX,yBAAyB,CAACY,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAKH,KAAK,CAAC;IACxFT,4BAA4B,CAACU,yBAAyB,CAAC;EACzD,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAIC,SAAS;IACb,IAAI;MACF5C,UAAU,CAAC,IAAI,CAAC;MAChBU,iBAAiB,CAAC,CAAC,CAAC;MACpBE,eAAe,CAAC,EAAE,CAAC;MACnBd,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACAyE,SAAS,GAAGC,UAAU,CAAC,MAAM;QAC3B,IAAIpC,cAAc,KAAK,GAAG,EAAE;UAC1BT,UAAU,CAAC,KAAK,CAAC;UACjBU,iBAAiB,CAAC,CAAC,CAAC;UACpBE,eAAe,CAAC,EAAE,CAAC;UACnBd,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;UACvBL,OAAO,CAACiF,OAAO,CAAC,iCAAiC,CAAC;QACpD;MACF,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;MAEV,IAAIC,QAAQ;MAEZ,IAAIvD,YAAY,KAAK,QAAQ,EAAE;QAC7B;QACA,MAAMwD,WAAW,GAAG;UAClB,GAAGL,MAAM;UACTM,iBAAiB,EAAErB;QACrB,CAAC;;QAED;QACA,IAAIrB,YAAY,KAAK,SAAS,EAAE;UAC9B;UACAK,eAAe,CAAC,yBAAyB,CAAC;UAC1CmC,QAAQ,GAAG,MAAM3E,QAAQ,CAAC4E,WAAW,CAAC;QACxC,CAAC,MAAM,IAAIzC,YAAY,KAAK,OAAO,EAAE;UACnC;UACA,IAAIJ,aAAa,CAAC+C,MAAM,GAAG,CAAC,IAAI/C,aAAa,CAAC,CAAC,CAAC,CAACgD,aAAa,EAAE;YAC9D;YACA,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;YAE/B;YACAC,MAAM,CAACC,IAAI,CAACP,WAAW,CAAC,CAACQ,OAAO,CAACC,GAAG,IAAI;cACtC,IAAIT,WAAW,CAACS,GAAG,CAAC,KAAKxB,SAAS,IAAIe,WAAW,CAACS,GAAG,CAAC,KAAK,IAAI,EAAE;gBAC/D,IAAIC,KAAK,CAACC,OAAO,CAACX,WAAW,CAACS,GAAG,CAAC,CAAC,EAAE;kBACnC;kBACAT,WAAW,CAACS,GAAG,CAAC,CAACD,OAAO,CAACI,IAAI,IAAIR,QAAQ,CAACS,MAAM,CAACJ,GAAG,EAAEG,IAAI,CAAC,CAAC;gBAC9D,CAAC,MAAM;kBACLR,QAAQ,CAACS,MAAM,CAACJ,GAAG,EAAET,WAAW,CAACS,GAAG,CAAC,CAAC;gBACxC;cACF;YACF,CAAC,CAAC;;YAEF;YACAL,QAAQ,CAACS,MAAM,CAAC,WAAW,EAAE1D,aAAa,CAAC,CAAC,CAAC,CAACgD,aAAa,CAAC;YAC5DvC,eAAe,CAAC,yCAAyC,CAAC;YAC1DmC,QAAQ,GAAG,MAAM3E,QAAQ,CAACgF,QAAQ,CAAC;UACrC,CAAC,MAAM;YACL;YACAxC,eAAe,CAAC,oBAAoB,CAAC;YACrCmC,QAAQ,GAAG,MAAM3E,QAAQ,CAAC4E,WAAW,CAAC;UACxC;QACF,CAAC,MAAM;UACL;UACA,MAAMI,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;UAE/B;UACAC,MAAM,CAACC,IAAI,CAACP,WAAW,CAAC,CAACQ,OAAO,CAACC,GAAG,IAAI;YACtC,IAAIT,WAAW,CAACS,GAAG,CAAC,KAAKxB,SAAS,IAAIe,WAAW,CAACS,GAAG,CAAC,KAAK,IAAI,EAAE;cAC/D,IAAIC,KAAK,CAACC,OAAO,CAACX,WAAW,CAACS,GAAG,CAAC,CAAC,EAAE;gBACnC;gBACAT,WAAW,CAACS,GAAG,CAAC,CAACD,OAAO,CAACI,IAAI,IAAIR,QAAQ,CAACS,MAAM,CAACJ,GAAG,EAAEG,IAAI,CAAC,CAAC;cAC9D,CAAC,MAAM;gBACLR,QAAQ,CAACS,MAAM,CAACJ,GAAG,EAAET,WAAW,CAACS,GAAG,CAAC,CAAC;cACxC;YACF;UACF,CAAC,CAAC;;UAEF;UACA,IAAIpD,aAAa,CAAC6C,MAAM,GAAG,CAAC,IAAI7C,aAAa,CAAC,CAAC,CAAC,CAAC8C,aAAa,EAAE;YAC9DC,QAAQ,CAACS,MAAM,CAAC,OAAO,EAAExD,aAAa,CAAC,CAAC,CAAC,CAAC8C,aAAa,CAAC;YACxDvC,eAAe,CAAC,yBAAyB,CAAC;UAC5C;;UAEA;UACA,IAAIT,aAAa,CAAC+C,MAAM,GAAG,CAAC,IAAI/C,aAAa,CAAC,CAAC,CAAC,CAACgD,aAAa,EAAE;YAC9DW,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE5D,aAAa,CAAC,CAAC,CAAC,CAAC6D,IAAI,CAAC;YACpEZ,QAAQ,CAACS,MAAM,CAAC,WAAW,EAAE1D,aAAa,CAAC,CAAC,CAAC,CAACgD,aAAa,CAAC;UAC9D;;UAEA;UACAjC,kBAAkB,CAAC+C,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;UAC9BnB,QAAQ,GAAG,MAAM3E,QAAQ,CAACgF,QAAQ,EAAE,CAACe,QAAQ,EAAEC,MAAM,EAAEC,KAAK,KAAK;YAC/D3D,iBAAiB,CAACyD,QAAQ,CAAC;;YAE3B;YACA,IAAIlD,eAAe,EAAE;cACnB,MAAMqD,WAAW,GAAG,CAACL,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGjD,eAAe,IAAI,IAAI,CAAC,CAAC;cAC3D,MAAMsD,aAAa,GAAGH,MAAM,IAAKC,KAAK,GAAGF,QAAQ,GAAG,GAAI;cACxD,MAAMK,KAAK,GAAGD,aAAa,GAAGD,WAAW,CAAC,CAAC;cAC3C,MAAMG,cAAc,GAAGJ,KAAK,GAAGE,aAAa;cAC5C,MAAMG,gBAAgB,GAAGD,cAAc,GAAGD,KAAK;cAE/C1D,cAAc,CAAC0D,KAAK,CAAC;cACrBxD,gBAAgB,CAAC0D,gBAAgB,CAAC;YACpC;YAEA,IAAIP,QAAQ,KAAK,GAAG,EAAE;cACpBvD,eAAe,CAAC,sBAAsB,CAAC;YACzC,CAAC,MAAM,IAAIuD,QAAQ,GAAG,CAAC,EAAE;cACvBvD,eAAe,CAAE,gBAAeuD,QAAS,GAAE,CAAC;YAC9C;UACF,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL;QACA,MAAMf,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;QAE/B;QACAC,MAAM,CAACC,IAAI,CAACZ,MAAM,CAAC,CAACa,OAAO,CAACC,GAAG,IAAI;UACjC,IAAId,MAAM,CAACc,GAAG,CAAC,KAAKxB,SAAS,IAAIU,MAAM,CAACc,GAAG,CAAC,KAAK,IAAI,EAAE;YACrDL,QAAQ,CAACS,MAAM,CAACJ,GAAG,EAAEd,MAAM,CAACc,GAAG,CAAC,CAAC;UACnC;QACF,CAAC,CAAC;;QAEF;QACA,IAAIxD,QAAQ,CAACiD,MAAM,GAAG,CAAC,IAAIjD,QAAQ,CAAC,CAAC,CAAC,CAACkD,aAAa,EAAE;UACpDC,QAAQ,CAACS,MAAM,CAAC,UAAU,EAAE5D,QAAQ,CAAC,CAAC,CAAC,CAACkD,aAAa,CAAC;QACxD;QAEA,IAAI3D,YAAY,KAAK,OAAO,IAAIW,aAAa,CAAC+C,MAAM,GAAG,CAAC,IAAI/C,aAAa,CAAC,CAAC,CAAC,CAACgD,aAAa,EAAE;UAC1FC,QAAQ,CAACS,MAAM,CAAC,WAAW,EAAE1D,aAAa,CAAC,CAAC,CAAC,CAACgD,aAAa,CAAC;QAC9D;;QAEA;QACA,QAAQ3D,YAAY;UAClB,KAAK,aAAa;YAChBuD,QAAQ,GAAG,MAAM1E,OAAO,CAAC+E,QAAQ,CAAC;YAClC;UACF,KAAK,aAAa;YAChBL,QAAQ,GAAG,MAAMzE,YAAY,CAAC8E,QAAQ,CAAC;YACvC;UACF,KAAK,OAAO;YACVL,QAAQ,GAAG,MAAMxE,OAAO,CAAC6E,QAAQ,CAAC;YAClC;UACF;YACE,MAAM,IAAIuB,KAAK,CAAC,uBAAuB,CAAC;QAC5C;MACF;MAEA,IAAI5B,QAAQ,IAAIA,QAAQ,CAAC6B,MAAM,KAAK,GAAG,IAAI7B,QAAQ,CAAC8B,IAAI,CAAC/B,OAAO,EAAE;QAChEjF,OAAO,CAACiF,OAAO,CAACC,QAAQ,CAAC8B,IAAI,CAAChH,OAAO,CAAC;QACtC+B,IAAI,CAACkF,WAAW,CAAC,CAAC;QAClB5E,WAAW,CAAC,EAAE,CAAC;QACfE,gBAAgB,CAAC,EAAE,CAAC;QACpBE,gBAAgB,CAAC,EAAE,CAAC;QACpBuB,4BAA4B,CAAC,EAAE,CAAC;QAChCrB,eAAe,CAAC,SAAS,CAAC;QAC1BE,iBAAiB,CAAC,CAAC,CAAC;QACpBE,eAAe,CAAC,EAAE,CAAC;QACnBnB,SAAS,CAACD,YAAY,CAAC;MACzB,CAAC,MAAM,IAAIuD,QAAQ,EAAE;QAAA,IAAAgC,cAAA;QACnB,MAAMC,YAAY,GAAG,EAAAD,cAAA,GAAAhC,QAAQ,CAAC8B,IAAI,cAAAE,cAAA,uBAAbA,cAAA,CAAelH,OAAO,KAAI,wBAAwB;QACvEA,OAAO,CAACoH,KAAK,CAACD,YAAY,CAAC;MAC7B,CAAC,MAAM;QACLnH,OAAO,CAACoH,KAAK,CAAC,+CAA+C,CAAC;MAChE;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdtB,OAAO,CAACmB,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;MAE9C;MACA,IAAIA,KAAK,CAAClC,QAAQ,IAAIkC,KAAK,CAAClC,QAAQ,CAAC6B,MAAM,KAAK,GAAG,EAAE;QACnD/G,OAAO,CAACoH,KAAK,CAAC,4CAA4C,CAAC;QAC3D;QACApC,UAAU,CAAC,MAAM;UACfwC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QACjC,CAAC,EAAE,IAAI,CAAC;QACR;MACF;;MAEA;MACA,IAAIN,KAAK,CAACO,IAAI,KAAK,cAAc,EAAE;QACjC3H,OAAO,CAACoH,KAAK,CAAC,mFAAmF,CAAC;MACpG,CAAC,MAAM,IAAI,EAAAC,eAAA,GAAAD,KAAK,CAAClC,QAAQ,cAAAmC,eAAA,uBAAdA,eAAA,CAAgBN,MAAM,MAAK,GAAG,EAAE;QACzC/G,OAAO,CAACoH,KAAK,CAAC,uDAAuD,CAAC;MACxE,CAAC,MAAM,IAAI,EAAAE,gBAAA,GAAAF,KAAK,CAAClC,QAAQ,cAAAoC,gBAAA,uBAAdA,gBAAA,CAAgBP,MAAM,MAAK,GAAG,EAAE;QAAA,IAAAa,oBAAA;QACzC5H,OAAO,CAACoH,KAAK,CAAC,EAAAQ,oBAAA,GAAAR,KAAK,CAAClC,QAAQ,CAAC8B,IAAI,cAAAY,oBAAA,uBAAnBA,oBAAA,CAAqB5H,OAAO,KAAI,4BAA4B,CAAC;MAC7E,CAAC,MAAM,IAAI,EAAAuH,gBAAA,GAAAH,KAAK,CAAClC,QAAQ,cAAAqC,gBAAA,uBAAdA,gBAAA,CAAgBR,MAAM,MAAK,GAAG,EAAE;QACzC/G,OAAO,CAACoH,KAAK,CAAC,uCAAuC,CAAC;MACxD,CAAC,MAAM;QACLpH,OAAO,CAACoH,KAAK,CAAC,qEAAqE,CAAC;MACtF;IACF,CAAC,SAAS;MACR,IAAIrC,SAAS,EAAE;QACb8C,YAAY,CAAC9C,SAAS,CAAC;MACzB;MACA5C,UAAU,CAAC,KAAK,CAAC;MACjBU,iBAAiB,CAAC,CAAC,CAAC;MACpBE,eAAe,CAAC,EAAE,CAAC;MACnBd,QAAQ,CAAC5B,WAAW,CAAC,CAAC,CAAC;IACzB;EACF,CAAC;EAED,MAAMyH,WAAW,GAAG;IAClBC,YAAY,EAAEA,CAAA,KAAM,KAAK;IAAE;IAC3BC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAEtG,YAAY,KAAK,QAAQ,GAAGyC,SAAS,GAAG;EAClD,CAAC;EAED,MAAM8D,gBAAgB,GAAG;IACvBH,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC;EAED,MAAME,oBAAoB,GAAG;IAC3BJ,YAAY,EAAEA,CAAA,KAAM,KAAK;IACzBC,QAAQ,EAAE,CAAC;IACXC,MAAM,EAAE;EACV,CAAC;EAED,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5B,QAAQzG,YAAY;MAClB,KAAK,QAAQ;QACX,oBAAOL,OAAA,CAACN,OAAO;UAAAqH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACpB,KAAK,aAAa;QAChB,oBAAOlH,OAAA,CAACL,SAAS;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB,KAAK,aAAa;QAChB,oBAAOlH,OAAA,CAACH,eAAe;UAAAkH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5B,KAAK,OAAO;QACV,oBAAOlH,OAAA,CAACJ,MAAM;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnB;QACE,oBAAOlH,OAAA,CAACL,SAAS;UAAAoH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IACxB;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,QAAQ9G,YAAY;MAClB,KAAK,QAAQ;QACX,OAAO,OAAO;MAChB,KAAK,aAAa;QAChB,OAAO,YAAY;MACrB,KAAK,aAAa;QAChB,OAAO,YAAY;MACrB,KAAK,OAAO;QACV,OAAO,MAAM;MACf;QACE,OAAO,UAAU;IACrB;EACF,CAAC;EAED,oBACEL,OAAA;IAAK+C,SAAS,EAAC,mBAAmB;IAAAqE,QAAA,eAChCpH,OAAA;MAAK+C,SAAS,EAAC,WAAW;MAAAqE,QAAA,gBACxBpH,OAAA;QAAK+C,SAAS,EAAC,kBAAkB;QAAAqE,QAAA,GAC9BN,eAAe,CAAC,CAAC,eAClB9G,OAAA;UAAAoH,QAAA,GAAI,UAAQ,EAACD,gBAAgB,CAAC,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAENlH,OAAA,CAACvB,IAAI;QACHgC,IAAI,EAAEA,IAAK;QACX4G,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE/D,YAAa;QACvBgE,aAAa,EAAE;UACbpF,KAAK,EAAE,SAAS;UAChBY,SAAS,EAAE,EAAE;UACbF,OAAO,EAAE,EAAE;UACX2E,KAAK,EAAE,EAAE;UACTC,IAAI,EAAE,EAAE;UACRC,OAAO,EAAE,EAAE;UACXC,QAAQ,EAAE,EAAE;UACZC,YAAY,EAAE;QAChB,CAAE;QACF7E,SAAS,EAAC,eAAe;QAAAqE,QAAA,gBAEzBpH,OAAA;UAAK+C,SAAS,EAAC,UAAU;UAAAqE,QAAA,gBACvBpH,OAAA,CAACvB,IAAI,CAACoJ,IAAI;YACRC,KAAK,EAAC,OAAO;YACbjD,IAAI,EAAC,OAAO;YACZkD,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEtJ,OAAO,EAAE;YAAwB,CAAC,CAAE;YAC9DqE,SAAS,EAAC,gBAAgB;YAAAqE,QAAA,eAE1BpH,OAAA,CAACrB,MAAM;cACLsJ,WAAW,EAAC,cAAc;cAC1BC,QAAQ,EAAEvF,iBAAkB;cAC5BwF,IAAI,EAAC,OAAO;cAAAf,QAAA,gBAEZpH,OAAA,CAACG,MAAM;gBAACgD,KAAK,EAAC,SAAS;gBAAAiE,QAAA,EAAC;cAAO;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxClH,OAAA,CAACG,MAAM;gBAACgD,KAAK,EAAC,WAAW;gBAAAiE,QAAA,EAAC;cAAS;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC5ClH,OAAA,CAACG,MAAM;gBAACgD,KAAK,EAAC,SAAS;gBAAAiE,QAAA,EAAC;cAAO;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAEZlH,OAAA,CAACvB,IAAI,CAACoJ,IAAI;YACRC,KAAK,EAAC,OAAO;YACbjD,IAAI,EAAC,WAAW;YAChBkD,KAAK,EAAE,CAAC;cAAEC,QAAQ,EAAE,IAAI;cAAEtJ,OAAO,EAAE;YAAwB,CAAC,CAAE;YAC9DqE,SAAS,EAAC,gBAAgB;YAAAqE,QAAA,eAE1BpH,OAAA,CAACrB,MAAM;cACLsJ,WAAW,EAAC,cAAc;cAC1BE,IAAI,EAAC,OAAO;cACZD,QAAQ,EAAEhF,qBAAsB;cAAAkE,QAAA,EAE/B7E,gBAAgB,CAAC6F,GAAG,CAAC9E,GAAG,iBACvBtD,OAAA,CAACG,MAAM;gBAAWgD,KAAK,EAAEG,GAAI;gBAAA8D,QAAA,EAAE9D;cAAG,GAArBA,GAAG;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA2B,CAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eAENlH,OAAA,CAACvB,IAAI,CAACoJ,IAAI;UACRC,KAAK,EAAC,SAAS;UACfjD,IAAI,EAAC,SAAS;UACdkD,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtJ,OAAO,EAAE;UAA0B,CAAC,CAAE;UAAA0I,QAAA,eAEhEpH,OAAA,CAACrB,MAAM;YAACsJ,WAAW,EAAC,gBAAgB;YAACE,IAAI,EAAC,OAAO;YAAAf,QAAA,EAC9C/E,iBAAiB,CAAC+F,GAAG,CAACvF,OAAO,iBAC5B7C,OAAA,CAACG,MAAM;cAAegD,KAAK,EAAEN,OAAQ;cAAAuE,QAAA,EAAEvE;YAAO,GAAjCA,OAAO;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACxD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,EAEX7G,YAAY,KAAK,QAAQ,iBACxBL,OAAA,CAACvB,IAAI,CAACoJ,IAAI;UACRC,KAAK,EAAC,+BAA+B;UACrC/E,SAAS,EAAC,4BAA4B;UAAAqE,QAAA,gBAEtCpH,OAAA;YAAK+C,SAAS,EAAC,yBAAyB;YAAAqE,QAAA,eACtCpH,OAAA;cAAAoH,QAAA,EAAG;YAA4F;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChG,CAAC,eACNlH,OAAA,CAACrB,MAAM;YACL0J,IAAI,EAAC,UAAU;YACfJ,WAAW,EAAC,sCAAsC;YAClDE,IAAI,EAAC,OAAO;YACZhF,KAAK,EAAEV,yBAA0B;YACjCyF,QAAQ,EAAElF,6BAA8B;YACxCsF,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAnB,QAAA,EAExB7E,gBAAgB,CACdc,MAAM,CAACC,GAAG,IAAIA,GAAG,KAAK7C,IAAI,CAAC+H,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC;YAAA,CACvDJ,GAAG,CAAC9E,GAAG,iBACNtD,OAAA,CAACG,MAAM;cAAWgD,KAAK,EAAEG,GAAI;cAAA8D,QAAA,EAAE9D;YAAG,GAArBA,GAAG;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA2B,CAC5C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACTlH,OAAA;YAAK+C,SAAS,EAAC,yBAAyB;YAAAqE,QAAA,eACtCpH,OAAA;cAAAoH,QAAA,EAAO;YAAuF;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACZ,eAEDlH,OAAA,CAACvB,IAAI,CAACoJ,IAAI;UACRC,KAAK,EAAC,OAAO;UACbjD,IAAI,EAAC,OAAO;UACZkD,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtJ,OAAO,EAAE;UAAuB,CAAC,CAAE;UAAA0I,QAAA,eAE7DpH,OAAA;YACEyI,IAAI,EAAC,MAAM;YACXR,WAAW,EAAG,SAAQd,gBAAgB,CAAC,CAAC,CAACuB,WAAW,CAAC,CAAE,QAAQ;YAC/D3F,SAAS,EAAC;UAAY;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,EAEX,CAAC7G,YAAY,KAAK,aAAa,IAAIA,YAAY,KAAK,OAAO,kBAC1DL,OAAA,CAACvB,IAAI,CAACoJ,IAAI;UACRC,KAAK,EAAC,MAAM;UACZjD,IAAI,EAAC,MAAM;UACXkD,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEtJ,OAAO,EAAE;UAAwB,CAAC,CAAE;UAAA0I,QAAA,eAE9DpH,OAAA;YACEyI,IAAI,EAAC,MAAM;YACXR,WAAW,EAAC,yBAAyB;YACrClF,SAAS,EAAC;UAAY;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CACZ,EAEA7G,YAAY,KAAK,QAAQ,iBACxBL,OAAA,CAAAE,SAAA;UAAAkH,QAAA,gBACEpH,OAAA,CAACvB,IAAI,CAACoJ,IAAI;YACRC,KAAK,EAAC,eAAe;YACrB/E,SAAS,EAAC,uBAAuB;YAAAqE,QAAA,eAEjCpH,OAAA;cAAK+C,SAAS,EAAC,wBAAwB;cAAAqE,QAAA,gBACrCpH,OAAA;gBACE+C,SAAS,EAAG,iBAAgB3B,YAAY,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAG,EAAE;gBACzEuH,OAAO,EAAEA,CAAA,KAAMtH,eAAe,CAAC,SAAS,CAAE;gBAAA+F,QAAA,gBAE1CpH,OAAA,CAACN,OAAO;kBAACqD,SAAS,EAAC;gBAAa;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACnClH,OAAA;kBAAAoH,QAAA,EAAM;gBAAa;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1BlH,OAAA;kBAAAoH,QAAA,EAAG;gBAA0B;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNlH,OAAA;gBACE+C,SAAS,EAAG,iBAAgB3B,YAAY,KAAK,OAAO,GAAG,QAAQ,GAAG,EAAG,EAAE;gBACvEuH,OAAO,EAAEA,CAAA,KAAMtH,eAAe,CAAC,OAAO,CAAE;gBAAA+F,QAAA,gBAExCpH,OAAA,CAACF,gBAAgB;kBAACiD,SAAS,EAAC;gBAAa;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5ClH,OAAA;kBAAAoH,QAAA,EAAM;gBAAa;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1BlH,OAAA;kBAAAoH,QAAA,EAAG;gBAA6B;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACNlH,OAAA;gBACE+C,SAAS,EAAG,iBAAgB3B,YAAY,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAG,EAAE;gBACxEuH,OAAO,EAAEA,CAAA,KAAMtH,eAAe,CAAC,QAAQ,CAAE;gBAAA+F,QAAA,gBAEzCpH,OAAA,CAACF,gBAAgB;kBAACiD,SAAS,EAAC;gBAAa;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC5ClH,OAAA;kBAAAoH,QAAA,EAAM;gBAAiB;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9BlH,OAAA;kBAAAoH,QAAA,EAAG;gBAA2B;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,EAEX9F,YAAY,KAAK,SAAS,gBACzBpB,OAAA,CAAAE,SAAA;YAAAkH,QAAA,gBACEpH,OAAA,CAACvB,IAAI,CAACoJ,IAAI;cACRC,KAAK,EAAC,oBAAoB;cAC1BjD,IAAI,EAAC,SAAS;cACdkD,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE5G,YAAY,KAAK,SAAS;gBAAE1C,OAAO,EAAE;cAAgC,CAAC,CAAE;cAAA0I,QAAA,eAE5FpH,OAAA;gBACEyI,IAAI,EAAC,MAAM;gBACXR,WAAW,EAAC,4CAA4C;gBACxDlF,SAAS,EAAC;cAAY;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZlH,OAAA,CAACvB,IAAI,CAACoJ,IAAI;cACRC,KAAK,EAAC,sBAAsB;cAC5BjD,IAAI,EAAC,UAAU;cAAAuC,QAAA,eAEfpH,OAAA;gBACEyI,IAAI,EAAC,KAAK;gBACVR,WAAW,EAAC,4BAA4B;gBACxClF,SAAS,EAAC;cAAY;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZlH,OAAA,CAACvB,IAAI,CAACoJ,IAAI;cACRC,KAAK,EAAC,0BAA0B;cAChCjD,IAAI,EAAC,cAAc;cAAAuC,QAAA,eAEnBpH,OAAA;gBACEyI,IAAI,EAAC,KAAK;gBACVR,WAAW,EAAC,gCAAgC;gBAC5ClF,SAAS,EAAC;cAAY;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA,eACZ,CAAC,GACD9F,YAAY,KAAK,OAAO,gBAC1BpB,OAAA,CAAAE,SAAA;YAAAkH,QAAA,gBACEpH,OAAA,CAACvB,IAAI,CAACoJ,IAAI;cACRC,KAAK,EAAC,eAAe;cACrBjD,IAAI,EAAC,UAAU;cACfkD,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE5G,YAAY,KAAK,OAAO;gBAAE1C,OAAO,EAAE;cAA6B,CAAC,CAAE;cAAA0I,QAAA,eAEvFpH,OAAA;gBACEyI,IAAI,EAAC,KAAK;gBACVR,WAAW,EAAC,4EAA4E;gBACxFlF,SAAS,EAAC;cAAY;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZlH,OAAA,CAACvB,IAAI,CAACoJ,IAAI;cACRC,KAAK,EAAC,kCAAkC;cACxC/E,SAAS,EAAC,gBAAgB;cAAAqE,QAAA,eAE1BpH,OAAA,CAACpB,MAAM;gBAAA,GACDiI,oBAAoB;gBACxB/F,QAAQ,EAAEE,aAAc;gBACxBkH,QAAQ,EAAEA,CAAC;kBAAEpH;gBAAS,CAAC,KAAKG,gBAAgB,CAACH,QAAQ,CAAE;gBACvDiC,SAAS,EAAC,kBAAkB;gBAC5B6F,MAAM,EAAGC,CAAC,IAAK;kBACbA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClB7G,aAAa,CAAC,KAAK,CAAC;kBACpB,MAAM8G,KAAK,GAAGxE,KAAK,CAACyE,IAAI,CAACH,CAAC,CAACI,YAAY,CAACF,KAAK,CAAC;kBAC9C,MAAMG,UAAU,GAAGH,KAAK,CAAC1F,MAAM,CAAC8F,IAAI,IAAIA,IAAI,CAACV,IAAI,CAACW,UAAU,CAAC,QAAQ,CAAC,CAAC;kBACvE,IAAIF,UAAU,CAACnF,MAAM,GAAG,CAAC,EAAE;oBACzB,MAAMoF,IAAI,GAAGD,UAAU,CAAC,CAAC,CAAC;oBAC1B;oBACA,IAAIC,IAAI,CAAChB,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;sBAC/BzJ,OAAO,CAACoH,KAAK,CAAC,2CAA2C,CAAC;sBAC1D;oBACF;oBACA7E,gBAAgB,CAAC,CAAC;sBAChBoI,GAAG,EAAE,IAAI;sBACTxE,IAAI,EAAEsE,IAAI,CAACtE,IAAI;sBACfY,MAAM,EAAE,MAAM;sBACdzB,aAAa,EAAEmF,IAAI;sBACnBG,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;oBAC/B,CAAC,CAAC,CAAC;oBACHzK,OAAO,CAACiF,OAAO,CAAC,kCAAkC,CAAC;kBACrD,CAAC,MAAM;oBACLjF,OAAO,CAACoH,KAAK,CAAC,2CAA2C,CAAC;kBAC5D;gBACF,CAAE;gBACF2D,UAAU,EAAGZ,CAAC,IAAK;kBACjBA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClB7G,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBACFyH,WAAW,EAAGb,CAAC,IAAK;kBAClBA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClB7G,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBACF0H,WAAW,EAAGd,CAAC,IAAK;kBAClBA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClB7G,aAAa,CAAC,KAAK,CAAC;gBACtB,CAAE;gBAAAmF,QAAA,eAEFpH,OAAA;kBAAK+C,SAAS,EAAG,yCAAwCf,UAAU,GAAG,WAAW,GAAG,EAAG,EAAE;kBAAAoF,QAAA,gBACvFpH,OAAA,CAACP,QAAQ;oBAACsD,SAAS,EAAC;kBAAa;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpClH,OAAA;oBAAAoH,QAAA,EAAIpF,UAAU,GAAG,sBAAsB,GAAG;kBAA0C;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzFlH,OAAA;oBAAG+C,SAAS,EAAC,aAAa;oBAAAqE,QAAA,EAAC;kBAA8B;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC7DlH,OAAA;oBAAG+C,SAAS,EAAC,aAAa;oBAAAqE,QAAA,EAAC;kBAAiC;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,eACZ,CAAC,gBAEHlH,OAAA,CAAAE,SAAA;YAAAkH,QAAA,gBACEpH,OAAA,CAACvB,IAAI,CAACoJ,IAAI;cACRC,KAAK,EAAC,mBAAmB;cACzB/E,SAAS,EAAC,gBAAgB;cAAAqE,QAAA,eAE1BpH,OAAA,CAACpB,MAAM;gBAAA,GACDgI,gBAAgB;gBACpB9F,QAAQ,EAAEI,aAAc;gBACxBgH,QAAQ,EAAEA,CAAC;kBAAEpH;gBAAS,CAAC,KAAKK,gBAAgB,CAACL,QAAQ,CAAE;gBACvDiC,SAAS,EAAC,cAAc;gBAAAqE,QAAA,eAExBpH,OAAA;kBAAK+C,SAAS,EAAC,aAAa;kBAAAqE,QAAA,gBAC1BpH,OAAA,CAACF,gBAAgB;oBAACiD,SAAS,EAAC;kBAAa;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5ClH,OAAA;oBAAAoH,QAAA,EAAG;kBAAkC;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACzClH,OAAA;oBAAG+C,SAAS,EAAC,aAAa;oBAAAqE,QAAA,EAAC;kBAAwC;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eACvElH,OAAA;oBAAG+C,SAAS,EAAC,aAAa;oBAAAqE,QAAA,EAAC;kBAA8C;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAEZlH,OAAA,CAACvB,IAAI,CAACoJ,IAAI;cACRC,KAAK,EAAC,oCAAoC;cAC1C/E,SAAS,EAAC,gBAAgB;cAAAqE,QAAA,eAE1BpH,OAAA,CAACpB,MAAM;gBAAA,GACDiI,oBAAoB;gBACxB/F,QAAQ,EAAEE,aAAc;gBACxBkH,QAAQ,EAAEA,CAAC;kBAAEpH;gBAAS,CAAC,KAAKG,gBAAgB,CAACH,QAAQ,CAAE;gBACvDiC,SAAS,EAAC,kBAAkB;gBAC5B6F,MAAM,EAAGC,CAAC,IAAK;kBACbA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClB7G,aAAa,CAAC,KAAK,CAAC;kBACpB,MAAM8G,KAAK,GAAGxE,KAAK,CAACyE,IAAI,CAACH,CAAC,CAACI,YAAY,CAACF,KAAK,CAAC;kBAC9C,MAAMG,UAAU,GAAGH,KAAK,CAAC1F,MAAM,CAAC8F,IAAI,IAAIA,IAAI,CAACV,IAAI,CAACW,UAAU,CAAC,QAAQ,CAAC,CAAC;kBACvE,IAAIF,UAAU,CAACnF,MAAM,GAAG,CAAC,EAAE;oBACzB,MAAMoF,IAAI,GAAGD,UAAU,CAAC,CAAC,CAAC;oBAC1B;oBACA,IAAIC,IAAI,CAAChB,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;sBAC/BzJ,OAAO,CAACoH,KAAK,CAAC,2CAA2C,CAAC;sBAC1D;oBACF;oBACA7E,gBAAgB,CAAC,CAAC;sBAChBoI,GAAG,EAAE,IAAI;sBACTxE,IAAI,EAAEsE,IAAI,CAACtE,IAAI;sBACfY,MAAM,EAAE,MAAM;sBACdzB,aAAa,EAAEmF,IAAI;sBACnBG,GAAG,EAAEC,GAAG,CAACC,eAAe,CAACL,IAAI;oBAC/B,CAAC,CAAC,CAAC;oBACHzK,OAAO,CAACiF,OAAO,CAAC,kCAAkC,CAAC;kBACrD,CAAC,MAAM;oBACLjF,OAAO,CAACoH,KAAK,CAAC,2CAA2C,CAAC;kBAC5D;gBACF,CAAE;gBACF2D,UAAU,EAAGZ,CAAC,IAAK;kBACjBA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClB7G,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBACFyH,WAAW,EAAGb,CAAC,IAAK;kBAClBA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClB7G,aAAa,CAAC,IAAI,CAAC;gBACrB,CAAE;gBACF0H,WAAW,EAAGd,CAAC,IAAK;kBAClBA,CAAC,CAACC,cAAc,CAAC,CAAC;kBAClB7G,aAAa,CAAC,KAAK,CAAC;gBACtB,CAAE;gBAAAmF,QAAA,eAEFpH,OAAA;kBAAK+C,SAAS,EAAG,yCAAwCf,UAAU,GAAG,WAAW,GAAG,EAAG,EAAE;kBAAAoF,QAAA,gBACvFpH,OAAA,CAACP,QAAQ;oBAACsD,SAAS,EAAC;kBAAa;oBAAAgE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpClH,OAAA;oBAAAoH,QAAA,EAAIpF,UAAU,GAAG,sBAAsB,GAAG;kBAA0C;oBAAA+E,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzFlH,OAAA;oBAAG+C,SAAS,EAAC,aAAa;oBAAAqE,QAAA,EAAC;kBAA8B;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC7DlH,OAAA;oBAAG+C,SAAS,EAAC,aAAa;oBAAAqE,QAAA,EAAC;kBAAiC;oBAAAL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,eACZ,CACH;QAAA,eACD,CACH,EAEA7G,YAAY,KAAK,QAAQ,iBACxBL,OAAA,CAACvB,IAAI,CAACoJ,IAAI;UACRC,KAAK,EAAG,UAASX,gBAAgB,CAAC,CAAE,WAAW;UAC/CpE,SAAS,EAAC,gBAAgB;UAAAqE,QAAA,eAE1BpH,OAAA,CAACpB,MAAM;YAAA,GACD4H,WAAW;YACf1F,QAAQ,EAAEA,QAAS;YACnBoH,QAAQ,EAAEA,CAAC;cAAEpH;YAAS,CAAC,KAAKC,WAAW,CAACD,QAAQ,CAAE;YAClDiC,SAAS,EAAC,iBAAiB;YAAAqE,QAAA,eAE3BpH,OAAA;cAAK+C,SAAS,EAAC,aAAa;cAAAqE,QAAA,gBAC1BpH,OAAA,CAACF,gBAAgB;gBAACiD,SAAS,EAAC;cAAa;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC5ClH,OAAA;gBAAAoH,QAAA,EAAG;cAA4B;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnClH,OAAA;gBAAG+C,SAAS,EAAC,aAAa;gBAAAqE,QAAA,EAAC;cAAkC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACZ,EAEA7G,YAAY,KAAK,OAAO,iBACvBL,OAAA,CAACvB,IAAI,CAACoJ,IAAI;UACRC,KAAK,EAAC,6BAA6B;UACnC/E,SAAS,EAAC,gBAAgB;UAAAqE,QAAA,eAE1BpH,OAAA,CAACpB,MAAM;YAAA,GACDiI,oBAAoB;YACxB/F,QAAQ,EAAEE,aAAc;YACxBkH,QAAQ,EAAEA,CAAC;cAAEpH;YAAS,CAAC,KAAKG,gBAAgB,CAACH,QAAQ,CAAE;YACvDiC,SAAS,EAAC,kBAAkB;YAAAqE,QAAA,eAE5BpH,OAAA;cAAK+C,SAAS,EAAC,mBAAmB;cAAAqE,QAAA,gBAChCpH,OAAA,CAACP,QAAQ;gBAACsD,SAAS,EAAC;cAAa;gBAAAgE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACpClH,OAAA;gBAAAoH,QAAA,EAAG;cAAiB;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACZ,EAGAtG,OAAO,IAAIQ,YAAY,KAAK,QAAQ,IAAIf,YAAY,KAAK,QAAQ,iBAChEL,OAAA;UAAK+C,SAAS,EAAC,yBAAyB;UAAAqE,QAAA,gBACtCpH,OAAA;YAAK+C,SAAS,EAAC,iBAAiB;YAAAqE,QAAA,gBAC9BpH,OAAA;cAAK+C,SAAS,EAAC,eAAe;cAAAqE,QAAA,gBAC5BpH,OAAA;gBAAM+C,SAAS,EAAC,eAAe;gBAAAqE,QAAA,EAAE5F;cAAY;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrDlH,OAAA;gBAAM+C,SAAS,EAAC,qBAAqB;gBAAAqE,QAAA,GAAE9F,cAAc,EAAC,GAAC;cAAA;gBAAAyF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,EACLxF,WAAW,GAAG,CAAC,iBACd1B,OAAA;cAAK+C,SAAS,EAAC,cAAc;cAAAqE,QAAA,gBAC3BpH,OAAA;gBAAM+C,SAAS,EAAC,cAAc;gBAAAqE,QAAA,GAAC,eAC1B,EAAC,CAAC1F,WAAW,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEkI,OAAO,CAAC,CAAC,CAAC,EAAC,OAC/C;cAAA;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EACNtF,aAAa,GAAG,CAAC,IAAIA,aAAa,GAAG,IAAI,iBACxC5B,OAAA;gBAAM+C,SAAS,EAAC,gBAAgB;gBAAAqE,QAAA,GAAC,eAC5B,EAACyC,IAAI,CAACC,IAAI,CAAClI,aAAa,CAAC,EAAC,aAC/B;cAAA;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENlH,OAAA;YAAK+C,SAAS,EAAC,cAAc;YAAAqE,QAAA,eAC3BpH,OAAA;cACE+C,SAAS,EAAC,eAAe;cACzBuF,KAAK,EAAE;gBACLC,KAAK,EAAG,GAAEjH,cAAe,GAAE;gBAC3ByI,UAAU,EAAE;cACd;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENlH,OAAA;YAAK+C,SAAS,EAAC,kBAAkB;YAAAqE,QAAA,EAC9B9F,cAAc,GAAG,GAAG,gBACnBtB,OAAA;cAAK+C,SAAS,EAAC,gBAAgB;cAAAqE,QAAA,gBAC7BpH,OAAA;gBAAAoH,QAAA,EAAM;cAAoC;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjDlH,OAAA;gBAAAoH,QAAA,EAAO;cAAgD;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC,gBAENlH,OAAA;cAAK+C,SAAS,EAAC,iBAAiB;cAAAqE,QAAA,gBAC9BpH,OAAA;gBAAAoH,QAAA,EAAM;cAAgE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7ElH,OAAA;gBAAAoH,QAAA,EAAO;cAA2C;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDlH,OAAA;UAAK+C,SAAS,EAAC,cAAc;UAAAqE,QAAA,gBAC3BpH,OAAA,CAACnB,MAAM;YACL4J,IAAI,EAAC,SAAS;YACdE,OAAO,EAAEpI,QAAS;YAClB4H,IAAI,EAAC,OAAO;YACZpF,SAAS,EAAC,YAAY;YACtBiH,QAAQ,EAAEpJ,OAAQ;YAAAwG,QAAA,EACnB;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTlH,OAAA,CAACnB,MAAM;YACL4J,IAAI,EAAC,SAAS;YACdwB,QAAQ,EAAC,QAAQ;YACjBrJ,OAAO,EAAEA,OAAQ;YACjBuH,IAAI,EAAC,OAAO;YACZpF,SAAS,EAAC,YAAY;YAAAqE,QAAA,EAErBxG,OAAO,GACNQ,YAAY,KAAK,QAAQ,IAAIf,YAAY,KAAK,QAAQ,GACtD,cAAc,GAAG,WAAW,GAE3B,OAAM8G,gBAAgB,CAAC,CAAE;UAC3B;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC1G,EAAA,CA1wBQJ,oBAAoB;EAAA,QACZ3B,IAAI,CAACiC,OAAO,EACV5B,WAAW;AAAA;AAAAoL,EAAA,GAFrB9J,oBAAoB;AA4wB7B,eAAeA,oBAAoB;AAAC,IAAA8J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}