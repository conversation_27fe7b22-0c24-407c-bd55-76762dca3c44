@echo off
title BRAINWAVE STARTUP - WORKING VERSION
color 0A

echo.
echo ========================================
echo    🧠 BRAINWAVE STARTUP - FIXED 🧠
echo ========================================
echo.

REM Kill existing processes
echo [1/5] 🔄 Clearing existing processes...
taskkill /F /IM node.exe >nul 2>&1
echo ✓ Cleared

echo [2/5] 🔧 Testing Database...
cd /d "%~dp0server"
node -e "require('dotenv').config(); const mongoose = require('mongoose'); console.log('Testing DB...'); mongoose.connect(process.env.MONGO_URL, { bufferCommands: false }).then(() => { console.log('✅ DB Connected'); process.exit(0); }).catch(err => { console.error('❌ DB Failed:', err.message); process.exit(1); });"
echo ✓ Database OK

echo [3/5] 🚀 Starting Server...
start "SERVER - Port 5000" cmd /k "cd /d \"%~dp0server\" && echo ===== BRAINWAVE SERVER ===== && echo Starting on port 5000... && node server.js"
timeout /t 8 /nobreak >nul

echo [4/5] ⚛️ Starting Client...
start "CLIENT - Port 3000" cmd /k "cd /d \"%~dp0client\" && echo ===== BRAINWAVE CLIENT ===== && echo Starting on port 3000... && set BROWSER=none && npm start"
timeout /t 15 /nobreak >nul

echo [5/5] 🌐 Testing & Opening...
curl -s http://localhost:5000/api/health
echo.
echo ✅ EVERYTHING IS RUNNING!
echo.
echo 🔗 FRONTEND: http://localhost:3000
echo 🔗 BACKEND:  http://localhost:5000
echo.
start http://localhost:3000
echo Browser opened!
echo.
pause
