// Debug the specific endpoint that's timing out
const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function debugEndpointTimeout() {
  try {
    console.log('🔍 Debugging Endpoint Timeout Issue...');
    
    // Step 1: Login to get token
    console.log('\n1️⃣ Getting admin token...');
    const loginResponse = await axios.post(`${BASE_URL}/users/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    }, { timeout: 5000 });
    
    if (!loginResponse.data.success) {
      console.log('❌ Login failed:', loginResponse.data.message);
      return;
    }
    
    const token = loginResponse.data.data;
    console.log('✅ Token obtained');
    
    // Step 2: Test the exact endpoint with minimal data
    console.log('\n2️⃣ Testing add-video endpoint with minimal data...');
    
    const minimalVideoData = {
      className: '1',
      subject: 'Mathematics',
      title: 'Debug Test Video',
      level: 'primary',
      videoID: 'dQw4w9WgXcQ'
    };
    
    console.log('📤 Sending request...');
    const startTime = Date.now();
    
    try {
      const response = await axios.post(`${BASE_URL}/study/add-video`, minimalVideoData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000, // 10 second timeout
        onUploadProgress: (progressEvent) => {
          console.log('📊 Upload progress:', Math.round((progressEvent.loaded * 100) / progressEvent.total) + '%');
        }
      });
      
      const endTime = Date.now();
      console.log(`✅ Request completed in ${endTime - startTime}ms`);
      console.log('📋 Response:', response.data);
      
    } catch (uploadError) {
      const endTime = Date.now();
      console.log(`❌ Request failed after ${endTime - startTime}ms`);
      
      if (uploadError.code === 'ECONNABORTED') {
        console.log('⏰ TIMEOUT CONFIRMED - Request was aborted');
        console.log('💡 This means the server is not responding within 10 seconds');
      } else if (uploadError.response) {
        console.log('📋 Server responded with error:', uploadError.response.status);
        console.log('📋 Error data:', uploadError.response.data);
      } else {
        console.log('📋 Network error:', uploadError.message);
      }
    }
    
    // Step 3: Test a simpler endpoint to see if it's server-wide
    console.log('\n3️⃣ Testing simpler endpoint for comparison...');
    try {
      const simpleResponse = await axios.post(`${BASE_URL}/study/get-study-content`, {
        content: 'videos',
        className: 'all',
        subject: 'all'
      }, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 5000
      });
      
      console.log('✅ Simple endpoint works fine');
      console.log('📊 Found videos:', simpleResponse.data.data?.length || 0);
      
    } catch (simpleError) {
      console.log('❌ Simple endpoint also fails:', simpleError.message);
    }
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugEndpointTimeout();
