// Diagnose the exact database write issue
const mongoose = require('mongoose');
const Videos = require('./server/models/studyVideos');
require('dotenv').config({ path: './server/.env' });

async function diagnoseDatabaseWrite() {
  try {
    console.log('🔍 Diagnosing Database Write Issue...');
    
    // Test 1: Basic connection
    console.log('\n1️⃣ Testing database connection...');
    
    const options = {
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 10000,
      connectTimeoutMS: 5000,
      family: 4,
      maxPoolSize: 3,
      minPoolSize: 1,
      retryWrites: true
    };
    
    await mongoose.connect(process.env.MONGO_URL, options);
    console.log('✅ Database connection successful');
    
    // Test 2: Simple read operation
    console.log('\n2️⃣ Testing read operation...');
    const videoCount = await Videos.countDocuments().maxTimeMS(5000);
    console.log(`✅ Read operation successful - Found ${videoCount} videos`);
    
    // Test 3: Simple write operation
    console.log('\n3️⃣ Testing write operation...');
    
    const testVideo = {
      className: '1',
      subject: 'Mathematics',
      title: `Test Video ${Date.now()}`,
      level: 'primary',
      videoID: 'dQw4w9WgXcQ',
      videoUrl: '',
      thumbnail: ''
    };
    
    console.log('📝 Creating test video...');
    const startTime = Date.now();
    
    // Set a timeout for the save operation
    const savePromise = Videos.create(testVideo);
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Save operation timed out')), 10000);
    });
    
    const savedVideo = await Promise.race([savePromise, timeoutPromise]);
    const endTime = Date.now();
    
    console.log(`✅ Write operation successful in ${endTime - startTime}ms`);
    console.log('📊 Saved video ID:', savedVideo._id);
    
    // Test 4: Cleanup - delete the test video
    console.log('\n4️⃣ Testing delete operation...');
    await Videos.findByIdAndDelete(savedVideo._id);
    console.log('✅ Delete operation successful');
    
    console.log('\n🎉 All database operations working correctly!');
    console.log('💡 The issue might be in the API endpoint logic, not the database.');
    
  } catch (error) {
    console.error('❌ Database operation failed:', error.message);
    
    if (error.message.includes('timed out')) {
      console.log('\n⏰ TIMEOUT ISSUE CONFIRMED');
      console.log('💡 Database write operations are hanging');
      console.log('🔧 Possible solutions:');
      console.log('1. Check MongoDB Atlas network connectivity');
      console.log('2. Verify database user permissions');
      console.log('3. Check if database is full or has issues');
      console.log('4. Try using a local MongoDB instance');
    }
    
    if (error.message.includes('authentication')) {
      console.log('\n🔐 AUTHENTICATION ISSUE');
      console.log('💡 Database credentials may be incorrect');
    }
    
    if (error.message.includes('network')) {
      console.log('\n🌐 NETWORK ISSUE');
      console.log('💡 Cannot reach MongoDB Atlas servers');
    }
    
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
    process.exit(0);
  }
}

diagnoseDatabaseWrite();
