{"ast": null, "code": "const {\n  default: axiosInstance\n} = require(\".\");\n\n// add question\nexport const addQuestion = async payload => {\n  try {\n    const response = await axiosInstance.post(\"/api/forum/add-question\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// add reply\nexport const addReply = async payload => {\n  try {\n    const response = await axiosInstance.post(\"/api/forum/add-reply\", payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// get all questions\nexport const getAllQuestions = async (params = {}) => {\n  try {\n    const {\n      page = 1,\n      limit = 50\n    } = params;\n    const response = await axiosInstance.get(`/api/forum/get-all-questions?page=${page}&limit=${limit}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// delete question\nexport const deleteQuestion = async questionId => {\n  try {\n    const response = await axiosInstance.delete(`/api/forum/delete-question/${questionId}`);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// delete question\nexport const updateQuestion = async (payload, questionId) => {\n  try {\n    const response = await axiosInstance.put(`/api/forum/update-question/${questionId}`, payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};\n\n// approve reply\nexport const updateReplyStatus = async (payload, questionId) => {\n  try {\n    const response = await axiosInstance.put(`/api/forum/update-reply-status/${questionId}`, payload);\n    return response.data;\n  } catch (error) {\n    return error.response.data;\n  }\n};", "map": {"version": 3, "names": ["default", "axiosInstance", "require", "addQuestion", "payload", "response", "post", "data", "error", "addReply", "getAllQuestions", "params", "page", "limit", "get", "deleteQuestion", "questionId", "delete", "updateQuestion", "put", "updateReplyStatus"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/apicalls/forum.js"], "sourcesContent": ["const { default: axiosInstance } = require(\".\");\r\n\r\n// add question\r\nexport const addQuestion = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/forum/add-question\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// add reply\r\nexport const addReply = async (payload) => {\r\n    try {\r\n        const response = await axiosInstance.post(\"/api/forum/add-reply\", payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// get all questions\r\nexport const getAllQuestions = async (params = {}) => {\r\n    try {\r\n        const { page = 1, limit = 50 } = params;\r\n        const response = await axiosInstance.get(`/api/forum/get-all-questions?page=${page}&limit=${limit}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// delete question\r\nexport const deleteQuestion = async (questionId) => {\r\n    try {\r\n        const response = await axiosInstance.delete(`/api/forum/delete-question/${questionId}`);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// delete question\r\nexport const updateQuestion = async (payload, questionId) => {\r\n    try {\r\n        const response = await axiosInstance.put(`/api/forum/update-question/${questionId}`, payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n// approve reply\r\nexport const updateReplyStatus = async (payload, questionId) => {\r\n    try {\r\n        const response = await axiosInstance.put(`/api/forum/update-reply-status/${questionId}`, payload);\r\n        return response.data;\r\n    } catch (error) {\r\n        return error.response.data;\r\n    }\r\n}\r\n\r\n\r\n\r\n"], "mappings": "AAAA,MAAM;EAAEA,OAAO,EAAEC;AAAc,CAAC,GAAGC,OAAO,CAAC,GAAG,CAAC;;AAE/C;AACA,OAAO,MAAMC,WAAW,GAAG,MAAOC,OAAO,IAAK;EAC1C,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,yBAAyB,EAAEF,OAAO,CAAC;IAC7E,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;;AAED;AACA,OAAO,MAAME,QAAQ,GAAG,MAAOL,OAAO,IAAK;EACvC,IAAI;IACA,MAAMC,QAAQ,GAAG,MAAMJ,aAAa,CAACK,IAAI,CAAC,sBAAsB,EAAEF,OAAO,CAAC;IAC1E,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;;AAED;AACA,OAAO,MAAMG,eAAe,GAAG,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;EAClD,IAAI;IACA,MAAM;MAAEC,IAAI,GAAG,CAAC;MAAEC,KAAK,GAAG;IAAG,CAAC,GAAGF,MAAM;IACvC,MAAMN,QAAQ,GAAG,MAAMJ,aAAa,CAACa,GAAG,CAAE,qCAAoCF,IAAK,UAASC,KAAM,EAAC,CAAC;IACpG,OAAOR,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;;AAED;AACA,OAAO,MAAMQ,cAAc,GAAG,MAAOC,UAAU,IAAK;EAChD,IAAI;IACA,MAAMX,QAAQ,GAAG,MAAMJ,aAAa,CAACgB,MAAM,CAAE,8BAA6BD,UAAW,EAAC,CAAC;IACvF,OAAOX,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;;AAED;AACA,OAAO,MAAMW,cAAc,GAAG,MAAAA,CAAOd,OAAO,EAAEY,UAAU,KAAK;EACzD,IAAI;IACA,MAAMX,QAAQ,GAAG,MAAMJ,aAAa,CAACkB,GAAG,CAAE,8BAA6BH,UAAW,EAAC,EAAEZ,OAAO,CAAC;IAC7F,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC;;AAED;AACA,OAAO,MAAMa,iBAAiB,GAAG,MAAAA,CAAOhB,OAAO,EAAEY,UAAU,KAAK;EAC5D,IAAI;IACA,MAAMX,QAAQ,GAAG,MAAMJ,aAAa,CAACkB,GAAG,CAAE,kCAAiCH,UAAW,EAAC,EAAEZ,OAAO,CAAC;IACjG,OAAOC,QAAQ,CAACE,IAAI;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAOA,KAAK,CAACH,QAAQ,CAACE,IAAI;EAC9B;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}