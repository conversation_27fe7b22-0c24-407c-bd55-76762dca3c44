// Server startup that handles slow database operations
const express = require('express');
const cors = require('cors');
const mongoose = require('mongoose');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

console.log('🚀 Starting BrainWave Server with Slow DB Handling...');

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
  credentials: true
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Health check endpoint (works without database)
app.get('/api/health', (req, res) => {
  res.json({
    status: 'success',
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    port: PORT,
    environment: process.env.NODE_ENV || 'development',
    database: mongoose.connection.readyState === 1 ? 'connected' : 'connecting'
  });
});

// Basic route
app.get('/', (req, res) => {
  res.send('Server is Up!');
});

// Database connection with slow operation handling
async function connectDatabase() {
  try {
    console.log('🔗 Connecting to MongoDB Atlas...');
    
    // Optimized options for slow networks
    const options = {
      serverSelectionTimeoutMS: 20000, // 20 seconds
      socketTimeoutMS: 45000, // 45 seconds  
      connectTimeoutMS: 20000, // 20 seconds
      family: 4,
      maxPoolSize: 3,
      minPoolSize: 1,
      retryWrites: true,
      retryReads: true,
      bufferCommands: false, // Important: don't buffer commands
      bufferMaxEntries: 0
    };
    
    await mongoose.connect(process.env.MONGO_URL, options);
    console.log('✅ MongoDB Atlas connected successfully!');
    
    // Test database operations with timeout
    setTimeout(async () => {
      try {
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log(`✅ Database operations working - ${collections.length} collections`);
      } catch (error) {
        console.log('⚠️ Database operations slow but connection established');
      }
    }, 5000);
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.log('⚠️ Server will continue without database features');
  }
}

// Start server first, then connect database
app.listen(PORT, async () => {
  console.log(`✅ Server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  
  // Connect to database after server is running
  await connectDatabase();
  
  // Load routes after database connection attempt
  setTimeout(() => {
    try {
      // Import and use routes
      const userRoutes = require('./routes/userRoute');
      const examRoutes = require('./routes/examRoute');
      const reportRoutes = require('./routes/reportRoute');
      const studyRoutes = require('./routes/studyRoute');
      
      app.use('/api/users', userRoutes);
      app.use('/api/exams', examRoutes);
      app.use('/api/reports', reportRoutes);
      app.use('/api/study', studyRoutes);
      
      console.log('✅ API routes loaded successfully');
      console.log('🎉 Server fully operational!');
      console.log('');
      console.log('🌐 Available endpoints:');
      console.log('   - Health: http://localhost:5000/api/health');
      console.log('   - Users: http://localhost:5000/api/users');
      console.log('   - Study: http://localhost:5000/api/study');
      console.log('');
      console.log('💡 If login is slow, wait for database operations to stabilize');
      
    } catch (routeError) {
      console.error('❌ Error loading routes:', routeError.message);
      console.log('⚠️ Server running with limited functionality');
    }
  }, 3000);
});

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down server...');
  
  if (mongoose.connection.readyState === 1) {
    await mongoose.disconnect();
    console.log('🔌 Database disconnected');
  }
  
  process.exit(0);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled Promise Rejection:', err.message);
  console.log('⚠️ Server continuing to run...');
});

console.log('📍 Server initialization complete');
console.log('⏳ Waiting for database connection...');
