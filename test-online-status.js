const axios = require('axios');

const baseURL = 'http://localhost:5000';

async function testOnlineStatus() {
    console.log('🧪 Testing Online Status API...\n');

    try {
        // Test 1: Check if server is running
        console.log('1️⃣ Testing server health...');
        const healthResponse = await axios.get(`${baseURL}/api/health`);
        console.log('✅ Server is running:', healthResponse.data.message);

        // Test 2: Try to get online status (without auth - should fail gracefully)
        console.log('\n2️⃣ Testing online status API endpoint...');
        try {
            const statusResponse = await axios.get(`${baseURL}/api/notifications/status/test-user-id`);
            console.log('📊 Online Status API Response:', statusResponse.data);
        } catch (error) {
            if (error.response?.status === 401) {
                console.log('🔒 Online Status API requires authentication (expected)');
            } else {
                console.log('❌ Online Status API error:', error.message);
            }
        }

        console.log('\n🎉 Online status test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
}

testOnlineStatus();
