{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\common\\\\Forum\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from \"react\";\nimport './index.css';\nimport { getUserInfo } from \"../../../apicalls/users\";\nimport { message, Button, Input, Form, Avatar, Badge } from \"antd\";\nimport { useDispatch } from \"react-redux\";\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\nimport image from '../../../assets/person.png';\nimport { PlusOutlined, MessageOutlined, UserOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Forum = () => {\n  _s();\n  const [isAdmin, setIsAdmin] = useState(false);\n  const [userData, setUserData] = useState('');\n  const [questions, setQuestions] = useState([]);\n  const [askQuestionVisible, setAskQuestionVisible] = useState(false);\n  const [replyQuestionId, setReplyQuestionId] = useState(null);\n  const [form] = Form.useForm();\n  const dispatch = useDispatch();\n  const [replyRefs, setReplyRefs] = useState({});\n\n  // Pagination states\n  const [currentPage, setCurrentPage] = useState(1);\n  const [questionsPerPage] = useState(5);\n  const [totalQuestions, setTotalQuestions] = useState(0);\n  const fetchQuestions = async (page = currentPage) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getAllQuestions({\n        page,\n        limit: questionsPerPage\n      });\n      if (response.success) {\n        // Sort by creation date (newest first) instead of reversing\n        const sortedQuestions = response.data.sort((a, b) => new Date(b.createdAt || b._id) - new Date(a.createdAt || a._id));\n        setQuestions(sortedQuestions);\n        setTotalQuestions(response.total || response.totalQuestions || response.data.length);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const getUserData = async () => {\n    try {\n      const response = await getUserInfo();\n      if (response.success) {\n        if (response.data.isAdmin) {\n          setIsAdmin(true);\n        } else {\n          setIsAdmin(false);\n          setUserData(response.data);\n          await fetchQuestions();\n        }\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n    dispatch(HideLoading());\n  };\n  useEffect(() => {\n    if (localStorage.getItem(\"token\")) {\n      dispatch(ShowLoading());\n      getUserData();\n    }\n  }, []);\n  const handleAskQuestion = async values => {\n    try {\n      dispatch(ShowLoading());\n      const response = await addQuestion(values);\n      if (response.success) {\n        message.success(response.message);\n        setAskQuestionVisible(false);\n        form.resetFields();\n        // Reset to first page to see the new question\n        setCurrentPage(1);\n        await fetchQuestions(1);\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    } finally {\n      dispatch(HideLoading());\n    }\n  };\n  const handleReply = questionId => {\n    setReplyQuestionId(questionId);\n  };\n  const handleReplySubmit = async values => {\n    try {\n      const payload = {\n        questionId: replyQuestionId,\n        text: values.text\n      };\n      const response = await addReply(payload);\n      if (response.success) {\n        message.success(response.message);\n        setReplyQuestionId(null);\n        form.resetFields();\n        await fetchQuestions();\n      } else {\n        message.error(response.message);\n      }\n    } catch (error) {\n      message.error(error.message);\n    }\n  };\n  useEffect(() => {\n    if (replyQuestionId && !replyRefs[replyQuestionId]) {\n      setReplyRefs(prevRefs => ({\n        ...prevRefs,\n        [replyQuestionId]: /*#__PURE__*/React.createRef()\n      }));\n    }\n  }, [replyQuestionId, replyRefs]);\n  useEffect(() => {\n    if (replyQuestionId && replyRefs[replyQuestionId]) {\n      replyRefs[replyQuestionId].current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [replyQuestionId, replyRefs]);\n\n  // Pagination calculations - use all questions since we're fetching by page\n  const totalPages = Math.ceil(totalQuestions / questionsPerPage);\n  const startItem = (currentPage - 1) * questionsPerPage + 1;\n  const endItem = Math.min(currentPage * questionsPerPage, totalQuestions);\n  const handlePageChange = page => {\n    setCurrentPage(page);\n    fetchQuestions(page);\n  };\n\n  // Format date and time\n  const formatDateTime = dateString => {\n    if (!dateString) return 'Just now';\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInHours = (now - date) / (1000 * 60 * 60);\n    if (diffInHours < 24) {\n      // Show time if less than 24 hours\n      return date.toLocaleTimeString('en-US', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n      });\n    } else {\n      // Show date if more than 24 hours\n      return date.toLocaleDateString('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined\n      });\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: !isAdmin && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modern-forum\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"forum-header\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"forum-header-content\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"forum-title\",\n            children: \"Community Forum\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"forum-description\",\n            children: \"Connect with fellow learners, ask questions, and share knowledge. Join our vibrant community discussion!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"header-ask-btn\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 43\n              }, this),\n              onClick: () => setAskQuestionVisible(true),\n              className: \"ask-question-header\",\n              size: \"large\",\n              children: \"Ask Question\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 21\n      }, this), askQuestionVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ask-question-modal\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ask-question-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"form-title\",\n            children: \"Ask a Question\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            onFinish: handleAskQuestion,\n            layout: \"vertical\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"Question Title\",\n              rules: [{\n                required: true,\n                message: 'Please enter the title'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"What's your question about?\",\n                className: \"modern-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"body\",\n              label: \"Question Details\",\n              rules: [{\n                required: true,\n                message: 'Please enter the question details'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                rows: 4,\n                placeholder: \"Provide more details about your question...\",\n                className: \"modern-textarea\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              className: \"form-actions\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                className: \"submit-btn\",\n                children: \"Post Question\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => setAskQuestionVisible(false),\n                className: \"cancel-btn\",\n                children: \"Cancel\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 25\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"questions-container\",\n        children: questions.map(question => {\n          var _question$user, _question$user2, _question$replies, _question$replies2;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"modern-question-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"question-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"user-info\",\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  src: (_question$user = question.user) !== null && _question$user !== void 0 && _question$user.profileImage ? question.user.profileImage : image,\n                  alt: \"profile\",\n                  size: 48,\n                  icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 51\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"user-details\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"username\",\n                    children: ((_question$user2 = question.user) === null || _question$user2 === void 0 ? void 0 : _question$user2.name) || 'Anonymous'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 45\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"question-datetime\",\n                    children: formatDateTime(question.createdAt)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(Badge, {\n                count: ((_question$replies = question.replies) === null || _question$replies === void 0 ? void 0 : _question$replies.length) || 0,\n                className: \"reply-badge\",\n                showZero: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 258,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"question-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"question-title\",\n                children: question.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"question-body\",\n                children: question.body\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"replies-section\",\n              children: ((_question$replies2 = question.replies) === null || _question$replies2 === void 0 ? void 0 : _question$replies2.length) > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"replies-header\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"replies-count\",\n                    children: [question.replies.length, \" \", question.replies.length === 1 ? 'Reply' : 'Replies']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 45\n                }, this), question.replies.map(reply => {\n                  var _reply$user, _reply$user2;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"modern-reply\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"reply-header\",\n                      children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                        src: (_reply$user = reply.user) !== null && _reply$user !== void 0 && _reply$user.profileImage ? reply.user.profileImage : image,\n                        alt: \"profile\",\n                        size: 32,\n                        icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 287,\n                          columnNumber: 67\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 283,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"reply-user-info\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"reply-username\",\n                          children: ((_reply$user2 = reply.user) === null || _reply$user2 === void 0 ? void 0 : _reply$user2.name) || 'Anonymous'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 290,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"reply-datetime\",\n                          children: formatDateTime(reply.createdAt)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 291,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 289,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 282,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"reply-content\",\n                      children: reply.text\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 296,\n                      columnNumber: 53\n                    }, this)]\n                  }, reply._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 49\n                  }, this);\n                })]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"no-replies\",\n                children: \"No replies yet. Be the first to reply!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"question-actions\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                icon: /*#__PURE__*/_jsxDEV(MessageOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 47\n                }, this),\n                onClick: () => handleReply(question._id),\n                className: \"action-btn reply-btn\",\n                children: \"Add Reply\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 37\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              ref: replyRefs[question._id],\n              children: replyQuestionId === question._id && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"reply-form-section\",\n                children: /*#__PURE__*/_jsxDEV(Form, {\n                  form: form,\n                  onFinish: handleReplySubmit,\n                  layout: \"vertical\",\n                  children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                    name: \"text\",\n                    label: \"Your Reply\",\n                    rules: [{\n                      required: true,\n                      message: 'Please enter your reply'\n                    }],\n                    children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n                      rows: 3,\n                      placeholder: \"Write your reply...\",\n                      className: \"modern-textarea\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                    className: \"reply-actions\",\n                    children: [/*#__PURE__*/_jsxDEV(Button, {\n                      type: \"primary\",\n                      htmlType: \"submit\",\n                      className: \"submit-reply-btn\",\n                      children: \"Submit Reply\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 333,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(Button, {\n                      onClick: () => setReplyQuestionId(null),\n                      className: \"cancel-reply-btn\",\n                      children: \"Cancel\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 45\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 33\n            }, this)]\n          }, question._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 29\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 21\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"forum-pagination\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-info\",\n          children: [\"Showing \", startItem, \"-\", endItem, \" of \", totalQuestions, \" questions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pagination-controls\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handlePageChange(currentPage - 1),\n            disabled: currentPage === 1,\n            className: \"pagination-btn\",\n            children: \"Previous\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 33\n          }, this), Array.from({\n            length: totalPages\n          }, (_, i) => i + 1).map(page => /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handlePageChange(page),\n            className: `pagination-btn ${currentPage === page ? 'active' : ''}`,\n            children: page\n          }, page, false, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 37\n          }, this)), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => handlePageChange(currentPage + 1),\n            disabled: currentPage === totalPages,\n            className: \"pagination-btn\",\n            children: \"Next\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 350,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 174,\n      columnNumber: 17\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 172,\n    columnNumber: 9\n  }, this);\n};\n_s(Forum, \"PLowEnnynYfT2Iq7gUlOJ/goKSg=\", false, function () {\n  return [Form.useForm, useDispatch];\n});\n_c = Forum;\nexport default Forum;\nvar _c;\n$RefreshReg$(_c, \"Forum\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "getUserInfo", "message", "<PERSON><PERSON>", "Input", "Form", "Avatar", "Badge", "useDispatch", "HideLoading", "ShowLoading", "addQuestion", "addReply", "getAllQuestions", "image", "PlusOutlined", "MessageOutlined", "UserOutlined", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Forum", "_s", "isAdmin", "setIsAdmin", "userData", "setUserData", "questions", "setQuestions", "askQuestionVisible", "setAskQuestionVisible", "replyQuestionId", "setReplyQuestionId", "form", "useForm", "dispatch", "replyRefs", "setReplyRefs", "currentPage", "setCurrentPage", "questionsPerPage", "totalQuestions", "setTotalQuestions", "fetchQuestions", "page", "response", "limit", "success", "sortedQuestions", "data", "sort", "a", "b", "Date", "createdAt", "_id", "total", "length", "error", "getUserData", "localStorage", "getItem", "handleAskQuestion", "values", "resetFields", "handleReply", "questionId", "handleReplySubmit", "payload", "text", "prevRefs", "createRef", "current", "scrollIntoView", "behavior", "totalPages", "Math", "ceil", "startItem", "endItem", "min", "handlePageChange", "formatDateTime", "dateString", "date", "now", "diffInHours", "toLocaleTimeString", "hour", "minute", "hour12", "toLocaleDateString", "month", "day", "year", "getFullYear", "undefined", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "icon", "onClick", "size", "onFinish", "layout", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "TextArea", "rows", "htmlType", "map", "question", "_question$user", "_question$user2", "_question$replies", "_question$replies2", "src", "user", "profileImage", "alt", "count", "replies", "showZero", "title", "body", "reply", "_reply$user", "_reply$user2", "ref", "disabled", "Array", "from", "_", "i", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/common/Forum/index.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from \"react\";\r\nimport './index.css';\r\nimport { getUserInfo } from \"../../../apicalls/users\";\r\nimport { message, Button, Input, Form, Avatar, Badge } from \"antd\";\r\nimport { useDispatch } from \"react-redux\";\r\nimport { HideLoading, ShowLoading } from \"../../../redux/loaderSlice\";\r\nimport { addQuestion, addReply, getAllQuestions } from \"../../../apicalls/forum\";\r\nimport image from '../../../assets/person.png';\r\nimport { PlusOutlined, MessageOutlined, UserOutlined } from '@ant-design/icons';\r\n\r\nconst Forum = () => {\r\n    const [isAdmin, setIsAdmin] = useState(false);\r\n    const [userData, setUserData] = useState('');\r\n    const [questions, setQuestions] = useState([]);\r\n    const [askQuestionVisible, setAskQuestionVisible] = useState(false);\r\n    const [replyQuestionId, setReplyQuestionId] = useState(null);\r\n    const [form] = Form.useForm();\r\n    const dispatch = useDispatch();\r\n    const [replyRefs, setReplyRefs] = useState({});\r\n\r\n    // Pagination states\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [questionsPerPage] = useState(5);\r\n    const [totalQuestions, setTotalQuestions] = useState(0);\r\n\r\n    const fetchQuestions = async (page = currentPage) => {\r\n        try {\r\n            dispatch(ShowLoading());\r\n            const response = await getAllQuestions({ page, limit: questionsPerPage });\r\n            if (response.success) {\r\n                // Sort by creation date (newest first) instead of reversing\r\n                const sortedQuestions = response.data.sort((a, b) =>\r\n                    new Date(b.createdAt || b._id) - new Date(a.createdAt || a._id)\r\n                );\r\n                setQuestions(sortedQuestions);\r\n                setTotalQuestions(response.total || response.totalQuestions || response.data.length);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        } finally {\r\n            dispatch(HideLoading());\r\n        }\r\n    };\r\n\r\n    const getUserData = async () => {\r\n        try {\r\n            const response = await getUserInfo();\r\n            if (response.success) {\r\n                if (response.data.isAdmin) {\r\n                    setIsAdmin(true);\r\n                } else {\r\n                    setIsAdmin(false);\r\n                    setUserData(response.data);\r\n                    await fetchQuestions();\r\n                }\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n        dispatch(HideLoading());\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (localStorage.getItem(\"token\")) {\r\n            dispatch(ShowLoading());\r\n            getUserData();\r\n        }\r\n    }, []);\r\n\r\n\r\n\r\n    const handleAskQuestion = async (values) => {\r\n        try {\r\n            dispatch(ShowLoading());\r\n            const response = await addQuestion(values);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setAskQuestionVisible(false);\r\n                form.resetFields();\r\n                // Reset to first page to see the new question\r\n                setCurrentPage(1);\r\n                await fetchQuestions(1);\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        } finally {\r\n            dispatch(HideLoading());\r\n        }\r\n    };\r\n\r\n    const handleReply = (questionId) => {\r\n        setReplyQuestionId(questionId);\r\n    };\r\n\r\n    const handleReplySubmit = async (values) => {\r\n        try {\r\n            const payload = {\r\n                questionId: replyQuestionId,\r\n                text: values.text\r\n            };\r\n            const response = await addReply(payload);\r\n            if (response.success) {\r\n                message.success(response.message);\r\n                setReplyQuestionId(null);\r\n                form.resetFields();\r\n                await fetchQuestions();\r\n            } else {\r\n                message.error(response.message);\r\n            }\r\n        } catch (error) {\r\n            message.error(error.message);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && !replyRefs[replyQuestionId]) {\r\n            setReplyRefs((prevRefs) => ({\r\n                ...prevRefs,\r\n                [replyQuestionId]: React.createRef(),\r\n            }));\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    useEffect(() => {\r\n        if (replyQuestionId && replyRefs[replyQuestionId]) {\r\n            replyRefs[replyQuestionId].current.scrollIntoView({ behavior: 'smooth' });\r\n        }\r\n    }, [replyQuestionId, replyRefs]);\r\n\r\n    // Pagination calculations - use all questions since we're fetching by page\r\n    const totalPages = Math.ceil(totalQuestions / questionsPerPage);\r\n    const startItem = (currentPage - 1) * questionsPerPage + 1;\r\n    const endItem = Math.min(currentPage * questionsPerPage, totalQuestions);\r\n\r\n    const handlePageChange = (page) => {\r\n        setCurrentPage(page);\r\n        fetchQuestions(page);\r\n    };\r\n\r\n    // Format date and time\r\n    const formatDateTime = (dateString) => {\r\n        if (!dateString) return 'Just now';\r\n\r\n        const date = new Date(dateString);\r\n        const now = new Date();\r\n        const diffInHours = (now - date) / (1000 * 60 * 60);\r\n\r\n        if (diffInHours < 24) {\r\n            // Show time if less than 24 hours\r\n            return date.toLocaleTimeString('en-US', {\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n                hour12: true\r\n            });\r\n        } else {\r\n            // Show date if more than 24 hours\r\n            return date.toLocaleDateString('en-US', {\r\n                month: 'short',\r\n                day: 'numeric',\r\n                year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined\r\n            });\r\n        }\r\n    };\r\n\r\n    return (\r\n        <div>\r\n            {!isAdmin && (\r\n                <div className=\"modern-forum\">\r\n                    {/* Header Section */}\r\n                    <div className=\"forum-header\">\r\n                        <div className=\"forum-header-content\">\r\n                            <h1 className=\"forum-title\">Community Forum</h1>\r\n                            <p className=\"forum-description\">\r\n                                Connect with fellow learners, ask questions, and share knowledge.\r\n                                Join our vibrant community discussion!\r\n                            </p>\r\n                            {/* Ask Question Button in Header */}\r\n                            <div className=\"header-ask-btn\">\r\n                                <Button\r\n                                    type=\"primary\"\r\n                                    icon={<PlusOutlined />}\r\n                                    onClick={() => setAskQuestionVisible(true)}\r\n                                    className=\"ask-question-header\"\r\n                                    size=\"large\"\r\n                                >\r\n                                    Ask Question\r\n                                </Button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    {/* Ask Question Form */}\r\n                    {askQuestionVisible && (\r\n                        <div className=\"ask-question-modal\">\r\n                            <div className=\"ask-question-form\">\r\n                                <h3 className=\"form-title\">Ask a Question</h3>\r\n                                <Form form={form} onFinish={handleAskQuestion} layout=\"vertical\">\r\n                                    <Form.Item\r\n                                        name=\"title\"\r\n                                        label=\"Question Title\"\r\n                                        rules={[{ required: true, message: 'Please enter the title' }]}\r\n                                    >\r\n                                        <Input\r\n                                            placeholder=\"What's your question about?\"\r\n                                            className=\"modern-input\"\r\n                                        />\r\n                                    </Form.Item>\r\n                                    <Form.Item\r\n                                        name=\"body\"\r\n                                        label=\"Question Details\"\r\n                                        rules={[{ required: true, message: 'Please enter the question details' }]}\r\n                                    >\r\n                                        <Input.TextArea\r\n                                            rows={4}\r\n                                            placeholder=\"Provide more details about your question...\"\r\n                                            className=\"modern-textarea\"\r\n                                        />\r\n                                    </Form.Item>\r\n                                    <Form.Item className=\"form-actions\">\r\n                                        <Button type=\"primary\" htmlType=\"submit\" className=\"submit-btn\">\r\n                                            Post Question\r\n                                        </Button>\r\n                                        <Button onClick={() => setAskQuestionVisible(false)} className=\"cancel-btn\">\r\n                                            Cancel\r\n                                        </Button>\r\n                                    </Form.Item>\r\n                                </Form>\r\n                            </div>\r\n                        </div>\r\n                    )}\r\n\r\n                    {/* Questions List */}\r\n                    <div className=\"questions-container\">\r\n                        {questions.map((question) => (\r\n                            <div key={question._id} className=\"modern-question-card\">\r\n                                {/* Question Header */}\r\n                                <div className=\"question-header\">\r\n                                    <div className=\"user-info\">\r\n                                        <Avatar\r\n                                            src={question.user?.profileImage ? question.user.profileImage : image}\r\n                                            alt=\"profile\"\r\n                                            size={48}\r\n                                            icon={<UserOutlined />}\r\n                                        />\r\n                                        <div className=\"user-details\">\r\n                                            <span className=\"username\">{question.user?.name || 'Anonymous'}</span>\r\n                                            <span className=\"question-datetime\">\r\n                                                {formatDateTime(question.createdAt)}\r\n                                            </span>\r\n                                        </div>\r\n                                    </div>\r\n                                    <Badge\r\n                                        count={question.replies?.length || 0}\r\n                                        className=\"reply-badge\"\r\n                                        showZero\r\n                                    />\r\n                                </div>\r\n\r\n                                {/* Question Content */}\r\n                                <div className=\"question-content\">\r\n                                    <h3 className=\"question-title\">{question.title}</h3>\r\n                                    <p className=\"question-body\">{question.body}</p>\r\n                                </div>\r\n\r\n                                {/* Replies Section - Always Visible */}\r\n                                <div className=\"replies-section\">\r\n                                    {question.replies?.length > 0 ? (\r\n                                        <>\r\n                                            <div className=\"replies-header\">\r\n                                                <span className=\"replies-count\">\r\n                                                    {question.replies.length} {question.replies.length === 1 ? 'Reply' : 'Replies'}\r\n                                                </span>\r\n                                            </div>\r\n                                            {question.replies.map((reply) => (\r\n                                                <div key={reply._id} className=\"modern-reply\">\r\n                                                    <div className=\"reply-header\">\r\n                                                        <Avatar\r\n                                                            src={reply.user?.profileImage ? reply.user.profileImage : image}\r\n                                                            alt=\"profile\"\r\n                                                            size={32}\r\n                                                            icon={<UserOutlined />}\r\n                                                        />\r\n                                                        <div className=\"reply-user-info\">\r\n                                                            <span className=\"reply-username\">{reply.user?.name || 'Anonymous'}</span>\r\n                                                            <span className=\"reply-datetime\">\r\n                                                                {formatDateTime(reply.createdAt)}\r\n                                                            </span>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div className=\"reply-content\">{reply.text}</div>\r\n                                                </div>\r\n                                            ))}\r\n                                        </>\r\n                                    ) : (\r\n                                        <p className=\"no-replies\">No replies yet. Be the first to reply!</p>\r\n                                    )}\r\n                                </div>\r\n\r\n                                {/* Action Buttons */}\r\n                                <div className=\"question-actions\">\r\n                                    <Button\r\n                                        icon={<MessageOutlined />}\r\n                                        onClick={() => handleReply(question._id)}\r\n                                        className=\"action-btn reply-btn\"\r\n                                    >\r\n                                        Add Reply\r\n                                    </Button>\r\n                                </div>\r\n\r\n                                {/* Reply Form */}\r\n                                <div ref={replyRefs[question._id]}>\r\n                                    {replyQuestionId === question._id && (\r\n                                        <div className=\"reply-form-section\">\r\n                                            <Form form={form} onFinish={handleReplySubmit} layout=\"vertical\">\r\n                                                <Form.Item\r\n                                                    name=\"text\"\r\n                                                    label=\"Your Reply\"\r\n                                                    rules={[{ required: true, message: 'Please enter your reply' }]}\r\n                                                >\r\n                                                    <Input.TextArea\r\n                                                        rows={3}\r\n                                                        placeholder=\"Write your reply...\"\r\n                                                        className=\"modern-textarea\"\r\n                                                    />\r\n                                                </Form.Item>\r\n                                                <Form.Item className=\"reply-actions\">\r\n                                                    <Button type=\"primary\" htmlType=\"submit\" className=\"submit-reply-btn\">\r\n                                                        Submit Reply\r\n                                                    </Button>\r\n                                                    <Button onClick={() => setReplyQuestionId(null)} className=\"cancel-reply-btn\">\r\n                                                        Cancel\r\n                                                    </Button>\r\n                                                </Form.Item>\r\n                                            </Form>\r\n                                        </div>\r\n                                    )}\r\n                                </div>\r\n                            </div>\r\n                        ))}\r\n                    </div>\r\n\r\n                    {/* Pagination */}\r\n                    {totalPages > 1 && (\r\n                        <div className=\"forum-pagination\">\r\n                            <div className=\"pagination-info\">\r\n                                Showing {startItem}-{endItem} of {totalQuestions} questions\r\n                            </div>\r\n                            <div className=\"pagination-controls\">\r\n                                <Button\r\n                                    onClick={() => handlePageChange(currentPage - 1)}\r\n                                    disabled={currentPage === 1}\r\n                                    className=\"pagination-btn\"\r\n                                >\r\n                                    Previous\r\n                                </Button>\r\n\r\n                                {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (\r\n                                    <Button\r\n                                        key={page}\r\n                                        onClick={() => handlePageChange(page)}\r\n                                        className={`pagination-btn ${currentPage === page ? 'active' : ''}`}\r\n                                    >\r\n                                        {page}\r\n                                    </Button>\r\n                                ))}\r\n\r\n                                <Button\r\n                                    onClick={() => handlePageChange(currentPage + 1)}\r\n                                    disabled={currentPage === totalPages}\r\n                                    className=\"pagination-btn\"\r\n                                >\r\n                                    Next\r\n                                </Button>\r\n                            </div>\r\n                        </div>\r\n                    )}\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}\r\n\r\nexport default Forum;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,aAAa;AACpB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,QAAQ,MAAM;AAClE,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,WAAW,EAAEC,QAAQ,EAAEC,eAAe,QAAQ,yBAAyB;AAChF,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,SAASC,YAAY,EAAEC,eAAe,EAAEC,YAAY,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAEhF,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACkC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoC,IAAI,CAAC,GAAG7B,IAAI,CAAC8B,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE9C;EACA,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC2C,gBAAgB,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EACtC,MAAM,CAAC4C,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EAEvD,MAAM8C,cAAc,GAAG,MAAAA,CAAOC,IAAI,GAAGN,WAAW,KAAK;IACjD,IAAI;MACAH,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMoC,QAAQ,GAAG,MAAMjC,eAAe,CAAC;QAAEgC,IAAI;QAAEE,KAAK,EAAEN;MAAiB,CAAC,CAAC;MACzE,IAAIK,QAAQ,CAACE,OAAO,EAAE;QAClB;QACA,MAAMC,eAAe,GAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAC5C,IAAIC,IAAI,CAACD,CAAC,CAACE,SAAS,IAAIF,CAAC,CAACG,GAAG,CAAC,GAAG,IAAIF,IAAI,CAACF,CAAC,CAACG,SAAS,IAAIH,CAAC,CAACI,GAAG,CAClE,CAAC;QACD3B,YAAY,CAACoB,eAAe,CAAC;QAC7BN,iBAAiB,CAACG,QAAQ,CAACW,KAAK,IAAIX,QAAQ,CAACJ,cAAc,IAAII,QAAQ,CAACI,IAAI,CAACQ,MAAM,CAAC;MACxF,CAAC,MAAM;QACHxD,OAAO,CAACyD,KAAK,CAACb,QAAQ,CAAC5C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACZzD,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;IAChC,CAAC,SAAS;MACNkC,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMmD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACA,MAAMd,QAAQ,GAAG,MAAM7C,WAAW,CAAC,CAAC;MACpC,IAAI6C,QAAQ,CAACE,OAAO,EAAE;QAClB,IAAIF,QAAQ,CAACI,IAAI,CAAC1B,OAAO,EAAE;UACvBC,UAAU,CAAC,IAAI,CAAC;QACpB,CAAC,MAAM;UACHA,UAAU,CAAC,KAAK,CAAC;UACjBE,WAAW,CAACmB,QAAQ,CAACI,IAAI,CAAC;UAC1B,MAAMN,cAAc,CAAC,CAAC;QAC1B;MACJ,CAAC,MAAM;QACH1C,OAAO,CAACyD,KAAK,CAACb,QAAQ,CAAC5C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACZzD,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;IAChC;IACAkC,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;EAC3B,CAAC;EAEDV,SAAS,CAAC,MAAM;IACZ,IAAI8D,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,EAAE;MAC/B1B,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACvBkD,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,EAAE,CAAC;EAIN,MAAMG,iBAAiB,GAAG,MAAOC,MAAM,IAAK;IACxC,IAAI;MACA5B,QAAQ,CAAC1B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMoC,QAAQ,GAAG,MAAMnC,WAAW,CAACqD,MAAM,CAAC;MAC1C,IAAIlB,QAAQ,CAACE,OAAO,EAAE;QAClB9C,OAAO,CAAC8C,OAAO,CAACF,QAAQ,CAAC5C,OAAO,CAAC;QACjC6B,qBAAqB,CAAC,KAAK,CAAC;QAC5BG,IAAI,CAAC+B,WAAW,CAAC,CAAC;QAClB;QACAzB,cAAc,CAAC,CAAC,CAAC;QACjB,MAAMI,cAAc,CAAC,CAAC,CAAC;MAC3B,CAAC,MAAM;QACH1C,OAAO,CAACyD,KAAK,CAACb,QAAQ,CAAC5C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACZzD,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;IAChC,CAAC,SAAS;MACNkC,QAAQ,CAAC3B,WAAW,CAAC,CAAC,CAAC;IAC3B;EACJ,CAAC;EAED,MAAMyD,WAAW,GAAIC,UAAU,IAAK;IAChClC,kBAAkB,CAACkC,UAAU,CAAC;EAClC,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAOJ,MAAM,IAAK;IACxC,IAAI;MACA,MAAMK,OAAO,GAAG;QACZF,UAAU,EAAEnC,eAAe;QAC3BsC,IAAI,EAAEN,MAAM,CAACM;MACjB,CAAC;MACD,MAAMxB,QAAQ,GAAG,MAAMlC,QAAQ,CAACyD,OAAO,CAAC;MACxC,IAAIvB,QAAQ,CAACE,OAAO,EAAE;QAClB9C,OAAO,CAAC8C,OAAO,CAACF,QAAQ,CAAC5C,OAAO,CAAC;QACjC+B,kBAAkB,CAAC,IAAI,CAAC;QACxBC,IAAI,CAAC+B,WAAW,CAAC,CAAC;QAClB,MAAMrB,cAAc,CAAC,CAAC;MAC1B,CAAC,MAAM;QACH1C,OAAO,CAACyD,KAAK,CAACb,QAAQ,CAAC5C,OAAO,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOyD,KAAK,EAAE;MACZzD,OAAO,CAACyD,KAAK,CAACA,KAAK,CAACzD,OAAO,CAAC;IAChC;EACJ,CAAC;EAEDH,SAAS,CAAC,MAAM;IACZ,IAAIiC,eAAe,IAAI,CAACK,SAAS,CAACL,eAAe,CAAC,EAAE;MAChDM,YAAY,CAAEiC,QAAQ,KAAM;QACxB,GAAGA,QAAQ;QACX,CAACvC,eAAe,gBAAGnC,KAAK,CAAC2E,SAAS,CAAC;MACvC,CAAC,CAAC,CAAC;IACP;EACJ,CAAC,EAAE,CAACxC,eAAe,EAAEK,SAAS,CAAC,CAAC;EAEhCtC,SAAS,CAAC,MAAM;IACZ,IAAIiC,eAAe,IAAIK,SAAS,CAACL,eAAe,CAAC,EAAE;MAC/CK,SAAS,CAACL,eAAe,CAAC,CAACyC,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC7E;EACJ,CAAC,EAAE,CAAC3C,eAAe,EAAEK,SAAS,CAAC,CAAC;;EAEhC;EACA,MAAMuC,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACpC,cAAc,GAAGD,gBAAgB,CAAC;EAC/D,MAAMsC,SAAS,GAAG,CAACxC,WAAW,GAAG,CAAC,IAAIE,gBAAgB,GAAG,CAAC;EAC1D,MAAMuC,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC1C,WAAW,GAAGE,gBAAgB,EAAEC,cAAc,CAAC;EAExE,MAAMwC,gBAAgB,GAAIrC,IAAI,IAAK;IAC/BL,cAAc,CAACK,IAAI,CAAC;IACpBD,cAAc,CAACC,IAAI,CAAC;EACxB,CAAC;;EAED;EACA,MAAMsC,cAAc,GAAIC,UAAU,IAAK;IACnC,IAAI,CAACA,UAAU,EAAE,OAAO,UAAU;IAElC,MAAMC,IAAI,GAAG,IAAI/B,IAAI,CAAC8B,UAAU,CAAC;IACjC,MAAME,GAAG,GAAG,IAAIhC,IAAI,CAAC,CAAC;IACtB,MAAMiC,WAAW,GAAG,CAACD,GAAG,GAAGD,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEnD,IAAIE,WAAW,GAAG,EAAE,EAAE;MAClB;MACA,OAAOF,IAAI,CAACG,kBAAkB,CAAC,OAAO,EAAE;QACpCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACZ,CAAC,CAAC;IACN,CAAC,MAAM;MACH;MACA,OAAON,IAAI,CAACO,kBAAkB,CAAC,OAAO,EAAE;QACpCC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAEV,IAAI,CAACW,WAAW,CAAC,CAAC,KAAKV,GAAG,CAACU,WAAW,CAAC,CAAC,GAAG,SAAS,GAAGC;MACjE,CAAC,CAAC;IACN;EACJ,CAAC;EAED,oBACI9E,OAAA;IAAA+E,QAAA,EACK,CAAC1E,OAAO,iBACLL,OAAA;MAAKgF,SAAS,EAAC,cAAc;MAAAD,QAAA,gBAEzB/E,OAAA;QAAKgF,SAAS,EAAC,cAAc;QAAAD,QAAA,eACzB/E,OAAA;UAAKgF,SAAS,EAAC,sBAAsB;UAAAD,QAAA,gBACjC/E,OAAA;YAAIgF,SAAS,EAAC,aAAa;YAAAD,QAAA,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChDpF,OAAA;YAAGgF,SAAS,EAAC,mBAAmB;YAAAD,QAAA,EAAC;UAGjC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJpF,OAAA;YAAKgF,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC3B/E,OAAA,CAAChB,MAAM;cACHqG,IAAI,EAAC,SAAS;cACdC,IAAI,eAAEtF,OAAA,CAACJ,YAAY;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBG,OAAO,EAAEA,CAAA,KAAM3E,qBAAqB,CAAC,IAAI,CAAE;cAC3CoE,SAAS,EAAC,qBAAqB;cAC/BQ,IAAI,EAAC,OAAO;cAAAT,QAAA,EACf;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAGLzE,kBAAkB,iBACfX,OAAA;QAAKgF,SAAS,EAAC,oBAAoB;QAAAD,QAAA,eAC/B/E,OAAA;UAAKgF,SAAS,EAAC,mBAAmB;UAAAD,QAAA,gBAC9B/E,OAAA;YAAIgF,SAAS,EAAC,YAAY;YAAAD,QAAA,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9CpF,OAAA,CAACd,IAAI;YAAC6B,IAAI,EAAEA,IAAK;YAAC0E,QAAQ,EAAE7C,iBAAkB;YAAC8C,MAAM,EAAC,UAAU;YAAAX,QAAA,gBAC5D/E,OAAA,CAACd,IAAI,CAACyG,IAAI;cACNC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,gBAAgB;cACtBC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhH,OAAO,EAAE;cAAyB,CAAC,CAAE;cAAAgG,QAAA,eAE/D/E,OAAA,CAACf,KAAK;gBACF+G,WAAW,EAAC,6BAA6B;gBACzChB,SAAS,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACZpF,OAAA,CAACd,IAAI,CAACyG,IAAI;cACNC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,kBAAkB;cACxBC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhH,OAAO,EAAE;cAAoC,CAAC,CAAE;cAAAgG,QAAA,eAE1E/E,OAAA,CAACf,KAAK,CAACgH,QAAQ;gBACXC,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC,6CAA6C;gBACzDhB,SAAS,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACZpF,OAAA,CAACd,IAAI,CAACyG,IAAI;cAACX,SAAS,EAAC,cAAc;cAAAD,QAAA,gBAC/B/E,OAAA,CAAChB,MAAM;gBAACqG,IAAI,EAAC,SAAS;gBAACc,QAAQ,EAAC,QAAQ;gBAACnB,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAEhE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTpF,OAAA,CAAChB,MAAM;gBAACuG,OAAO,EAAEA,CAAA,KAAM3E,qBAAqB,CAAC,KAAK,CAAE;gBAACoE,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAE5E;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR,eAGDpF,OAAA;QAAKgF,SAAS,EAAC,qBAAqB;QAAAD,QAAA,EAC/BtE,SAAS,CAAC2F,GAAG,CAAEC,QAAQ;UAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,iBAAA,EAAAC,kBAAA;UAAA,oBACpBzG,OAAA;YAAwBgF,SAAS,EAAC,sBAAsB;YAAAD,QAAA,gBAEpD/E,OAAA;cAAKgF,SAAS,EAAC,iBAAiB;cAAAD,QAAA,gBAC5B/E,OAAA;gBAAKgF,SAAS,EAAC,WAAW;gBAAAD,QAAA,gBACtB/E,OAAA,CAACb,MAAM;kBACHuH,GAAG,EAAE,CAAAJ,cAAA,GAAAD,QAAQ,CAACM,IAAI,cAAAL,cAAA,eAAbA,cAAA,CAAeM,YAAY,GAAGP,QAAQ,CAACM,IAAI,CAACC,YAAY,GAAGjH,KAAM;kBACtEkH,GAAG,EAAC,SAAS;kBACbrB,IAAI,EAAE,EAAG;kBACTF,IAAI,eAAEtF,OAAA,CAACF,YAAY;oBAAAmF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAE;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACFpF,OAAA;kBAAKgF,SAAS,EAAC,cAAc;kBAAAD,QAAA,gBACzB/E,OAAA;oBAAMgF,SAAS,EAAC,UAAU;oBAAAD,QAAA,EAAE,EAAAwB,eAAA,GAAAF,QAAQ,CAACM,IAAI,cAAAJ,eAAA,uBAAbA,eAAA,CAAeX,IAAI,KAAI;kBAAW;oBAAAX,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACtEpF,OAAA;oBAAMgF,SAAS,EAAC,mBAAmB;oBAAAD,QAAA,EAC9Bf,cAAc,CAACqC,QAAQ,CAACjE,SAAS;kBAAC;oBAAA6C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNpF,OAAA,CAACZ,KAAK;gBACF0H,KAAK,EAAE,EAAAN,iBAAA,GAAAH,QAAQ,CAACU,OAAO,cAAAP,iBAAA,uBAAhBA,iBAAA,CAAkBjE,MAAM,KAAI,CAAE;gBACrCyC,SAAS,EAAC,aAAa;gBACvBgC,QAAQ;cAAA;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNpF,OAAA;cAAKgF,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC7B/E,OAAA;gBAAIgF,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,EAAEsB,QAAQ,CAACY;cAAK;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpDpF,OAAA;gBAAGgF,SAAS,EAAC,eAAe;gBAAAD,QAAA,EAAEsB,QAAQ,CAACa;cAAI;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC,eAGNpF,OAAA;cAAKgF,SAAS,EAAC,iBAAiB;cAAAD,QAAA,EAC3B,EAAA0B,kBAAA,GAAAJ,QAAQ,CAACU,OAAO,cAAAN,kBAAA,uBAAhBA,kBAAA,CAAkBlE,MAAM,IAAG,CAAC,gBACzBvC,OAAA,CAAAE,SAAA;gBAAA6E,QAAA,gBACI/E,OAAA;kBAAKgF,SAAS,EAAC,gBAAgB;kBAAAD,QAAA,eAC3B/E,OAAA;oBAAMgF,SAAS,EAAC,eAAe;oBAAAD,QAAA,GAC1BsB,QAAQ,CAACU,OAAO,CAACxE,MAAM,EAAC,GAAC,EAAC8D,QAAQ,CAACU,OAAO,CAACxE,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,SAAS;kBAAA;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EACLiB,QAAQ,CAACU,OAAO,CAACX,GAAG,CAAEe,KAAK;kBAAA,IAAAC,WAAA,EAAAC,YAAA;kBAAA,oBACxBrH,OAAA;oBAAqBgF,SAAS,EAAC,cAAc;oBAAAD,QAAA,gBACzC/E,OAAA;sBAAKgF,SAAS,EAAC,cAAc;sBAAAD,QAAA,gBACzB/E,OAAA,CAACb,MAAM;wBACHuH,GAAG,EAAE,CAAAU,WAAA,GAAAD,KAAK,CAACR,IAAI,cAAAS,WAAA,eAAVA,WAAA,CAAYR,YAAY,GAAGO,KAAK,CAACR,IAAI,CAACC,YAAY,GAAGjH,KAAM;wBAChEkH,GAAG,EAAC,SAAS;wBACbrB,IAAI,EAAE,EAAG;wBACTF,IAAI,eAAEtF,OAAA,CAACF,YAAY;0BAAAmF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAE;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC,eACFpF,OAAA;wBAAKgF,SAAS,EAAC,iBAAiB;wBAAAD,QAAA,gBAC5B/E,OAAA;0BAAMgF,SAAS,EAAC,gBAAgB;0BAAAD,QAAA,EAAE,EAAAsC,YAAA,GAAAF,KAAK,CAACR,IAAI,cAAAU,YAAA,uBAAVA,YAAA,CAAYzB,IAAI,KAAI;wBAAW;0BAAAX,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,eACzEpF,OAAA;0BAAMgF,SAAS,EAAC,gBAAgB;0BAAAD,QAAA,EAC3Bf,cAAc,CAACmD,KAAK,CAAC/E,SAAS;wBAAC;0BAAA6C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC9B,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNpF,OAAA;sBAAKgF,SAAS,EAAC,eAAe;sBAAAD,QAAA,EAAEoC,KAAK,CAAChE;oBAAI;sBAAA8B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA,GAf3C+B,KAAK,CAAC9E,GAAG;oBAAA4C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAgBd,CAAC;gBAAA,CACT,CAAC;cAAA,eACJ,CAAC,gBAEHpF,OAAA;gBAAGgF,SAAS,EAAC,YAAY;gBAAAD,QAAA,EAAC;cAAsC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YACtE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGNpF,OAAA;cAAKgF,SAAS,EAAC,kBAAkB;cAAAD,QAAA,eAC7B/E,OAAA,CAAChB,MAAM;gBACHsG,IAAI,eAAEtF,OAAA,CAACH,eAAe;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC1BG,OAAO,EAAEA,CAAA,KAAMxC,WAAW,CAACsD,QAAQ,CAAChE,GAAG,CAAE;gBACzC2C,SAAS,EAAC,sBAAsB;gBAAAD,QAAA,EACnC;cAED;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eAGNpF,OAAA;cAAKsH,GAAG,EAAEpG,SAAS,CAACmF,QAAQ,CAAChE,GAAG,CAAE;cAAA0C,QAAA,EAC7BlE,eAAe,KAAKwF,QAAQ,CAAChE,GAAG,iBAC7BrC,OAAA;gBAAKgF,SAAS,EAAC,oBAAoB;gBAAAD,QAAA,eAC/B/E,OAAA,CAACd,IAAI;kBAAC6B,IAAI,EAAEA,IAAK;kBAAC0E,QAAQ,EAAExC,iBAAkB;kBAACyC,MAAM,EAAC,UAAU;kBAAAX,QAAA,gBAC5D/E,OAAA,CAACd,IAAI,CAACyG,IAAI;oBACNC,IAAI,EAAC,MAAM;oBACXC,KAAK,EAAC,YAAY;oBAClBC,KAAK,EAAE,CAAC;sBAAEC,QAAQ,EAAE,IAAI;sBAAEhH,OAAO,EAAE;oBAA0B,CAAC,CAAE;oBAAAgG,QAAA,eAEhE/E,OAAA,CAACf,KAAK,CAACgH,QAAQ;sBACXC,IAAI,EAAE,CAAE;sBACRF,WAAW,EAAC,qBAAqB;sBACjChB,SAAS,EAAC;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK,CAAC,eACZpF,OAAA,CAACd,IAAI,CAACyG,IAAI;oBAACX,SAAS,EAAC,eAAe;oBAAAD,QAAA,gBAChC/E,OAAA,CAAChB,MAAM;sBAACqG,IAAI,EAAC,SAAS;sBAACc,QAAQ,EAAC,QAAQ;sBAACnB,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAEtE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTpF,OAAA,CAAChB,MAAM;sBAACuG,OAAO,EAAEA,CAAA,KAAMzE,kBAAkB,CAAC,IAAI,CAAE;sBAACkE,SAAS,EAAC,kBAAkB;sBAAAD,QAAA,EAAC;oBAE9E;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YACR;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA,GAtGAiB,QAAQ,CAAChE,GAAG;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuGjB,CAAC;QAAA,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,EAGL3B,UAAU,GAAG,CAAC,iBACXzD,OAAA;QAAKgF,SAAS,EAAC,kBAAkB;QAAAD,QAAA,gBAC7B/E,OAAA;UAAKgF,SAAS,EAAC,iBAAiB;UAAAD,QAAA,GAAC,UACrB,EAACnB,SAAS,EAAC,GAAC,EAACC,OAAO,EAAC,MAAI,EAACtC,cAAc,EAAC,YACrD;QAAA;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNpF,OAAA;UAAKgF,SAAS,EAAC,qBAAqB;UAAAD,QAAA,gBAChC/E,OAAA,CAAChB,MAAM;YACHuG,OAAO,EAAEA,CAAA,KAAMxB,gBAAgB,CAAC3C,WAAW,GAAG,CAAC,CAAE;YACjDmG,QAAQ,EAAEnG,WAAW,KAAK,CAAE;YAC5B4D,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAC7B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,EAERoC,KAAK,CAACC,IAAI,CAAC;YAAElF,MAAM,EAAEkB;UAAW,CAAC,EAAE,CAACiE,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAACvB,GAAG,CAAC1E,IAAI,iBACzD1B,OAAA,CAAChB,MAAM;YAEHuG,OAAO,EAAEA,CAAA,KAAMxB,gBAAgB,CAACrC,IAAI,CAAE;YACtCsD,SAAS,EAAG,kBAAiB5D,WAAW,KAAKM,IAAI,GAAG,QAAQ,GAAG,EAAG,EAAE;YAAAqD,QAAA,EAEnErD;UAAI,GAJAA,IAAI;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKL,CACX,CAAC,eAEFpF,OAAA,CAAChB,MAAM;YACHuG,OAAO,EAAEA,CAAA,KAAMxB,gBAAgB,CAAC3C,WAAW,GAAG,CAAC,CAAE;YACjDmG,QAAQ,EAAEnG,WAAW,KAAKqC,UAAW;YACrCuB,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAC7B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EACR;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAAhF,EAAA,CAxXKD,KAAK;EAAA,QAMQjB,IAAI,CAAC8B,OAAO,EACV3B,WAAW;AAAA;AAAAuI,EAAA,GAP1BzH,KAAK;AA0XX,eAAeA,KAAK;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}