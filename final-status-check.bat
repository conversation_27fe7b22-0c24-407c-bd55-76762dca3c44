@echo off
title BRAINWAVE STATUS CHECK - FINAL
color 0A

echo.
echo ========================================
echo    🧠 BRAINWAVE STATUS CHECK 🧠
echo ========================================
echo.

echo [1/4] 🔍 Checking Server Port 5000...
curl -s http://localhost:5000/api/health
if %errorlevel% equ 0 (
    echo ✅ SERVER IS RUNNING ON PORT 5000
) else (
    echo ❌ SERVER NOT RESPONDING ON PORT 5000
)
echo.

echo [2/4] 🔍 Checking Database Connection...
curl -s http://localhost:5000/api/test/db
if %errorlevel% equ 0 (
    echo ✅ DATABASE IS CONNECTED
) else (
    echo ❌ DATABASE CONNECTION FAILED
)
echo.

echo [3/4] 🔍 Checking Client Port 3000...
curl -s http://localhost:3000 >nul
if %errorlevel% equ 0 (
    echo ✅ CLIENT IS RUNNING ON PORT 3000
) else (
    echo ❌ CLIENT NOT RESPONDING ON PORT 3000
)
echo.

echo [4/4] 📊 Port Status...
netstat -ano | findstr ":3000 :5000"
echo.

echo ========================================
echo    🎉 BRAINWAVE STATUS COMPLETE
echo ========================================
echo.
echo 🔗 Frontend: http://localhost:3000
echo 🔗 Backend:  http://localhost:5000
echo 🔗 Health:   http://localhost:5000/api/health
echo 🔗 Database: http://localhost:5000/api/test/db
echo.
pause
